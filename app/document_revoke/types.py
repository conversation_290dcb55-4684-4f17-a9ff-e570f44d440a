from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from functools import cached_property

from app.auth.types import AuthUser, User
from app.document_revoke.enums import DocumentRevokeStatus
from app.documents.types import Document
from app.lib.database import DBRow
from app.lib.enums import SignatureFormat
from app.lib.types import DataDict
from app.signatures.enums import (
    CertificatePowerType,
    SignatureAlgo,
    SignaturePowerType,
    SignatureSource,
)


@dataclass(frozen=True)
class DocumentRevoke:
    id: str
    document_id: str
    initiator_role_id: str
    initiator_company_id: str
    reason: str
    status: DocumentRevokeStatus

    @staticmethod
    def from_row(row: DBRow) -> DocumentRevoke:
        return DocumentRevoke(
            id=row.id,
            document_id=row.document_id,
            initiator_role_id=row.initiator_role_id,
            initiator_company_id=row.initiator_company_id,
            reason=row.reason,
            status=row.status,
        )


@dataclass(frozen=True)
class GetXMLDocumentRevokeCtx:
    document: Document
    revoke: DocumentRevoke


@dataclass
class CreateDocumentRevokeCtx:
    document: Document
    reason: str
    user: User
    content: bytes | None = None


@dataclass(frozen=True)
class RejectDocumentRevokeCtx:
    revoke: DocumentRevoke
    document: Document
    reason: str
    user: User


@dataclass
class DocumentRevokeSignature:
    id: str
    revoke_id: str
    role_id: str
    user_email: str
    owner_edrpou: str
    format: SignatureFormat
    algo: SignatureAlgo
    source: str
    date_created: datetime

    key_acsk: str | None
    key_timemark: datetime | None
    key_serial_number: str | None
    key_company_fullname: str | None
    key_owner_edrpou: str | None
    key_owner_position: str | None
    key_owner_fullname: str | None
    key_power_type: SignaturePowerType | None
    key_certificate_power_type: CertificatePowerType | None
    key_exists: bool

    stamp_acsk: str | None
    stamp_timemark: datetime | None
    stamp_serial_number: str | None
    stamp_company_fullname: str | None
    stamp_owner_edrpou: str | None
    stamp_owner_position: str | None
    stamp_owner_fullname: str | None
    stamp_power_type: SignaturePowerType | None
    stamp_certificate_power_type: CertificatePowerType | None
    stamp_exists: bool

    is_internal: bool
    internal_file_name: str | None

    _row: DBRow

    is_valid: bool = True
    key_is_legal: bool = True
    stamp_is_legal: bool = True

    @staticmethod
    def from_row(row: DBRow) -> DocumentRevokeSignature:
        format_ = DocumentRevokeSignature.get_format_from_row(row)
        owner_edrpou: str = row.key_owner_edrpou or row.stamp_owner_edrpou

        return DocumentRevokeSignature(
            id=row.id,
            revoke_id=row.revoke_id,
            role_id=row.role_id,
            user_email=row.user_email,
            owner_edrpou=owner_edrpou,
            format=format_,
            key_exists=row.key_exists,
            key_acsk=row.key_acsk,
            key_timemark=row.key_timemark,
            key_serial_number=row.key_serial_number,
            key_company_fullname=row.key_company_fullname,
            key_owner_edrpou=row.key_owner_edrpou,
            key_owner_position=row.key_owner_position,
            key_owner_fullname=row.key_owner_fullname,
            stamp_power_type=row.stamp_power_type,
            key_power_type=row.key_power_type,
            key_certificate_power_type=row.key_certificate_power_type,
            stamp_certificate_power_type=row.stamp_certificate_power_type,
            stamp_exists=row.stamp_exists,
            stamp_acsk=row.stamp_acsk,
            stamp_timemark=row.stamp_timemark,
            stamp_serial_number=row.stamp_serial_number,
            stamp_company_fullname=row.stamp_company_fullname,
            stamp_owner_edrpou=row.stamp_owner_edrpou,
            stamp_owner_position=row.stamp_owner_position,
            stamp_owner_fullname=row.stamp_owner_fullname,
            is_internal=row.is_internal,
            internal_file_name=row.internal_file_name,
            algo=row.algo,
            source=row.source,
            date_created=row.date_created,
            _row=row,
        )

    @staticmethod
    def get_format_from_row(row: DBRow) -> SignatureFormat:
        """Extract signature format from raw database object"""
        raw_format = row.format
        if raw_format:
            return SignatureFormat(raw_format)

        # Before the signature format was introduced,
        # we used only the "_separated" formats
        if row.is_internal:
            return SignatureFormat.internal_separated

        return SignatureFormat.external_separated

    @property
    def is_eusign_key(self) -> bool:
        return self.format.is_eusign and bool(self.key_serial_number)

    @property
    def is_eusign_stamp(self) -> bool:
        return self.format.is_eusign and bool(self.stamp_serial_number)


@dataclass(frozen=True)
class AddSignatureDocumentRevokeDiiaRequestCtx:
    document: Document
    revoke: DocumentRevoke
    sign_algo: SignatureAlgo | None
    user: AuthUser
    source: SignatureSource


@dataclass(frozen=True)
class AddSignatureDocumentRevokeCtx:
    document: Document
    revoke: DocumentRevoke
    user: User
    signatures: list[DocumentRevokeSignature]
    involved_companies: list[str]
    data: DataDict

    @cached_property
    def is_first_sign(self) -> bool:
        """
        No signatures means that this is the first sign.
        """
        return len(self.signatures) == 0

    @cached_property
    def is_last_sign(self) -> bool:
        """
        Document revoke should be signed by all involved sides.
        """
        not_signed_by_companies = set(self.involved_companies) - {
            s.owner_edrpou for s in self.signatures
        }

        return (
            len(not_signed_by_companies) == 1
            and self.user.company_edrpou in not_signed_by_companies
        )
