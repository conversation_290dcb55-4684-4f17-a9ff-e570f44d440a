from uuid import uuid1

import pytest

from api import errors
from api.errors import AccessDenied, Code, Error, InvalidRequest
from api.public.tests.common import TEST_RECIPIENT_EDRPOU, TEST_RECIPIENT_EMAIL
from app.auth.types import User
from app.billing.db import update_billing_company_config
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.tests.utils import prepare_document_version
from app.documents.enums import DocumentSource
from app.documents.types import (
    Document,
    DocumentWithUploader,
    RecipientAggregatedData,
    RecipientsEmailsOptions,
    UpdateInfoData,
    UpdateVersionedCtx,
)
from app.documents.utils import save_hidden_emails
from app.documents.validators import (
    _validate_update_general_info,
    _validate_update_recipients,
    _validate_update_versioned,
    validate_accept_delete_request,
    validate_cancel_delete_request,
    validate_create_delete_request,
    validate_document_access,
    validate_documents_exists,
    validate_find_recipients_emails,
    validate_reject_delete_request,
    validate_self_recipient,
    validate_update_document,
)
from app.documents_required_fields.enums import Document<PERSON>ate<PERSON>yFields
from app.flags import FeatureFlags
from app.flow.types import AddFlowOptions, CreateFlowCtx
from app.flow.utils import create_flows
from app.groups.utils import add_group
from app.lib.enums import DocumentStatus, SignersSource, Source, UserRole
from app.lib.types import DataDict
from app.services import services
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_EMAIL,
    assert_exception,
    cleanup_on_teardown,
    datetime_test,
    prepare_client,
    prepare_company_data,
    prepare_delete_document_request,
    prepare_document_data,
    prepare_document_required_fields,
    prepare_public_document_categories,
    prepare_signature_data,
    prepare_signature_row,
    prepare_user_data,
)

TEST_UUID1 = 'e94cfe94-598f-407f-9bc2-28bb72cdd38c'
TEST_UUID2 = 'ec6cc286-53c0-44ad-8745-2edf888cec7e'

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_EMAIL_3 = '<EMAIL>'
TEST_EMAIL_4 = '<EMAIL>'

ROLE_ID_1 = '57bd810a-e0a6-4fdc-8ad4-5ea83df6b6bf'
ROLE_ID_2 = '2162b388-5b50-4f67-a639-2a5de857b087'
ROLE_ID_3 = '9bdb3d1b-55f3-41e3-a757-d55ed9ea41bf'
ROLE_ID_4 = '0e17882e-4d05-4f88-a2de-345afab2af8f'

USER_ID_1 = '581af44b-8351-4422-829c-06d6944f2307'
USER_ID_2 = 'cd395bfb-f99f-4915-9dd2-bca0907039d7'
USER_ID_3 = 'c1e8c58b-c59f-473c-8da1-02b82839e6e4'
USER_ID_4 = '93e4d3ac-9405-4586-be43-8896c54ce2e0'

COMPANY_EDRPOU_1 = '50000001'
COMPANY_EDRPOU_2 = '50000002'
COMPANY_EDRPOU_3 = '50000003'
COMPANY_EDRPOU_4 = '50000004'

TEST_DATETIME = datetime_test('2021-01-01 00:00:00')


@pytest.mark.parametrize(
    'recipient',
    [
        RecipientsEmailsOptions(
            edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            emails=[TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            emails=[TEST_USER_EMAIL],
        ),
    ],
)
async def test_validate_recipient(aiohttp_client, recipient):
    app, _, user = await prepare_client(aiohttp_client)

    try:
        assert validate_self_recipient(recipient, user) is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'recipient',
    [
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_USER_EMAIL],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_USER_EMAIL, TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_USER_EMAIL.upper(), TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_USER_EMAIL.capitalize(), TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
        RecipientsEmailsOptions(
            edrpou=TEST_COMPANY_EDRPOU,
            emails=[TEST_USER_EMAIL.lower(), TEST_DOCUMENT_EMAIL_RECIPIENT],
        ),
    ],
)
async def test_validate_recipient_fail(aiohttp_client, recipient):
    app, _, user = await prepare_client(aiohttp_client)

    try:
        with pytest.raises(Error):
            validate_self_recipient(recipient, user)
    finally:
        await cleanup_on_teardown(app)


async def test_validate_documents_exists_ok(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    try:
        async with app['db'].acquire() as conn:
            valid_data = await validate_documents_exists(conn, [document.id])
        assert len(valid_data), 'empty valid_data'
        assert len([document.id]) == len(valid_data), (
            f'valid_data len mismatch, {len([document.id])} != {len(valid_data)}'
        )
        assert document.id == valid_data[0].id, (
            f'doc ids mismatch, {document.id} != {valid_data[0].id}'
        )
    finally:
        await cleanup_on_teardown(app)


async def test_validate_documents_exists_fail(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    try:
        with pytest.raises(Error):
            async with app['db'].acquire() as conn:
                await validate_documents_exists(conn, {'docIds': ['1']})
    finally:
        await cleanup_on_teardown(app)


async def test_validate_delete_request(aiohttp_client, test_flags):
    app, _, user = await prepare_client(aiohttp_client)

    delete_request_creator = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou='10101010'
    )
    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': '10101010',
                'emails': ['<EMAIL>'],
            }
        ],
    )
    await prepare_signature_data(app, user, document, is_owner_signature=True)
    await prepare_signature_data(
        app,
        delete_request_creator,
        document,
        next_status_id=DocumentStatus.finished.value,
    )

    user_without_permissions = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        user_role=UserRole.user.value,
        can_delete_document=False,
    )
    async with services.db.acquire() as conn:
        with pytest.raises(InvalidRequest):
            await validate_create_delete_request(conn, user, {})
            await validate_create_delete_request(conn, user, {'document_ids': []})

    async with services.db.acquire() as conn:
        with pytest.raises(Error):
            await validate_create_delete_request(
                conn, user, {'document_ids': [str(uuid1())], 'message': 'message'}
            )

    async with services.db.acquire() as conn:
        with pytest.raises(AccessDenied):
            await validate_create_delete_request(
                conn,
                user_without_permissions,
                {'document_ids': [str(uuid1())], 'message': 'message'},
            )

    async with services.db.acquire() as conn:
        documents, message = await validate_create_delete_request(
            conn, user, {'document_ids': [document.id], 'message': 'message'}
        )
        assert documents
        assert len(documents) == 1
        assert document.id in [item.id for item in documents]
        assert message
        assert message == 'message'

    # Regular finished document can be deleted via delete request
    finished_document = await prepare_document_data(
        None,
        user,
        source=DocumentSource.vchasno,
        status_id=DocumentStatus.finished.value,
    )
    async with services.db.acquire() as conn:
        documents, message = await validate_create_delete_request(
            conn, user, {'document_ids': [finished_document.id], 'message': 'message'}
        )
        assert documents
        assert len(documents) == 1
        assert finished_document.id in [item.id for item in documents]
        assert message
        assert message == 'message'

    # do not allow creating delete request for finished edi documents
    test_flags[FeatureFlags.ENABLE_REVOKE_DOCUMENTS_FOR_EDI.name] = True
    finished_document_from_edi = await prepare_document_data(
        None,
        user,
        source=DocumentSource.edi,
        status_id=DocumentStatus.finished.value,
    )
    async with services.db.acquire() as conn:
        with pytest.raises(Error) as e:
            await validate_create_delete_request(
                conn,
                user,
                {
                    'document_ids': [finished_document_from_edi.id],
                    'message': 'message',
                },
            )
        assert e.value.code == Code.not_all_documents_in_finished_status

    # Regular finished document can be deleted via delete request
    finished_document = await prepare_document_data(
        None,
        user,
        source=DocumentSource.vchasno,
        status_id=DocumentStatus.finished.value,
    )
    async with services.db.acquire() as conn:
        documents, message = await validate_create_delete_request(
            conn, user, {'document_ids': [finished_document.id], 'message': 'message'}
        )
        assert documents
        assert len(documents) == 1
        assert finished_document.id in [item.id for item in documents]
        assert message
        assert message == 'message'

    # TODO: delete this part when document_revoke is enabled on prod and flag is removed
    # Can be deleted via delete request if document_revoke disabled
    test_flags[FeatureFlags.ENABLE_REVOKE_DOCUMENTS_FOR_EDI.name] = False
    async with services.db.acquire() as conn:
        documents, message = await validate_create_delete_request(
            conn, user, {'document_ids': [finished_document_from_edi.id], 'message': 'message'}
        )
        assert documents
        assert len(documents) == 1
        assert finished_document_from_edi.id in [item.id for item in documents]
        assert message
        assert message == 'message'

    # Regular finished document can be deleted via delete request
    async with services.db.acquire() as conn:
        documents, message = await validate_create_delete_request(
            conn, user, {'document_ids': [finished_document.id], 'message': 'message'}
        )
        assert documents
        assert len(documents) == 1
        assert finished_document.id in [item.id for item in documents]
        assert message
        assert message == 'message'


async def test_validate_accept_delete_request(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)

    delete_request_creator = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou='10101010'
    )
    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': '10101010',
                'emails': ['<EMAIL>'],
            }
        ],
    )
    await prepare_signature_data(app, user, document, is_owner_signature=True)
    await prepare_signature_data(
        app,
        delete_request_creator,
        document,
        next_status_id=DocumentStatus.finished.value,
    )
    delete_request = await prepare_delete_document_request(
        app,
        delete_request_creator,
        document.id,
        recipients_emails=[user.email],
        message='message',
        receiver_edrpou=user.company_edrpou,
    )

    user_without_permissions = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=delete_request_creator.company_edrpou,
        user_role=UserRole.user.value,
        can_delete_document=False,
    )

    async with app['db'].acquire() as conn:
        with pytest.raises(InvalidRequest):
            await validate_accept_delete_request(
                conn, delete_request_creator, {}, Source.api_internal
            )
            await validate_accept_delete_request(
                conn, delete_request_creator, {'delete_request_ids': []}, Source.api_internal
            )
            await validate_accept_delete_request(
                conn,
                delete_request_creator,
                {'delete_request_ids': [str(uuid1())]},
                Source.api_internal,
            )
            await validate_accept_delete_request(
                conn,
                delete_request_creator,
                {'delete_request_ids': [delete_request.id]},
                Source.api_internal,
            )

        with pytest.raises(AccessDenied):
            await validate_accept_delete_request(
                conn,
                user_without_permissions,
                {'delete_request_ids': [delete_request.id]},
                Source.api_internal,
            )

        requests, docs_to_delete = await validate_accept_delete_request(
            conn, user, {'delete_request_ids': [delete_request.id]}, Source.api_internal
        )
        assert requests
        assert len(requests) == 1
        result = requests[0]
        assert result.id == delete_request.id
        assert result.initiator_role_id == delete_request_creator.role_id
        assert result.status.value == 'new'


async def test_validate_cancel_delete_request(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    delete_request_creator = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou='10101010'
    )
    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': '10101010',
                'emails': ['<EMAIL>'],
            }
        ],
    )
    await prepare_signature_data(app, user, document, is_owner_signature=True)
    await prepare_signature_data(
        app,
        delete_request_creator,
        document,
        next_status_id=DocumentStatus.finished.value,
    )
    delete_request = await prepare_delete_document_request(
        app,
        delete_request_creator,
        document.id,
        recipients_emails=[user.email],
        message='message',
        receiver_edrpou=user.company_edrpou,
    )

    user_without_permissions = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=delete_request_creator.company_edrpou,
        user_role=UserRole.user.value,
        can_delete_document=False,
    )

    async with app['db'].acquire() as conn:
        with pytest.raises(InvalidRequest):
            await validate_accept_delete_request(
                conn, delete_request_creator, {}, Source.api_internal
            )
            await validate_accept_delete_request(
                conn, delete_request_creator, {'documents_ids': []}, Source.api_public
            )
            await validate_accept_delete_request(
                conn,
                delete_request_creator,
                {'delete_request_ids': [str(uuid1())]},
                Source.api_internal,
            )
            await validate_accept_delete_request(
                conn, user, {'documents_ids': [document.id]}, Source.api_public
            )
        with pytest.raises(AccessDenied):
            await validate_cancel_delete_request(
                conn, user_without_permissions, {'documents_ids': [document.id]}
            )

        validate_result = await validate_cancel_delete_request(
            conn, delete_request_creator, {'documents_ids': [document.id]}
        )
        assert validate_result
        assert len(validate_result) == 1
        result = validate_result[0]
        assert result.id == delete_request.id
        assert result.initiator_role_id == delete_request_creator.role_id
        assert result.status.value == 'new'


async def test_validate_reject_delete_request(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    delete_request_creator = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': TEST_RECIPIENT_EDRPOU,
                'emails': ['<EMAIL>'],
            }
        ],
    )
    await prepare_signature_data(app, user, document, is_owner_signature=True)
    await prepare_signature_data(
        app,
        delete_request_creator,
        document,
        next_status_id=DocumentStatus.finished.value,
    )
    delete_request = await prepare_delete_document_request(
        app,
        delete_request_creator,
        document.id,
        recipients_emails=[user.email],
        message='message',
        receiver_edrpou=user.company_edrpou,
    )

    user_without_permissions = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        user_role=UserRole.user.value,
        can_delete_document=False,
    )

    async with app['db'].acquire() as conn:
        with pytest.raises(InvalidRequest):
            await validate_reject_delete_request(conn, user, {}, Source.api_internal)
            await validate_reject_delete_request(
                conn, user, {'delete_request_ids': [str(uuid1())]}, Source.api_internal
            )

        with pytest.raises(AccessDenied):
            await validate_reject_delete_request(
                conn,
                user_without_permissions,
                {'delete_request_ids': [delete_request.id]},
                Source.api_internal,
            )

        reject_message, delete_requests = await validate_reject_delete_request(
            conn, user, {'delete_request_ids': [delete_request.id]}, Source.api_internal
        )

        assert not reject_message
        reject_message, delete_requests = await validate_reject_delete_request(
            conn,
            user,
            {
                'delete_request_ids': [delete_request.id],
                'reject_message': 'message',
            },
            Source.api_internal,
        )
        assert reject_message == 'message'


@pytest.mark.parametrize(
    'edrpous, document_id, document_recipients, missed_edrpou, error',
    [
        (
            [],
            TEST_UUID1,
            [],
            [],
            None,
        ),
        (
            [],
            TEST_UUID2,
            [],
            [],
            errors.AccessDenied,
        ),
        (
            [],
            'a1038d37-50ea-4299-adef-02558230e57b',
            [],
            [],
            errors.DoesNotExist,
        ),
        (
            [TEST_RECIPIENT_EDRPOU],
            TEST_UUID1,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_EMAIL],
                    'is_emails_hidden': False,
                }
            ],
            [],
            None,
        ),
        (
            [TEST_RECIPIENT_EDRPOU],
            TEST_UUID1,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_EMAIL],
                    'is_emails_hidden': True,
                }
            ],
            [],
            None,
        ),
        (
            [TEST_RECIPIENT_EDRPOU],
            TEST_UUID1,
            [],
            [TEST_RECIPIENT_EDRPOU],
            None,
        ),
        (
            [TEST_RECIPIENT_EDRPOU],
            TEST_UUID1,
            [
                {
                    'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                    'emails': [TEST_RECIPIENT_EMAIL],
                    'is_emails_hidden': True,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            None,
        ),
    ],
)
async def test_validate_find_recipients_emails(
    aiohttp_client,
    edrpous,
    document_id,
    document_recipients,
    missed_edrpou,
    error,
):
    app, client, user = await prepare_client(aiohttp_client)

    alien = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    await prepare_document_data(
        app,
        user,
        id=TEST_UUID1,
        document_recipients=document_recipients,
    )

    await prepare_document_data(app, alien, id=TEST_UUID2)
    async with app['db'].acquire() as conn:
        if error:
            with pytest.raises(error):
                await validate_find_recipients_emails(
                    conn=conn,
                    user=User.from_row(user),
                    data={
                        'document_id': document_id,
                        'edrpous': edrpous,
                    },
                )
            return

        options = await validate_find_recipients_emails(
            conn=conn,
            user=User.from_row(user),
            data={
                'document_id': document_id,
                'edrpous': edrpous,
            },
        )
        assert missed_edrpou == options.edrpous


@pytest.mark.parametrize(
    'document_data, raw_data, expected',
    [
        pytest.param(
            {
                'status_id': DocumentStatus.sent.value,
                'title': 'title',
                'number': None,
                'category': None,
                'date_document': None,
                'amount': None,
            },
            {
                'title': 'some new title',
            },
            {
                'validated_data': None,
                'error': InvalidRequest,
                'error_reason': (
                    'Тільки компанія власник документу може змінювати загальні реквізити'
                ),
                'error_details': {},
            },
            id='sent_cant_change_title',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.sent.value,
                'title': 'Some title',
                'number': None,
                'category': None,
                'date_document': None,
                'amount': None,
            },
            {
                'number': '',
            },
            {
                'validated_data': {
                    'title': 'Some title',
                    'number': None,  # normalized to none
                    'category': None,
                    'date_document': None,
                    'amount': None,
                }
            },
            id='sent_no_change_number_passed_empty',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.sent.value,
                'title': 'Some title',
                'number': 'test_num_1',
                'category': PublicDocumentCategory.act.value,
                'date_document': TEST_DATETIME,
                'amount': 1,
            },
            {
                'title': 'Some title',
                'number': 'test_num_1',
                'category': PublicDocumentCategory.act.value,
                'date_document': TEST_DATETIME,
                'amount': 1,
            },
            {
                'validated_data': {
                    'title': 'Some title',
                    'number': 'test_num_1',
                    'category': int(PublicDocumentCategory.act.value),
                    'date_document': TEST_DATETIME,
                    'amount': 1,
                },
            },
            id='sent_no_change_full',
        ),
    ],
)
async def test_validate_update_general_info_by_recipient(
    aiohttp_client,
    document_data: DataDict,
    raw_data: DataDict,
    expected: DataDict,
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_public_document_categories(2)

    edit_user = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app=app,
        owner=user,
        **document_data,
    )

    if 'error' in expected:
        with pytest.raises(expected['error']) as exc:
            await _validate_update_general_info(
                data={'document_settings': raw_data},
                document=DocumentWithUploader.from_row(document),
                user=edit_user,
                request_source=Source.api_internal,
            )
        assert exc.value.reason.value == expected['error_reason']
        assert exc.value.details == expected['error_details']
    else:
        validated_data = await _validate_update_general_info(
            data={'document_settings': raw_data},
            document=DocumentWithUploader.from_row(document),
            user=edit_user,
            request_source=Source.api_internal,
        )
        result_data = {
            'category': validated_data.category,
            'date_document': validated_data.date,
            'number': validated_data.number,
            'title': validated_data.title,
            'amount': validated_data.amount,
        }
        assert result_data == expected['validated_data']


@pytest.mark.parametrize(
    'document_data, update_data, expected',
    [
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'title': 'title',
                'number': '0000',
                'category': PublicDocumentCategory.edi.value,
                'date_document': '2028-01-01T00:00:00',
            },
            {
                'category': PublicDocumentCategory.act.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'validated': {
                    'category': PublicDocumentCategory.act.value,
                    'date_document': '2021-01-01T00:00:00+02:00',
                    'number': '1111',
                    'title': 'new_title',
                }
            },
            id='uploaded_everything_is_updated',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'title': 'title',
                'number': None,
                'category': None,
                'date_document': None,
            },
            {
                'category': PublicDocumentCategory.act.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'validated': {
                    'category': PublicDocumentCategory.act.value,
                    'date_document': '2021-01-01T00:00:00+02:00',
                    'number': '1111',
                    'title': 'new_title',
                }
            },
            id='uploaded_everything_is_updated_empty_document',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'title': 'title',
                'number': '0000',
                'category': PublicDocumentCategory.act.value,
                'date_document': '2023-01-01T00:00:00',
            },
            {
                'category': PublicDocumentCategory.power_of_attorney.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'validated': {
                    'category': PublicDocumentCategory.act.value,
                    'date_document': '2023-01-01T00:00:00+00:00',
                    'number': '0000',
                    'title': 'title',
                },
            },
            id='signed_nothing_updated',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'title': 'title',
                'number': None,
                'category': PublicDocumentCategory.act.value,
                'date_document': None,
            },
            {
                'category': PublicDocumentCategory.power_of_attorney.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'validated': {
                    'category': PublicDocumentCategory.act.value,
                    'date_document': '2021-01-01T00:00:00+02:00',
                    'number': '1111',
                    'title': 'title',
                },
            },
            id='signed_updated_only_empty_fields',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.flow.value,
                'title': 'title',
                'number': None,
                'category': PublicDocumentCategory.act.value,
                'date_document': None,
            },
            {
                'category': PublicDocumentCategory.power_of_attorney.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'validated': {
                    'category': PublicDocumentCategory.act.value,
                    'date_document': '2021-01-01T00:00:00+02:00',
                    'number': '1111',
                    'title': 'title',
                },
            },
            id='flow_updated_only_empty_fields',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.finished.value,
                'title': 'title',
                'number': None,
                'category': PublicDocumentCategory.act.value,
                'date_document': None,
            },
            {
                'category': PublicDocumentCategory.power_of_attorney.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'error': {
                    'exception': errors.Error,
                    'exception_dict': {
                        'code': 'invalid_document_status',
                        'reason': 'Невалідний статус документу',
                        'details': {'status': 7008},
                    },
                },
            },
            id='finished_error',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.reject.value,
                'title': 'title',
                'number': None,
                'category': PublicDocumentCategory.act.value,
                'date_document': None,
            },
            {
                'category': PublicDocumentCategory.power_of_attorney.value,
                'date_document': datetime_test('2021-01-01T00:00:00'),
                'number': '1111',
                'title': 'new_title',
            },
            {
                'error': {
                    'exception': errors.Error,
                    'exception_dict': {
                        'code': 'invalid_document_status',
                        'reason': 'Невалідний статус документу',
                        'details': {'status': 7006},
                    },
                },
            },
            id='rejected_error',
        ),
    ],
)
async def test_validate_update_general_info_status(
    aiohttp_client,
    document_data: dict,
    update_data: dict,
    expected: dict,
):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_public_document_categories(amount=20)

    document = await prepare_document_data(app, user, **document_data)
    document_obj = DocumentWithUploader.from_row(document)
    data = {'document_settings': update_data}

    if validated := expected.get('validated'):
        ctx = await _validate_update_general_info(
            data=data,
            document=document_obj,
            user=user,
            request_source=Source.api_internal,
        )

        assert ctx.category == validated['category']
        assert ctx.date.isoformat() == validated['date_document']
        assert ctx.number == validated['number']
        assert ctx.title == validated['title']

    elif error := expected.get('error'):
        with pytest.raises(error['exception']) as exc:
            await _validate_update_general_info(
                data=data,
                document=document_obj,
                user=user,
                request_source=Source.api_internal,
            )
        assert exc.value.to_dict() == error['exception_dict']

    else:
        raise ValueError('No expected data')


@pytest.mark.parametrize(
    'prepare, data, error',
    [
        pytest.param(
            {},
            {
                'is_ordered': False,
                'is_internal': False,
                'recipients': None,
            },
            {
                'code': 'invalid_request',
                'reason': 'Документ повинен мати хоча б одного одержувача',
                'details': {},
            },
            id='empty_recipients',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Всі одержувачі, крім власника, повинні мати заповнений email',
                'details': {},
            },
            id='emails_empty',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': None,
                        'is_email_hidden': True,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Не можемо знайти підібраний email. Спробуйте підібрати його знову.',
                'details': {},
            },
            id='hidden_email_not_found',
        ),
        pytest.param(
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                }
            },
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [],
            },
            {
                'code': 'invalid_request',
                'reason': (
                    'Зробити двосторонній документ без одержувачів можна '
                    'тільки з внутрішнього документа'
                ),
                'details': {},
            },
            id='change_to_empty_from_not_empty',
        ),
        pytest.param(
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'status_id': DocumentStatus.finished.value,
                },
            },
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_document_status',
                'reason': 'Невалідний статус документу',
                'details': {'status': 7008},
            },
            id='bilateral_is_finished',
        ),
        pytest.param(
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'status_id': DocumentStatus.finished.value,
                },
            },
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_document_status',
                'reason': 'Невалідний статус документу',
                'details': {'status': 7008},
            },
            id='multilateral_is_finished',
        ),
        pytest.param(
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'status_id': DocumentStatus.flow.value,
                },
                'signatures': [
                    {
                        'user_role_id': ROLE_ID_1,
                        'user_id': USER_ID_1,
                        'user_email': TEST_EMAIL_1,
                        'key_owner_edrpou': COMPANY_EDRPOU_1,
                    }
                ],
                'flows': [
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_1,
                        flow_order=0,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=0,  # already signed
                        recipient_emails=[TEST_EMAIL_1],
                    ),
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_2,
                        flow_order=1,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=1,
                        recipient_emails=[TEST_EMAIL_2],
                    ),
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_3,
                        flow_order=2,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=1,
                        recipient_emails=[TEST_EMAIL_3],
                    ),
                ],
            },
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': (
                    'Неможливо змінити порядок підписання для контрагентів, що вже '
                    'підписали документ'
                ),
                'details': {},
            },
            id='multilateral_cant_change_order_of_signed',
        ),
        pytest.param(
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'status_id': DocumentStatus.flow.value,
                },
                'signatures': [
                    {
                        'user_role_id': ROLE_ID_2,
                        'user_id': USER_ID_2,
                        'user_email': TEST_EMAIL_2,
                        'key_owner_edrpou': COMPANY_EDRPOU_2,
                    }
                ],
                'flows': [
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_1,
                        flow_order=None,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=1,
                        recipient_emails=[TEST_EMAIL_1],
                    ),
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_2,
                        flow_order=None,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=0,  # signed
                        recipient_emails=[TEST_EMAIL_2],
                    ),
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_3,
                        flow_order=None,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=1,
                        recipient_emails=[TEST_EMAIL_3],
                    ),
                    CreateFlowCtx(
                        flow_edrpou=COMPANY_EDRPOU_4,
                        flow_order=None,
                        flow_signatures_count=1,
                        flow_pending_signatures_count=1,
                        recipient_emails=[TEST_EMAIL_4],
                    ),
                ],
            },
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': (
                    'Неможливо видалити контрагенів, які вже підписали документ. ЄДРПОУ '
                    'компаній, яких немає в оновленому списку контрагентів: 50000002'
                ),
                'details': {},
            },
            id='multilateral_cant_remove_signed',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Тільки компанія відправник може бути одержувачем внутрішнього документа',
                'details': {},
            },
            id='internal_not_document_owner',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Двосторонній документ повинен бути між двома різними компаніями',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_2],
                },
            },
            id='bilateral_only_one_company',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Компанія відправник має бути однією з компаній-отримувачів документа',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_2, COMPANY_EDRPOU_3],
                },
            },
            id='bilateral_without_owner',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Документ повинен мати хоча б одного підписанта',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_1, COMPANY_EDRPOU_2],
                },
            },
            id='bilateral_no_signer',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': None,
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Багатосторонній документ повинен бути між двома або більше компаніями',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_1],
                },
            },
            id='multilateral_less_than_2_companies',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Компанія відправник має бути однією з компаній-отримувачів документа',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_2, COMPANY_EDRPOU_3, COMPANY_EDRPOU_4],
                },
            },
            id='multilateral_no_owner',
        ),
        pytest.param(
            {},
            {
                'is_ordered': True,
                'is_internal': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                ],
            },
            {
                'code': 'invalid_request',
                'reason': 'Документ повинен мати хоча б одного підписанта',
                'details': {
                    'owner_edrpou': COMPANY_EDRPOU_1,
                    'recipients_edrpous': [COMPANY_EDRPOU_1, COMPANY_EDRPOU_2, COMPANY_EDRPOU_3],
                },
            },
            id='multilateral_no_signer',
        ),
    ],
)
async def test_validate_update_recipients_invalid(
    aiohttp_client,
    prepare: dict,
    data: dict,
    error: dict,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        user_id=USER_ID_1,
        email=TEST_EMAIL_1,
        company_edrpou=COMPANY_EDRPOU_1,
        role_id=ROLE_ID_1,
    )

    await prepare_user_data(
        app=app,
        user_id=USER_ID_2,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    await prepare_user_data(
        app=app,
        user_id=USER_ID_3,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    await prepare_user_data(
        app=app,
        user_id=USER_ID_4,
        email=TEST_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=COMPANY_EDRPOU_4,
    )

    document_data = prepare.get('document') or {}
    document = await prepare_document_data(
        app=app,
        owner=user,
        **document_data,
    )
    signatures_data = prepare.get('signatures') or []
    for signature_data in signatures_data:
        await prepare_signature_row(
            document_id=document.id,
            **signature_data,
        )

    async with services.db.acquire() as conn:
        _options = AddFlowOptions(
            should_send=True,
            should_update_document_status=True,
            should_count_signers=True,
            documents=[Document.from_row(document)],
            receivers=prepare.get('flows') or [],
            assigner_role_id=ROLE_ID_1,
        )
        await create_flows(conn, _options)

        with pytest.raises(errors.Error) as exc_info:
            await _validate_update_recipients(
                conn=conn,
                data={'recipients_settings': data},
                user=User.from_row(user),
                document=Document.from_row(document),
                document_settings=None,
            )

        assert exc_info.value.to_dict() == error


async def test_validate_update_recipients_fill_hidden_emails(
    aiohttp_client,
):
    """
    Check that validation fills hidden emails for recipients
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        user_id=USER_ID_1,
        email=TEST_EMAIL_1,
        company_edrpou=COMPANY_EDRPOU_1,
        role_id=ROLE_ID_1,
    )

    await prepare_user_data(
        app=app,
        user_id=USER_ID_2,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    await prepare_user_data(
        app=app,
        user_id=USER_ID_3,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    await prepare_user_data(
        app=app,
        user_id=USER_ID_4,
        email=TEST_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=COMPANY_EDRPOU_4,
    )

    document = await prepare_document_data(app, owner=user)

    await save_hidden_emails(
        redis=services.redis,
        user=user,
        recipients=[
            RecipientAggregatedData(
                edrpou=COMPANY_EDRPOU_2,
                emails=[TEST_EMAIL_2],
                is_hidden=True,
            ),
            RecipientAggregatedData(
                edrpou=COMPANY_EDRPOU_3,
                emails=[TEST_EMAIL_3],
                is_hidden=True,
            ),
        ],
    )

    async with services.db.acquire() as conn:
        ctx = await _validate_update_recipients(
            conn=conn,
            data={
                'recipients_settings': {
                    'is_ordered': True,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': None,
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': None,
                            'is_email_hidden': True,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_3,
                            'emails': None,
                            'is_email_hidden': True,
                            'role': 'signer',
                        },
                    ],
                },
            },
            user=User.from_row(user),
            document=Document.from_row(document),
            document_settings=None,
        )

    assert ctx is not None
    actual = [
        {
            'edrpou': item.edrpou,
            'emails': item.emails,
            'is_email_hidden': item.is_email_hidden,
        }
        for item in ctx.recipients
    ]
    expected = [
        {
            'edrpou': COMPANY_EDRPOU_1,
            'emails': None,
            'is_email_hidden': False,
        },
        {
            'edrpou': COMPANY_EDRPOU_2,
            'emails': [TEST_EMAIL_2],
            'is_email_hidden': True,
        },
        {
            'edrpou': COMPANY_EDRPOU_3,
            'emails': [TEST_EMAIL_3],
            'is_email_hidden': True,
        },
    ]
    assert actual == expected


@pytest.mark.parametrize(
    'prepare, data, expected',
    [
        pytest.param(
            {'document': {}, 'versions': []},
            {
                'version_settings': None,
            },
            {
                'error': None,
                'context': None,
            },
            id='empty_data',
        ),
        pytest.param(
            {
                'document': {},
                'versions': [],
            },
            {
                'version_settings': {'is_versioned': False},
            },
            {
                'error': None,
                'context': None,
            },
            id='noting_was_changed_non_versioned',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID1,
                },
                'versions': [
                    {
                        'document_id': TEST_UUID1,
                        'role_id': ROLE_ID_1,
                        'company_edrpou': COMPANY_EDRPOU_1,
                    }
                ],
            },
            {
                'version_settings': {'is_versioned': True},
            },
            {
                'error': None,
                'context': None,
            },
            id='noting_was_changed_versioned',
        ),
        pytest.param(
            {
                'document': {
                    'status_id': DocumentStatus.finished.value,
                },
                'versions': [],
            },
            {
                'version_settings': {'is_versioned': True},
            },
            {
                'error': {
                    'type': errors.InvalidRequest,
                    'reason': (
                        'Неможливо змінити версійність документу, після початку процесу підписання'
                    ),
                    'code': 'invalid_request',
                },
                'context': None,
            },
            id='sent_version_bilateral',
        ),
        pytest.param(
            {
                'document': {
                    'status_id': DocumentStatus.flow.value,
                },
                'versions': [],
            },
            {
                'version_settings': {'is_versioned': True},
            },
            {
                'error': {
                    'type': errors.InvalidRequest,
                    'reason': (
                        'Неможливо змінити версійність документу, після початку процесу підписання'
                    ),
                    'code': 'invalid_request',
                },
                'context': None,
            },
            id='sent_version_multilateral',
        ),
        pytest.param(
            {
                'document': {
                    'status_id': DocumentStatus.signed.value,
                },
                'versions': [],
            },
            {
                'version_settings': {'is_versioned': True},
            },
            {
                'error': {
                    'type': errors.InvalidRequest,
                    'reason': (
                        'Неможливо змінити версійність документу, після початку процесу підписання'
                    ),
                    'code': 'invalid_request',
                },
                'context': None,
            },
            id='sent_version_internal',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID1,
                },
                'versions': [
                    {
                        'document_id': TEST_UUID1,
                        'role_id': ROLE_ID_1,
                        'company_edrpou': COMPANY_EDRPOU_1,
                    },
                    {
                        'document_id': TEST_UUID1,
                        'role_id': ROLE_ID_1,
                        'company_edrpou': COMPANY_EDRPOU_2,
                    },
                ],
            },
            {
                'version_settings': {'is_versioned': False},
            },
            {
                'error': {
                    'type': errors.InvalidRequest,
                    'reason': (
                        'Неможливо прибрати версійність документу, бо він має більше однієї версії'
                    ),
                    'code': 'invalid_request',
                },
                'context': None,
            },
            id='more_then_one_version',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID1,
                },
                'versions': [
                    {
                        'document_id': TEST_UUID1,
                        'role_id': ROLE_ID_1,
                        'company_edrpou': COMPANY_EDRPOU_1,
                        'is_sent': True,
                    },
                ],
            },
            {
                'version_settings': {'is_versioned': False},
            },
            {
                'error': {
                    'type': errors.InvalidRequest,
                    'reason': (
                        'Неможливо прибрати версійність документу, бо версія документу вже '
                        'була відправлена контрагенту'
                    ),
                    'code': 'invalid_request',
                },
                'context': None,
            },
            id='has_sent_version',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID1,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'versions': [],
            },
            {
                'version_settings': {'is_versioned': True},
            },
            {
                'error': None,
                'context': UpdateVersionedCtx(is_versioned=True),
            },
            id='success_to_versioned',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID1,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'versions': [
                    {
                        'document_id': TEST_UUID1,
                        'role_id': ROLE_ID_1,
                        'company_edrpou': COMPANY_EDRPOU_1,
                    },
                ],
            },
            {
                'version_settings': {'is_versioned': False},
            },
            {
                'error': None,
                'context': UpdateVersionedCtx(is_versioned=False),
            },
            id='success_to_non_versioned',
        ),
    ],
)
async def test_validate_update_versioned(
    aiohttp_client,
    prepare: DataDict,
    data: DataDict,
    expected: DataDict,
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
    )
    document = await prepare_document_data(app, user, **prepare['document'])

    for version in prepare['versions']:
        await prepare_document_version(**version)

    async with services.db.acquire() as conn:
        coroutine = _validate_update_versioned(
            conn=conn,
            data=data,
            user=User.from_row(user),
            document=Document.from_row(document),
        )
        if expected_error := expected['error']:
            with pytest.raises(expected_exception=expected_error['type']) as exc_info:
                await coroutine
            assert exc_info.value.reason.value == expected_error['reason']
            assert exc_info.value.code.name == expected_error['code']

        else:
            ctx = await coroutine
            assert ctx == expected['context']


@pytest.mark.parametrize(
    'prepare, data, document_settings, expected',
    [
        pytest.param(
            {
                'document': {
                    'title': '',
                    'number': None,
                    'create_document_access_for_recipients': True,
                    'document_recipients': [
                        {'edrpou': COMPANY_EDRPOU_3, 'emails': [TEST_EMAIL_3]},
                        {'edrpou': COMPANY_EDRPOU_4, 'emails': [TEST_EMAIL_4]},
                    ],
                }
            },
            {
                'recipients_settings': {
                    'is_ordered': False,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': [TEST_EMAIL_1],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': [TEST_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_3,
                            'emails': [TEST_EMAIL_3],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_4,
                            'emails': [COMPANY_EDRPOU_4],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                }
            },
            None,
            {
                'exception': errors.InvalidRequest,
                'error': {
                    'code': 'invalid_request',
                    'reason': 'Виникла помилка, перевірте введені дані',
                    'details': {'title': 'is_required', 'doc_number': 'is_required'},
                },
            },
            id='multilateral_should_send',
        ),
        pytest.param(
            {
                'document': {
                    'title': '',
                    'number': None,
                    'create_document_access_for_recipients': True,
                    'document_recipients': [
                        {'edrpou': COMPANY_EDRPOU_3, 'emails': [TEST_EMAIL_3]},
                        {'edrpou': COMPANY_EDRPOU_4, 'emails': [TEST_EMAIL_4]},
                    ],
                },
            },
            {
                'recipients_settings': {
                    'is_ordered': False,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': [TEST_EMAIL_1],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': [TEST_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_3,
                            'emails': [TEST_EMAIL_3],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_4,
                            'emails': [COMPANY_EDRPOU_4],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                }
            },
            UpdateInfoData(
                category=PublicDocumentCategory.other.value,
                date=None,
                number=None,
                title='new title',
                amount=None,
            ),
            {
                'exception': errors.InvalidRequest,
                'error': {
                    'code': 'invalid_request',
                    'reason': 'Виникла помилка, перевірте введені дані',
                    # Title is required, but is filled by document_settings
                    'details': {'doc_number': 'is_required'},
                },
            },
            id='multilateral_should_send_document_settings',
        ),
        pytest.param(
            {
                'document': {
                    'title': '',
                    'number': None,
                }
            },
            {
                'recipients_settings': {
                    'is_ordered': False,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': [TEST_EMAIL_1],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': [TEST_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_3,
                            'emails': [TEST_EMAIL_3],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_4,
                            'emails': [COMPANY_EDRPOU_4],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                }
            },
            None,
            {
                'exception': None,
                'error': None,
            },
            id='multilateral_not_should_send',
        ),
        pytest.param(
            {
                'document': {
                    'title': '',
                    'number': None,
                    'status_id': DocumentStatus.signed_and_sent.value,
                    'document_recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_3,
                            'emails': [TEST_EMAIL_3],
                        }
                    ],
                    'create_document_access_for_recipients': True,
                },
            },
            {
                'recipients_settings': {
                    'is_ordered': True,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': [TEST_EMAIL_1],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': [TEST_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            None,
            {
                'exception': errors.InvalidRequest,
                'error': {
                    'code': 'invalid_request',
                    'reason': 'Виникла помилка, перевірте введені дані',
                    'details': {'title': 'is_required'},
                },
            },
            id='bilateral_signed_and_send',
        ),
        pytest.param(
            {
                'document': {
                    'title': '',
                    'number': None,
                    'status_id': DocumentStatus.uploaded.value,
                }
            },
            {
                'recipients_settings': {
                    'is_ordered': True,
                    'is_internal': False,
                    'recipients': [
                        {
                            'edrpou': COMPANY_EDRPOU_1,
                            'emails': [TEST_EMAIL_1],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                        {
                            'edrpou': COMPANY_EDRPOU_2,
                            'emails': [TEST_EMAIL_2],
                            'is_email_hidden': False,
                            'role': 'signer',
                        },
                    ],
                },
            },
            None,
            {
                'exception': None,
                'error': None,
            },
            id='bilateral_uploaded',
        ),
    ],
)
async def test_validate_update_recipients_required_fields(
    aiohttp_client,
    prepare: DataDict,
    data: DataDict,
    document_settings: UpdateInfoData | None,
    expected: DataDict,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=COMPANY_EDRPOU_1,
        email=TEST_EMAIL_1,
    )
    user_obj = User.from_row(user)

    # Has 1 required field
    company_id_2 = await prepare_company_data(app, edrpou=COMPANY_EDRPOU_2)

    # Has 1 required field
    company_id_3 = await prepare_company_data(app, edrpou=COMPANY_EDRPOU_3)
    await prepare_user_data(app, company_id=company_id_3, email=TEST_EMAIL_3)

    # Has 0 required fields
    await prepare_company_data(app, edrpou=COMPANY_EDRPOU_4)

    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=company_id_2,
            config={'max_required_fields_count': 1},
        )
        await update_billing_company_config(
            conn=conn,
            company_id=company_id_3,
            config={'max_required_fields_count': 1},
        )

        await prepare_document_required_fields(
            conn=conn,
            company_id=company_id_2,
            document_category=DocumentCategoryFields.any,
            is_name_required=True,
            is_type_required=False,
            is_number_required=False,
            is_date_required=False,
            is_amount_required=False,
        )
        await prepare_document_required_fields(
            conn=conn,
            company_id=company_id_3,
            document_category=DocumentCategoryFields.any,
            is_name_required=False,
            is_type_required=False,
            is_number_required=True,
            is_date_required=False,
            is_amount_required=False,
        )

    document = await prepare_document_data(app=app, owner=user, **(prepare['document'] or {}))
    document_obj = Document.from_row(document)

    async with services.db.acquire() as conn:
        with assert_exception(expected['exception']) as exc_info:
            await _validate_update_recipients(
                conn=conn,
                data=data,
                user=user_obj,
                document=document_obj,
                document_settings=document_settings,
            )

        if exc_info is None:
            assert expected['error'] is None
        else:
            assert exc_info.value.to_dict() == expected['error']


@pytest.mark.parametrize(
    'update_data, err_message',
    [
        pytest.param(
            {
                'signers_settings': {
                    'parallel_signing': True,
                    'entities': [
                        {
                            'id': TEST_UUID1,
                            'type': 'group',
                        },
                    ],
                },
            },
            'Неможливо додати підписантом групу без учасників',
            id='empty_group_signer',
        ),
        pytest.param(
            {
                'reviews_settings': {
                    'is_required': False,
                    'reviewers': [
                        {
                            'id': TEST_UUID1,
                            'type': 'group',
                        },
                    ],
                }
            },
            'Неможливо додати в погодження групу без учасників',
            id='empty_group_reviewer',
        ),
    ],
)
async def test_validate_update_with_empty_group_in_settings(
    aiohttp_client,
    update_data: DataDict,
    err_message: str,
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user)
    raw_data = {
        'document_id': document.id,
        **update_data,
    }
    # prepare empty group
    async with services.db.acquire() as conn:
        group = await add_group(conn, name='test1', user=user, group_id=TEST_UUID1)

    # Act
    with pytest.raises(errors.InvalidRequest) as exc:
        async with services.db.acquire() as conn:
            await validate_update_document(
                conn=conn,
                user=user,
                raw_data=raw_data,
                request_source=Source.api_internal,
                signers_source=SignersSource.web,
            )

    # Assertions
    err_dict = exc.value.to_dict()
    assert err_dict['reason'] == err_message
    assert err_dict['details'] == {'empty_groups': [group.id]}


@pytest.mark.parametrize(
    'document_data, user_data',
    [
        pytest.param(
            {
                'create_document_access_for_recipients': True,
                'document_recipients': [
                    {'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': ['<EMAIL>']},
                ],
            },
            {
                'email': '<EMAIL>',
                'company_edrpou': TEST_RECIPIENT_EDRPOU,
                'can_view_document': False,
                'user_role': UserRole.user.value,
            },
            id='recipient_can_access_document',
        ),
        pytest.param(
            {
                'create_document_access_for_recipients': True,
                'document_recipients': [
                    {'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': ['<EMAIL>']},
                ],
            },
            {
                'email': '<EMAIL>',
                'company_edrpou': TEST_RECIPIENT_EDRPOU,
                'can_view_document': False,
                'user_role': UserRole.admin.value,
            },
            id='recipient_admin_can_access_document',
        ),
        pytest.param(
            {},
            {
                'email': '<EMAIL>',
                'company_edrpou': TEST_COMPANY_EDRPOU,
                'can_view_document': True,
                'user_role': UserRole.user.value,
            },
            id='coworker_can_view_document',
        ),
        pytest.param(
            {},
            {
                'email': '<EMAIL>',
                'company_edrpou': TEST_COMPANY_EDRPOU,
                'can_view_document': True,
                'user_role': UserRole.admin.value,
            },
            id='admin_can_access_document',
        ),
    ],
)
async def test_validate_document_access_success(
    aiohttp_client,
    document_data: dict,
    user_data: dict,
):
    app, _, owner = await prepare_client(aiohttp_client, company_edrpou=TEST_COMPANY_EDRPOU)

    user = await prepare_user_data(app, **user_data)
    document = await prepare_document_data(app, owner, **document_data)

    async with services.db.acquire() as conn:
        result = await validate_document_access(conn, user, document.id)
        assert result is not None
        assert result.id == document.id


@pytest.mark.parametrize(
    'document_data, user_data',
    [
        pytest.param(
            {},
            {
                'email': '<EMAIL>',
                'can_view_document': False,
                'user_role': UserRole.user.value,
                'company_edrpou': TEST_COMPANY_EDRPOU,
            },
            id='user_without_view_permission_cannot_access',
        ),
        pytest.param(
            {},
            {
                'email': '<EMAIL>',
                'company_edrpou': '99999999',
                'can_view_document': True,
                'user_role': UserRole.admin.value,
            },
            id='user_from_different_company_cannot_access',
        ),
        pytest.param(
            {
                'create_document_access_for_recipients': True,
                'document_recipients': [
                    {'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': ['<EMAIL>']},
                ],
            },
            {
                'email': '<EMAIL>',
                'company_edrpou': TEST_RECIPIENT_EDRPOU,
                'can_view_document': False,
                'user_role': UserRole.user.value,
            },
            id='wrong_recipient_email_cannot_access',
        ),
    ],
)
async def test_validate_document_access_denied(
    aiohttp_client,
    document_data: dict,
    user_data: dict,
):
    app, _, owner = await prepare_client(aiohttp_client, company_edrpou=TEST_COMPANY_EDRPOU)

    document = await prepare_document_data(app, owner, **document_data)

    # Create test user or use owner if user_data is empty
    if user_data:
        user = await prepare_user_data(app, **user_data)
    else:
        user = owner

    async with services.db.acquire() as conn:
        with pytest.raises(AccessDenied) as exc_info:
            await validate_document_access(conn, user, document.id)
        assert exc_info.value.reason.value == 'Доступ до документу заборонено'
