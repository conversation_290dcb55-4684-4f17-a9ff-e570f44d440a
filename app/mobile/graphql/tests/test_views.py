from app.mobile import constants
from app.mobile.tests import common as mobile_tests_common

GQL_CURRENT_ROLE = '{ currentRole { status } }'


async def test_graphql(aiohttp_client, monkeypatch):
    """
    Given: user with 2FA verified
    When: access graphql route
    Then: user can access graphql route
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()
    assert response.status == 200, data
    assert 'data' in data
    assert 'currentRole' in data['data']
