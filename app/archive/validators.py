from api.errors import Access<PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON>, <PERSON>, DoesNotExist, Error, Object
from app.archive.db import select_document_archives
from app.archive.schemas import ArchiveDocumentsSchema, UnArchiveDocumentsSchema
from app.archive.types import ArchiveDocumentsCtx
from app.archive.utils import count_archived_documents
from app.auth.db import select_company_config
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.billing.enums import CompanyLimit
from app.billing.utils import get_billing_company_config, get_rate_limit
from app.directories.validators import validate_directory_exists
from app.documents.types import Document
from app.documents.validators import validate_documents_access
from app.i18n import _, ngettext
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.lib.validators import validate_pydantic


async def validate_documents_status_for_archive(
    conn: DBConnection,
    user: User,
    documents: list[Document],
) -> None:
    company_config = await select_company_config(conn=conn, company_id=user.company_id)
    allowed_statuses = company_config.archive_settings.allowed_document_statuses

    if not_allowed_doc_ids := [doc.id for doc in documents if doc.status not in allowed_statuses]:
        raise Error(
            Code.invalid_document_status,
            details={'document_ids': not_allowed_doc_ids},
        )


async def validate_archive_documents(
    *, conn: DBConnection, user: User, data: DataDict
) -> ArchiveDocumentsCtx:
    valid_data = validate_pydantic(ArchiveDocumentsSchema, data)
    document_ids = valid_data.document_ids
    directory_id = valid_data.directory_id

    config = await get_billing_company_config(conn, company_edrpou=user.company_edrpou)
    archive_limit = get_rate_limit(config, CompanyLimit.max_archive_documents_count)
    if archive_limit == 0:
        raise AccessDenied(reason=_('Тариф не передбачає можливість архівувати документи'))

    validate_user_permission(user, {'can_archive_documents'})
    documents = await validate_documents_access(conn=conn, user=user, doc_ids=document_ids)
    directory = None
    if directory_id is not None:
        directory = await validate_directory_exists(
            conn=conn, id=directory_id, company_id=user.company_id
        )

    archive_size = await count_archived_documents(
        company_edrpou=user.company_edrpou,
    )

    if archive_size + len(document_ids) > (archive_limit or float('inf')):
        extra_text = ''
        if archive_limit is not None:
            documents_left = archive_limit - archive_size
            if documents_left > 0:
                extra_text = ngettext(
                    '. Ви можете додати до архіву ще {n} документ',
                    '. Ви можете додати до архіву ще {n} документи',
                    '. Ви можете додати до архіву ще {n} документів',
                    n=documents_left,
                ).value

        raise Error(
            Code.overdraft,
            reason=Code.overdraft.value + extra_text,
            log_extra={'archive_size': archive_size, 'archive_limit': archive_limit},
        )

    await validate_documents_status_for_archive(conn=conn, user=user, documents=documents)

    archives = await select_document_archives(
        conn=conn,
        company_id=user.company_id,
        document_ids=document_ids,
    )
    if archives:
        raise AlreadyExists(
            Object.document_archive,
            document_ids=[archive.document_id for archive in archives],
        )

    return ArchiveDocumentsCtx(
        documents=documents,
        directory=directory,
    )


async def validate_unarchive_documents(
    *, conn: DBConnection, user: User, data: DataDict
) -> list[Document]:
    valid_data = validate_pydantic(UnArchiveDocumentsSchema, data)
    document_ids = valid_data.document_ids

    validate_user_permission(user, {'can_archive_documents'})
    await validate_documents_archived(
        conn=conn, company_id=user.company_id, document_ids=document_ids
    )
    documents = await validate_documents_access(conn=conn, user=user, doc_ids=document_ids)

    return documents


async def validate_documents_archived(
    conn: DBConnection, company_id: str, document_ids: list[str]
) -> None:
    archives = await select_document_archives(
        conn=conn, company_id=company_id, document_ids=document_ids
    )
    if len(archives) != len(document_ids):
        raise DoesNotExist(
            Object.document_archive,
            document_ids=list(set(document_ids) - {archive.document_id for archive in archives}),
        )
