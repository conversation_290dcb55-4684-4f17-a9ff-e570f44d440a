import logging

from aiohttp import web

from app.auth.db import select_exists_users_emails
from app.auth.decorators import super_admin_permission_required
from app.auth.types import User
from app.lib import validators
from app.lib.database import DBRow
from app.registration.emailing import send_invite_email
from app.registration.validators import validate_invite_new_users

logger = logging.getLogger(__name__)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def invite_new_users(request: web.Request, user: DBRow) -> web.Response:
    """Invite new users by email notification.

    To invite userxs need to upload CSV file with columns:
        - edrpou
        - email
    """
    valid_data, counters = validate_invite_new_users(
        await validators.validate_post_request(request)
    )

    send_invites = 0
    user_exists = 0
    error_send_invites = 0
    emails = [data['email'] for data in valid_data]

    async with request.app['db'].acquire() as conn:
        # Get registered emails from email list
        exists_users_emails = await select_exists_users_emails(conn, emails)
        exists_emails = [item.email for item in exists_users_emails]

        for data in valid_data:
            email = data['email']
            edrpou = data['edrpou']

            # Do not send invite if email is exist
            if email in exists_emails:
                user_exists += 1
                continue

            # Send invite
            try:
                await send_invite_email(
                    conn=conn,
                    current_user=User.from_row(user),
                    recipient_edrpou=edrpou,
                    recipient_email=email,
                    email_domains=None,
                )
                send_invites += 1
            except Exception as err:
                error_send_invites += 1
                logger.warning(
                    'Unable to invite new user',
                    exc_info=True,
                    extra={'email': email, 'edrpou': edrpou, 'error': err},
                )

    counters = dict(
        counters,
        send_invites=send_invites,
        user_exists=user_exists,
        error_send_invites=error_send_invites,
    )
    return web.json_response(counters)
