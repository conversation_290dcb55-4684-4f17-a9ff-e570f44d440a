import io
import logging
from functools import partial

import pymupdf
from botocore.exceptions import ClientError
from PIL import Image

from app.auth.types import User
from app.lib import s3_utils
from app.lib.database import DBConnection
from app.lib.gotenberg import GotenbergClient, is_landscaped
from app.lib.helpers import not_none, run_sync_in_process_pool
from app.lib.s3_utils import DownloadFile, UploadFile
from app.templates.constants import (
    PREVIEW_FORMAT,
    PREVIEW_MAX_SIZE,
    PREVIEW_QUALITY,
    TEMPLATE_PREVIEW_PREFIX,
)
from app.templates.db import insert_template
from app.templates.types import CopyDocumentAsTemplateCtx, Template
from app.templates.validators import AddTemplateSchema, TemplatePreviewContext

logger = logging.getLogger(__name__)


def get_templates_s3_key(template_id: str) -> str:
    """
    Get s3 key for a template
    """
    return f'templates/original/{template_id}'


def get_template_preview_s3_key(
    template_id: str,
    *,
    format: str = PREVIEW_FORMAT,
    max_size: int = PREVIEW_MAX_SIZE,
    quality: int = PREVIEW_QUALITY,
) -> str:
    """
    Get s3 key for a template preview
    """
    return f'{TEMPLATE_PREVIEW_PREFIX}/{template_id}/{format}/{max_size}/{quality}'


async def get_or_generate_template_preview(ctx: TemplatePreviewContext) -> bytes:
    try:
        content, _ = await s3_utils.download(
            DownloadFile(
                key=get_template_preview_s3_key(
                    ctx.template.id,
                    max_size=ctx.validated_data.size,
                )
            )
        )
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            content = await generate_template_preview(
                template=ctx.template,
                max_size=ctx.validated_data.size,
            )
        else:
            raise e

    return content


def _convert_pdf_to_img(
    *,
    pdf_content: bytes,
    max_size: int,
    quality: int,
    format: str,
) -> bytes:
    """
    Convert first page of a PDF to an image.

    Warning: consider using with process pool
     because it's CPU-bound operation
    """
    try:
        pdf = pymupdf.open(stream=io.BytesIO(pdf_content))
        pix = pdf.load_page(0).get_pixmap()

        image = Image.frombytes('RGB', (pix.width, pix.height), pix.samples)
        # resize the image where the larger of the two dimensions is MAX_PREVIEW_SIZE
        image.thumbnail((max_size, max_size))

        preview = io.BytesIO()
        image.save(preview, format=format, quality=quality)

        return preview.getvalue()
    finally:
        if 'pdf' in locals():
            pdf.close()


async def generate_template_preview(
    *,
    template: Template,
    format: str = PREVIEW_FORMAT,
    max_size: int = PREVIEW_MAX_SIZE,
    quality: int = PREVIEW_QUALITY,
    content: bytes | None = None,
    page: int = 1,
) -> bytes:
    """
    Generate an image preview for a template.
    And upload it to S3.

    TODO: see if we need to move async task
    """

    if content is None:
        content, _ = await s3_utils.download(DownloadFile(key=get_templates_s3_key(template.id)))

    pdf_content = await GotenbergClient.convert_office_file_to_pdf(
        content=content,
        extension=template.extension[1:],
        page_ranges=[f'{page}-{page}'],
        landscape=is_landscaped(template.extension),
    )
    func = partial(
        _convert_pdf_to_img,
        pdf_content=pdf_content,
        max_size=max_size,
        quality=quality,
        format=format,
    )

    logger.info('Generating new template preview')
    preview_content = await run_sync_in_process_pool(func, timeout=3)

    await s3_utils.upload(
        item=UploadFile(
            key=get_template_preview_s3_key(
                template.id,
                format=format,
                max_size=max_size,
                quality=quality,
            ),
            body=preview_content,
        ),
    )
    return preview_content


async def copy_document_as_template(
    conn: DBConnection,
    user: User,
    ctx: CopyDocumentAsTemplateCtx,
) -> Template:
    from app.documents.utils import get_document_s3_key

    template = await insert_template(
        conn=conn,
        title=ctx.document.title,
        extension=not_none(ctx.document.extension),
        category_id=ctx.document.category,
        company_id=user.company_id,
        created_by_role_id=user.role_id,
    )
    content, _ = await s3_utils.download(
        DownloadFile(
            key=get_document_s3_key(document_id=ctx.document.id, version_id=ctx.version_id)
        )
    )
    await s3_utils.upload(
        item=UploadFile(
            key=get_templates_s3_key(template.id),
            body=content,
        )
    )
    try:
        await generate_template_preview(template=template, content=content)
    except Exception:
        logger.exception('Failed to generate template preview')
    return template


async def add_template(
    conn: DBConnection,
    user: User,
    validated_data: AddTemplateSchema,
    is_private: bool = True,
) -> Template:
    template = await insert_template(
        conn=conn,
        title=validated_data.title,
        extension=validated_data.extension,
        category_id=validated_data.category_id,
        company_id=user.company_id if is_private else None,
        created_by_role_id=user.role_id,
    )
    content = validated_data.content or validated_data.empty_file
    item = UploadFile(
        key=get_templates_s3_key(template.id),
        body=content,
    )
    await s3_utils.upload(item)
    try:
        await generate_template_preview(template=template, content=content)
    except Exception:
        logger.exception('Failed to generate template preview')

    return template
