import logging

import logevo
from aiohttp import ClientError, ClientResponse, ClientResponseError, ClientTimeout

from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


class TTNTimeoutError(TimeoutError):
    pass


class TTNClientError(ClientError):
    pass


class TTNUnknownError(Exception):
    pass


class TTNClient:
    """
    Client for Vchasno TTN API
    """

    @staticmethod
    async def make_post_request(
        *,
        endpoint: str,
        json: DataDict,
    ) -> ClientResponse:
        config = services.config.ttn

        try:
            # TODO: currently host is set to public domain, but consider to use internal K8S
            #  service name instead to avoid paying for public traffic
            async with services.http_client.post(
                url=config.host + endpoint,
                json=json,
                headers={
                    'X-Vchasno-Private-Token': config.auth_token,
                    'X-Request-Id': logevo.get_current_rxid(),
                },
                timeout=ClientTimeout(total=10),
            ) as response:
                response.raise_for_status()
                return response
        except TimeoutError as err:
            logger.error(
                msg='TTN request timeout',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                },
            )
            raise TTNTimeoutError() from err
        except ClientResponseError as err:
            logger.error(
                msg='TTN request error',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                    'response_status': err.status,
                    'response_message': err.message,
                },
            )
            raise TTNClientError() from err
        except Exception as err:
            logger.error(
                msg='TTN request internal error',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                },
            )
            raise TTNUnknownError() from err

    async def sync_vchasno_profile(self, data: DataDict) -> None:
        await self.make_post_request(
            endpoint='/api/private/edo-integrations/sync-users',
            json=data,
        )


client = TTNClient()
