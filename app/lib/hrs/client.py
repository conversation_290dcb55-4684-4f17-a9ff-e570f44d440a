import logging
import typing as t

import logevo
import yarl
from aiohttp import ClientError, ClientSession, ClientTimeout

from app.config.schemas import Config
from app.lib.types import DataDict

logger = logging.getLogger(__name__)


class HRSClientError(ClientError):
    pass


class HRSUnknownError(Exception):
    pass


class HRSMobileResponse(t.NamedTuple):
    status: int
    content: bytes
    content_type: str
    extra_headers: dict[str, str] | None = None


class HRSClient:
    extra_headers: list[str] = [
        'content-disposition',
        'content-length',
        'last-modified',
    ]
    bad_statues_for_proxy_resp: list[int] = [400, 403, 422]

    request_timeout: float
    http_client: ClientSession

    base_url: yarl.URL
    token: str

    def __init__(self, config: Config, http_client: ClientSession) -> None:
        self.http_client = http_client
        self.request_timeout = config.app.request_timeout

        if not config.hrs:
            raise ValueError('HRS config is not set')

        assert config.hrs.api_url, 'HRS URL expected'
        self.base_url = yarl.URL(config.hrs.api_url)

        self.token = config.hrs.token or 'fake-token'

    async def mobile_proxy(
        self,
        vchasno_id: str,
        method: str,
        path: str,
        params: DataDict | None = None,
        content: bytes | None = None,
        content_type: str | None = None,
    ) -> HRSMobileResponse:
        """
        Proxy request to HRS EDO api.
        Return status from HRS and data json.
        """

        hrs_path = f'api/mobile/v1/{vchasno_id}/{path}'
        url = self.base_url / hrs_path
        headers = self._default_headers()

        if content_type:
            headers['Content-Type'] = content_type

        try:
            async with self.http_client.request(
                method=str(method).upper(),
                url=str(url),
                headers=headers,
                params=params,
                data=content or None,
                timeout=ClientTimeout(total=self.request_timeout),
            ) as resp:
                resp_content = await resp.content.read()
                resp_status = resp.status

                if not resp.ok and resp_status not in self.bad_statues_for_proxy_resp:
                    # If status isn't expected return 500 error
                    logger.error(
                        msg='HRS client error',
                        extra={
                            'request_method': method,
                            'request_url': url,
                            'request_content_type': content_type,
                            'request_data': str(content)[:2000],
                            'response_body': str(resp_content)[:2000],
                            'response_status': resp_status,
                        },
                    )
                    raise HRSClientError()

                return HRSMobileResponse(
                    status=resp_status,
                    content=resp_content,
                    content_type=resp.headers.get('Content-Type', 'application/json'),
                    extra_headers={
                        k: v for k, v in resp.headers.items() if k.lower() in self.extra_headers
                    },
                )
        except Exception as err:
            logger.error(
                msg='HRS request internal error',
                extra={
                    'request_method': method,
                    'request_url': url,
                    'request_params': str(params),
                    'request_content_type': content_type,
                    'request_data': str(content)[:2000],
                },
            )
            raise HRSUnknownError() from err

    async def sign_document(
        self,
        document_id: str,
        email: str | None,
        vchasno_id: str | None,
        edrpou: str,
    ) -> None:
        """
        Mark document as signed in HRS.
        """

        hrs_path = f'api/private/v1/document/{document_id}/sign'
        url = self.base_url / hrs_path

        await self._private_request(
            url,
            data={
                'email': email,
                'vchasno_id': vchasno_id,
                'edrpou': edrpou,
            },
        )

    async def reject_document(
        self,
        document_id: str,
        email: str | None,
        vchasno_id: str | None,
        edrpou: str,
        reject_reason: str,
    ) -> None:
        """
        Mark document as rejected in HRS.
        """

        hrs_path = f'api/private/v1/document/{document_id}/decline'
        url = self.base_url / hrs_path

        await self._private_request(
            url,
            data={
                'email': email,
                'vchasno_id': vchasno_id,
                'edrpou': edrpou,
                'reason': reject_reason,
            },
        )

    async def _private_request(
        self,
        url: yarl.URL,
        data: DataDict,
        headers: dict[str, t.Any] | None = None,
    ) -> None:
        try:
            async with self.http_client.post(
                str(url),
                json=data,
                headers=headers or self._default_headers(),
                timeout=ClientTimeout(total=self.request_timeout),
            ) as resp:
                resp_content = await resp.content.read()
                resp_status = resp.status

                if not resp.ok and resp_status not in self.bad_statues_for_proxy_resp:
                    # If status isn't expected return 500 error
                    logger.error(
                        msg='HRS client error',
                        extra={
                            'request_method': 'POST',
                            'request_url': url,
                            'request_data': str(data)[:2000],
                            'response_body': str(resp_content)[:2000],
                            'response_status': resp_status,
                        },
                    )
                    raise HRSClientError()

        # we should re-raise timeout errors, these will be handled by retry decorator
        except (TimeoutError, ClientError):
            raise

        except Exception as err:
            logger.error(
                msg='HRS request internal error',
                extra={
                    'request_method': 'POST',
                    'request_url': url,
                    'request_data': str(data)[:2000],
                },
            )
            raise HRSUnknownError() from err

    def _default_headers(self) -> dict[str, t.Any]:
        return {
            'X-Vchasno-Private-Token': self.token,
            'X-Request-Id': logevo.get_current_rxid(),
        }
