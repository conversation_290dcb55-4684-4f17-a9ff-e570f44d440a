import json
from http import HTTPStatus
from unittest.mock import Async<PERSON>ock, MagicM<PERSON>, Mock

import yarl

from app.lib.hrs.client import HRS<PERSON>lient
from app.lib.types import DataDict
from app.mobile.constants import MOBILE_AUTH_HEADER, MOBILE_AUTH_ROLE_HEADER
from app.mobile.tests.common import create_user_with_2fa_verified
from app.services import services


def mock_http_client(
    monkeypatch,
    resp_status: int = 200,
    resp_json: DataDict | None = None,
    req_side_effect: Exception | None = None,
):
    if not resp_json:
        resp_json = {}

    http_resp_mock = AsyncMock()
    http_resp_mock.status = resp_status
    http_resp_mock.ok = resp_status < 400
    http_resp_mock.headers = {
        'Content-Type': 'application/json',
    }
    if resp_json:
        http_resp_mock.content.read.return_value = json.dumps(resp_json).encode('utf-8')

    async_cm = AsyncMock()
    async_cm.__aenter__.return_value = http_resp_mock

    http_client_mock = MagicMock()

    if req_side_effect:
        # For exception case, configure request to raise the exception
        async def side_effect(*args, **kwargs):
            raise req_side_effect

        http_client_mock.request = AsyncMock(side_effect=side_effect)
    else:
        http_client_mock.request.return_value = async_cm

    hrs_client = HRSClient(services.config, http_client=http_client_mock)
    hrs_client_ctx_mock = Mock()
    hrs_client_ctx_mock.get.return_value = hrs_client
    monkeypatch.setattr('app.services.services.ctx_hrs_client', hrs_client_ctx_mock)

    return http_client_mock


async def test_hrs_mobile_proxy_get(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']

    # mock http client
    http_client_mock = mock_http_client(monkeypatch)

    base_path = '/mobile-api/v1/hrs/proxy'
    params = {'search': 'test search req'}
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint'

    # req
    resp = await client.get(
        path=f'{base_path}/{path}',
        params=params,
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert resp.status == HTTPStatus.OK

    assert http_client_mock.request.call_args[1]['method'] == 'GET'
    expected_url = str(yarl.URL(services.config.hrs.api_url) / 'api/mobile/v1' / user.id / path)
    assert http_client_mock.request.call_args[1]['url'] == expected_url
    assert http_client_mock.request.call_args[1]['params'] == params
    assert (
        http_client_mock.request.call_args[1]['headers']['X-Vchasno-Private-Token']
        == services.config.hrs.token
    )
    assert http_client_mock.request.call_args[1]['data'] is None


async def test_hrs_mobile_proxy_post(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']

    # mock http client
    http_client_mock = mock_http_client(monkeypatch)

    base_path = '/mobile-api/v1/hrs/proxy'
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint/payload'
    expected_data = b'{"key":"value"}'
    json_body = json.loads(expected_data)

    # req
    resp = await client.post(
        path=f'{base_path}/{path}',
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
        json=json_body,
    )
    assert resp.status == HTTPStatus.OK

    assert http_client_mock.request.call_args[1]['method'] == 'POST'
    expected_url = str(yarl.URL(services.config.hrs.api_url) / 'api/mobile/v1' / user.id / path)
    assert http_client_mock.request.call_args[1]['url'] == expected_url
    assert http_client_mock.request.call_args[1]['params'] == {}
    assert (
        http_client_mock.request.call_args[1]['headers']['X-Vchasno-Private-Token']
        == services.config.hrs.token
    )
    assert http_client_mock.request.call_args[1]['data'] == expected_data


async def test_hrs_mobile_proxy_resp(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']
    expected_resp = {'key': 'val'}

    # mock http client
    mock_http_client(monkeypatch, resp_json=expected_resp)

    base_path = '/mobile-api/v1/hrs/proxy'
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint'

    # req
    resp = await client.get(
        path=f'{base_path}/{path}',
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert resp.status == HTTPStatus.OK
    assert await resp.json() == expected_resp


async def test_hrs_mobile_proxy_exception(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']

    # mock http client
    mock_http_client(monkeypatch, req_side_effect=Exception('something went wrong'))

    base_path = '/mobile-api/v1/hrs/proxy'
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint'

    # req
    resp = await client.get(
        path=f'{base_path}/{path}',
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert resp.status == HTTPStatus.INTERNAL_SERVER_ERROR


async def test_hrs_mobile_proxy_client_exception(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']
    expected_resp = {'error': 'ValidationError'}

    base_path = '/mobile-api/v1/hrs/proxy'
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint'

    # mock http client
    mock_http_client(monkeypatch, resp_status=422, resp_json=expected_resp)

    # req
    resp = await client.get(
        path=f'{base_path}/{path}',
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert resp.status == HTTPStatus.UNPROCESSABLE_ENTITY
    assert await resp.json() == expected_resp


async def test_hrs_mobile_proxy_bad_client_exception(aiohttp_client, monkeypatch) -> None:
    # setup
    user_data = await create_user_with_2fa_verified(aiohttp_client, monkeypatch)
    user = user_data['user']
    client = user_data['client']

    base_path = '/mobile-api/v1/hrs/proxy'
    path = 'mobile-api/v1/hrs/proxy/test/mobile/hrs/endpoint'

    # mock http client
    mock_http_client(monkeypatch, resp_status=401, resp_json={'error': 'unauthorized'})

    # req
    resp = await client.get(
        path=f'{base_path}/{path}',
        headers={
            MOBILE_AUTH_HEADER: user_data['access_token'],
            MOBILE_AUTH_ROLE_HEADER: user.role_id,
        },
    )
    assert resp.status == HTTPStatus.INTERNAL_SERVER_ERROR
