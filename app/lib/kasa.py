import logging

import logevo
from aiohttp import ClientError, ClientResponse, ClientResponseError, ClientTimeout

from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


class KasaTimeoutError(TimeoutError):
    pass


class KasaClientError(ClientError):
    pass


class KasaUnknownError(Exception):
    pass


class KasaClient:
    """
    Client for Vchasno Kasa API
    """

    @staticmethod
    async def make_post_request(
        *,
        endpoint: str,
        json: DataDict,
    ) -> ClientResponse:
        config = services.config.kassa

        try:
            # TODO: currently host is set to public domain, but consider to use internal K8S
            #  service name instead to avoid paying for public traffic
            async with services.http_client.post(
                url=config.host + endpoint,
                json=json,
                headers={
                    'X-Vchasno-Private-Token': config.auth_token,
                    'X-Request-Id': logevo.get_current_rxid(),
                },
                timeout=ClientTimeout(total=10),
            ) as response:
                response.raise_for_status()
                return response
        except TimeoutError as err:
            logger.error(
                msg='Ka<PERSON> request timeout',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                },
            )
            raise KasaTimeoutError() from err
        except ClientResponseError as err:
            logger.error(
                msg='Kassa request error',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                    'response_status': err.status,
                    'response_message': err.message,
                },
            )
            raise KasaClientError() from err
        except Exception as err:
            logger.error(
                msg='Kassa request internal error',
                extra={
                    'request_endpoint': endpoint,
                    'request_data': str(json),
                },
            )
            raise KasaUnknownError() from err

    async def sync_vchasno_profile(self, data: DataDict) -> None:
        await self.make_post_request(
            endpoint='/api/integration/edo/vchasno-profile-sync',
            json=data,
        )


client = KasaClient()
