import asyncio
import logging
import typing as t
from concurrent.futures.thread import Thr<PERSON><PERSON><PERSON>Executor
from multiprocessing import get_context

import aiobotocore.session
import aiohttp
from aiohttp import ClientSession, web
from conciergelib.aiohttp.client import ConciergeB<PERSON>endClient
from hiku.engine import Engine as HikuEngine
from hiku.executors.asyncio import AsyncIOExecutor
from hiku.graph import apply
from hiku.introspection.graphql import AsyncGraphQLIntrospection
from pebble import ProcessPool

from api.graph.high_level.graph import HIGH_LEVEL_GRAPH, AsyncGraphQLTypeNameField
from app.analytics.google.client import GoogleAnalyticsClient
from app.auth import providers as auth_providers
from app.config import get_level
from app.config.helpers import is_process_pool_enabled
from app.config.utils import get_config
from app.lib.enums import AppLevel
from app.lib.helpers import to_json
from app.lib.hrs.client import HRSClient
from app.lib.sender.client import EvoSender
from app.lib.sign_context import SignContext
from app.lib.urls import build_url
from app.services import services
from app.telegram.utils import build_telegram_url

if t.TYPE_CHECKING:
    from types_aiobotocore_sesv2.client import SESV2Client

logger = logging.getLogger(__name__)


async def init_client_session(app: web.Application) -> None:
    session = ClientSession(json_serialize=to_json)
    app['client_session'] = session
    services.ctx_http_client.set(session)
    logger.info('Client session instantiated')


async def init_hiku(app: web.Application) -> None:
    engine = HikuEngine(AsyncIOExecutor())
    app['hiku_engine'] = engine
    services.ctx_hiku_engine.set(engine)
    app['hiku_graph'] = HIGH_LEVEL_GRAPH

    # Enable introspection for local envs only
    # to not expose the schema in production
    if get_level() in (AppLevel.local, AppLevel.test):
        app['hiku_graph'] = apply(
            app['hiku_graph'],
            [AsyncGraphQLIntrospection(app['hiku_graph'])],
        )
    else:
        app['hiku_graph'] = apply(app['hiku_graph'], [AsyncGraphQLTypeNameField()])
    logger.info('Hiku instantiated')


async def init_evo_sender(app: web.Application) -> None:
    config = get_config(app).evo_sender
    evo_sender = EvoSender(
        client_session=app['client_session'],
        base_url=config.base_url,
        project_id=config.project_id,
    )
    app['evo_sender'] = evo_sender
    services.ctx_evo_sender.set(evo_sender)


async def init_auth_providers(app: web.Application) -> None:
    """Initialize all auth providers"""
    config = get_config(app)
    if google := config.google_auth:
        await auth_providers.google.auth.setup(clients_ids=google.clients_ids)

    if apple := config.apple_auth:
        auth_providers.apple.client.apple_auth.setup(apple)


async def setup_concierge(app: web.Application) -> None:
    config = get_config(app).concierge

    client = ConciergeBackendClient(
        service_id=config.service,
        endpoint=config.backend_url,
        session=app['client_session'],
        integration_token=config.token,
    )
    app['concierge_client'] = client
    services.ctx_concierge_client.set(client)
    logger.info(
        msg='Concierge enabled',
        extra={
            'concierge_service': config.service,
            'concierge_backend_url': config.backend_url,
        },
    )


async def close_client_session(app: web.Application) -> None:
    client_session = app.get('client_session')
    if client_session:
        await client_session.close()
        logger.info('Closed client session')


async def setup_telegram(app: web.Application) -> None:
    """Subscribe on Telegram messages"""
    # Our api for sending telegram messages
    url = build_url('api.telegram.dispatcher')

    # Telegram API URL with Token
    telegram_url = build_telegram_url(app)

    if not telegram_url:
        return

    try:
        async with ClientSession(timeout=aiohttp.ClientTimeout(total=3)) as session:
            data = {
                'url': url,
                'max_connections': 100,
                'allowed_updates': ['message'],
            }
            telegram_url = f'{telegram_url}/setWebhook'
            async with session.post(telegram_url, json=data) as resp:
                json = await resp.json()
                if not json['ok']:
                    logger.warning(
                        'Telegram webhook was not set',
                        extra={
                            'telegram_url': url,
                            'data': data,
                        },
                    )
    except TimeoutError:
        logger.exception('Timeout when connecting to Telegram')

    logging.info('The Telegram client was setup')


async def setup_sign_context(app: web.Application) -> None:
    timeouts = get_config(app).app.sign_timeout
    services.ctx_sign_context.set(SignContext(timeouts))
    logger.info(msg='Sign context was setup')


async def init_thread_pool(_: web.Application) -> t.AsyncGenerator[None, None]:
    executor = ThreadPoolExecutor(max_workers=10)
    asyncio.get_event_loop().set_default_executor(executor)
    logger.info(
        msg='Default loop executor was setup',
        extra={'num_workers': 10},
    )

    yield

    executor.shutdown(cancel_futures=True)


async def init_process_pool(app: web.Application) -> t.AsyncGenerator[None, None]:
    """
    Initializes a process pool for the given application if it is not in offline_mode
    (for example offline_mode = True for workers and cron apps).

    Using `get_context('spawn')` is preferred over the default 'fork' context because it creates
    a new Python interpreter process, which is more compatible with multithreading environments.

    """

    pool = None
    if is_process_pool_enabled():
        pool = ProcessPool(
            context=get_context('spawn'),
            max_workers=3,
            max_tasks=1000,  # restart worker after running 1000 tasks
        )
    app['process_pool'] = pool

    yield

    if pool is not None:
        pool.close()  # type: ignore
        pool.join()


async def init_bedrock_client(app: web.Application) -> t.AsyncGenerator[None, None]:
    config = get_config(app).bedrock
    if config is None:
        app['bedrock_client'] = None
        services.ctx_bedrock_client.set(None)
        logger.info('Bedrock not configured')
        yield
        return

    session = aiobotocore.session.get_session()
    async with session.create_client(
        'bedrock-runtime',
        region_name=config.region_name,
        aws_access_key_id=config.access_key,
        aws_secret_access_key=config.secret_key,
    ) as client:
        services.ctx_bedrock_client.set(client)
        app['bedrock_client'] = client
        logger.info('BedRock client configured')
        yield


async def init_google_analytics_client(app: web.Application) -> None:
    config = get_config(app).google_analytics
    if not config:
        logger.info('Google analytics config is not found')
        return

    client = GoogleAnalyticsClient(config=config)
    services.ctx_google_analytics_client.set(client)
    app['google_analytics_client'] = client
    logger.info('Google analytics client is set up')


async def init_aws_ses_client(app: web.Application) -> t.AsyncGenerator[None, None]:
    session = aiobotocore.session.get_session()
    config = get_config(app).aws_ses
    if not config:
        app['aws_ses_client'] = None
        services.ctx_aws_ses_client.set(None)
        yield
        return

    client: SESV2Client
    async with session.create_client(
        service_name='sesv2',
        region_name=config.region_name,
        aws_access_key_id=config.access_key,
        aws_secret_access_key=config.secret_key,
    ) as client:
        app['aws_ses_client'] = client
        services.ctx_aws_ses_client.set(client)
        logger.info('AWS SES client configured')
        yield


async def init_hrs_client(app: web.Application) -> None:
    config = get_config(app)

    client = HRSClient(
        config=config,
        http_client=services.http_client,
    )
    app['hrs_client'] = client
    services.ctx_hrs_client.set(client)

    logger.info('HRS Client instantiated')
