import logging
import subprocess
import tempfile
import time
from functools import partial
from io import BytesIO, StringIO

import pymupdf
import uj<PERSON>
from docx import Document
from docx.table import Table
from docx.text.paragraph import Paragraph
from pydantic import ValidationError

from api.downloads.types import to_document
from api.downloads.utils import download_document_content
from app.document_categories.db import select_available_document_categories
from app.document_categories.types import DocumentCategory
from app.document_versions.utils import (
    get_latest_document_version_available_for_company,
)
from app.documents.db import select_document_by_id
from app.documents.types import DocumentWithUploader
from app.documents_ai.constants import (
    ALLOWED_AI_SUGGEST_EXTENSIONS,
    MIN_TEXT_LEN,
    PROMPT_TEMPLATE,
    TEXT_CHUNK_LEN,
)
from app.documents_ai.db import insert_document_suggestion
from app.documents_ai.enums import ModelName
from app.documents_ai.schemas import DocumentMetaSuggest
from app.documents_required_fields.enums import DocumentCategoryFields
from app.lib.helpers import generate_uuid, run_sync_in_process_pool
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


DEFAULT_MODEL_NAME = ModelName.claude_haiku_3
MODEL_ID_BY_NAME = {
    ModelName.claude_haiku_3: 'anthropic.claude-3-haiku-20240307-v1:0',
    ModelName.claude_sonnet_3_5: 'anthropic.claude-3-5-sonnet-20240620-v1:0',
}


async def suggest_document_meta_by_content(
    content: bytes,
    extension: str,
    owner_edrpou: str,
) -> DocumentMetaSuggest | None:
    """
    Do AI suggest for meta information based on document content,
    Persist data in DB (temp)
    """

    async with services.db_readonly.acquire() as conn:
        document_categories = await select_available_document_categories(
            conn=conn,
            company_edrpou=owner_edrpou,
        )

    return await _suggest_document_meta_with_bedrock(
        owner_edrpou=owner_edrpou,
        available_document_categories=document_categories,
        extension=extension,
        document_content=content,
        document_id=None,
    )


async def suggest_document_meta_by_document_id(document_id: str) -> DocumentMetaSuggest | None:
    """
    Do AI suggest for meta information based on document content,
    Persist data in DB (temp)
    """

    async with services.db.acquire() as conn:
        row = await select_document_by_id(conn, document_id=document_id)
        if not row:
            logger.info('Document not found', extra={'document_id': document_id})
            return None

        document = to_document(DocumentWithUploader.from_row(row))

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=document.owned_by_edrpou,
        )
        extension = document_version.extension if document_version else document.extension

        # Currently support only PDF documents processing
        if extension not in ALLOWED_AI_SUGGEST_EXTENSIONS:
            logger.info(
                'Unsupported document extension',
                extra={
                    'document_id': document_id,
                    'extension': extension,
                    'supported_extensions': ', '.join(ALLOWED_AI_SUGGEST_EXTENSIONS),
                },
            )
            return None

        content = await download_document_content(
            conn, document, version_id=document_version.id if document_version else None
        )

        document_categories = await select_available_document_categories(
            conn=conn,
            company_edrpou=document.owned_by_edrpou,
        )

    return await _suggest_document_meta_with_bedrock(
        owner_edrpou=document.owned_by_edrpou,
        available_document_categories=document_categories,
        document_content=content,
        document_id=document_id,
        extension=extension,
    )


async def _suggest_document_meta_with_bedrock(
    owner_edrpou: str,
    available_document_categories: list[DocumentCategory],
    document_content: bytes,
    extension: str,
    document_id: str | None,
) -> DocumentMetaSuggest | None:
    if not services.bedrock_client:
        logger.info('Bedrock client not configured')
        return None

    body = await _prepare_bedrock_payload(
        content=document_content,
        available_document_categories=available_document_categories,
        owner_edrpou=owner_edrpou,
        extension=extension,
    )
    if not body:
        logger.info('Unable to extract text from document', extra={'document_id': document_id})
        return None

    time_start = time.time()
    try:
        result = await services.bedrock_client.invoke_model(
            body=ujson.dumps(body),
            modelId=MODEL_ID_BY_NAME[DEFAULT_MODEL_NAME],
        )
        request_duration = time.time() - time_start
        async with result['body'] as stream:
            resp = await stream.read()
        resp_json = ujson.loads(resp.decode())
        resp_content = resp_json['content'][0]['text']
        usage = resp_json['usage']
        raw_data = ujson.loads(resp_content)
        raw_data['document_id'] = document_id or generate_uuid()
        raw_data['request_duration'] = request_duration
        raw_data['chunk_length'] = TEXT_CHUNK_LEN
        raw_data['tokens_used'] = usage['input_tokens'] + usage['output_tokens']
        raw_data['model'] = DEFAULT_MODEL_NAME
        logger.info('Bedrock model tokens used', extra=usage)
    except Exception:
        logger.exception('Exception during Bedrock API calls', extra={'document_id': document_id})
        return None

    try:
        document_meta_suggest = DocumentMetaSuggest.model_validate(
            raw_data,
            context={'available_categories': {c.id for c in available_document_categories}},
        )
    except ValidationError:
        logger.warning(
            'Unexpected suggestion response from AI model', extra={'raw_data': str(raw_data)}
        )
        return None

    # Save results only for existing documents
    try:
        if document_id is not None:
            async with services.db.acquire() as conn:
                await insert_document_suggestion(conn, document_meta_suggest)
    except Exception:
        logger.exception('Exception during save document_meta analitics data')

    return document_meta_suggest


async def _prepare_bedrock_payload(
    *,
    content: bytes,
    owner_edrpou: str,
    available_document_categories: list[DocumentCategory],
    extension: str,
) -> DataDict | None:
    try:
        text_extractor_func = partial(
            _extract_text_from_document_content_sync,
            content=content,
            extension=extension,
        )
        doc_txt = await run_sync_in_process_pool(text_extractor_func, timeout=3)
    except Exception:
        logger.exception('Failed to extract text from PDF')
        return None

    category_map = {
        c.id: c.title
        for c in available_document_categories
        if c.id != DocumentCategoryFields.any.value
    }
    prompt = PROMPT_TEMPLATE.format(
        doc_text=doc_txt,
        owner_edrpou=owner_edrpou,
        category_map=category_map,
    )
    payload = {
        'messages': [{'role': 'user', 'content': [{'type': 'text', 'text': prompt}]}],
        'anthropic_version': 'bedrock-2023-05-31',
        'max_tokens': 300,  # max tokens in model response
        'temperature': 0,  # The amount of randomness injected into the response
        'stop_sequences': [],
    }
    return payload


def _extract_text_from_pdf_content(content: bytes) -> str | None:
    """
    We assume that document requisites info placed only on first + last page

    WARNING! CPU bound, consider using in process_pool
    """
    try:
        doc_pdf = pymupdf.open(stream=content)

        if doc_pdf.page_count < 1:
            return None

        doc_text = ''
        pages_iterator = (
            doc_pdf
            if doc_pdf.page_count <= 4
            else [doc_pdf[0], doc_pdf[1], doc_pdf[-1], doc_pdf[-2]]
        )
        for page in pages_iterator:
            doc_text += page.get_text()

        return doc_text
    finally:
        if 'doc_pdf' in locals():
            doc_pdf.close()


def _extract_text_from_docx_content(content: bytes) -> str:
    """
    Extract text from docx content
    """

    doc = Document(BytesIO(content))
    doc_text_buffer = StringIO()

    for item in doc.iter_inner_content():
        if isinstance(item, Paragraph):
            doc_text_buffer.write(item.text)
            doc_text_buffer.write(' ')

        elif isinstance(item, Table):
            for row in item.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        doc_text_buffer.write(paragraph.text)
                        doc_text_buffer.write(' ')

    return doc_text_buffer.getvalue()


def _extract_text_from_doc_content(content: bytes) -> str:
    """
    Extract text from doc content.
    Using antiword. Because python-docx does not support
    doc format due to binary format.
    It is not a good solution, but it works. 🥲
    And seems lighter than using libreoffice.

    https://packages.debian.org/en/sid/antiword
    """

    with tempfile.NamedTemporaryFile(suffix='.doc') as temp_file:
        temp_file.write(content)
        return subprocess.run(
            ['antiword', temp_file.name],
            check=True,
            capture_output=True,
        ).stdout.decode()


EXTRACTORS = {
    '.pdf': _extract_text_from_pdf_content,
    '.docx': _extract_text_from_docx_content,
    '.doc': _extract_text_from_doc_content,
}


def _text_cleanup(text: str) -> str:
    # Remove extra new lines
    text = text.replace('\n', ' ')
    # Remove extra spaces before and after text
    text = text.strip()
    # remove | characters generated by antiword for tables
    text = text.replace('|', ' ')
    # Remove extra spaces
    text = ' '.join(text.split())
    return text


def _extract_text_from_document_content_sync(
    *,
    content: bytes,
    extension: str,
) -> str | None:
    """
    Extract text from document content

    We read only chunk of data with length = TEXT_CHUNK_LEN
    from document start + from document end.
    It is initial assumptions, they may be corrected after testing.
    """
    if extension not in EXTRACTORS:
        logger.info('Unsupported document extension', extra={'extension': extension})
        return None

    extractor = EXTRACTORS[extension]
    raw = extractor(content=content)
    if not raw:
        return None

    doc_text = _text_cleanup(raw)

    if len(doc_text) > TEXT_CHUNK_LEN * 2:
        doc_text = ' '.join([doc_text[:TEXT_CHUNK_LEN], doc_text[-TEXT_CHUNK_LEN:]])

    # Do not process files with low text presence, it is possible for PDF with images
    if len(doc_text) < MIN_TEXT_LEN:
        return None

    return doc_text
