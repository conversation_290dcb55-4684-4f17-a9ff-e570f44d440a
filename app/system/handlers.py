import asyncio
import json
import logging
import os
from collections.abc import Callable
from contextlib import suppress

import elasticsearch
from aiohttp import ClientError, ClientTimeout, web
from yarl import URL

from api.errors import Code, Error
from app.auth import concierge
from app.auth.constants import AUTH_METHOD_APP_KEY, AUTH_USER_APP_KEY
from app.auth.enums import AuthMethod
from app.auth.types import AuthUser
from app.config import get_level
from app.es.models.document import Document
from app.lib import s3_utils
from app.lib.enums import AppLevel
from app.lib.helpers import generate_uuid, get_client_ip, to_json
from app.lib.types import DataDict, Handler
from app.services import services

logger = logging.getLogger(__name__)

SUPER_ADMIN_EDRPOU = '41231992'


def system_handler(
    *,
    disable_tracking: bool = True,
    disable_auth: bool = True,
) -> Callable[[<PERSON><PERSON>], <PERSON><PERSON>]:
    """
    Decorator for system routes to exclude them from middlewares, that by default is applied to
    all routes.
    """

    def decorator(handler: Handler) -> Handler:
        handler.__dict__['system_handler_disable_tracking'] = disable_tracking
        handler.__dict__['system_handler_disable_auth'] = disable_auth
        return handler

    return decorator


def is_system_handler_tracking_disabled(handler: Handler) -> bool:
    """
    Should we disable tracking (metrics) for this handler?
    """
    return handler.__dict__.get('system_handler_disable_tracking', False)


def is_system_handler_auth_disabled(handler: Handler) -> bool:
    """
    Should we disable auth for this handler?
    """
    return handler.__dict__.get('system_handler_disable_auth', False)


async def _check_resources() -> dict[str, bool]:
    """
    Performs a series of asynchronous health checks on various internal resources.
    Returns dict in format {resource_name: bool}.
    """

    async def check_db() -> bool:
        try:
            async with services.db.acquire() as conn:
                await conn.execute('select 1;')
            async with services.db_readonly.acquire() as conn:
                await conn.execute('select 1;')
            return True
        except Exception:
            logger.warning('Database (master or readonly) unavailable', exc_info=True)
            return False

    async def check_elasticsearch() -> bool:
        try:
            with suppress(elasticsearch.NotFoundError):
                await services.es.documents.get(
                    'id',
                    doc_cls=Document,
                    routing=SUPER_ADMIN_EDRPOU,
                )
            return True
        except Exception:
            logger.warning('Elasticsearch unavailable', exc_info=True)
            return False

    async def check_s3() -> bool:
        try:
            key = f's3_health_check_{generate_uuid()}'
            upload_file = s3_utils.UploadFile(key=key, body=b'v')
            await s3_utils.upload(upload_file)
            await s3_utils.delete(upload_file.key)
            return True
        except Exception:
            logger.warning('S3 unavailable', exc_info=True)
            return False

    async def check_redis() -> bool:
        try:
            await services.redis.ping()
            return True
        except Exception:
            logger.warning('Redis unavailable', exc_info=True)
            return False

    async def check_kafka() -> bool:
        try:
            await services.kafka.update_cluster_metadata()
            return True
        except Exception:
            logger.warning('Kafka cluster unavailable', exc_info=True)
            return False

    async def check_concierge_backend() -> bool:
        try:
            await concierge.backend_healthcheck()
            return True
        except Exception:
            logger.warning('Concierge unavailable', exc_info=True)
            return False

    async def check_concierge_frontend() -> bool:
        try:
            await concierge.frontend_healthcheck()
            return True
        except Exception:
            logger.warning('Concierge frontend unavailable', exc_info=True)
            return False

    results = await asyncio.gather(
        check_db(),
        check_elasticsearch(),
        check_s3(),
        check_redis(),
        check_kafka(),
        check_concierge_backend(),
        check_concierge_frontend(),
        return_exceptions=False,
    )

    return {
        'DB': results[0],
        'ES': results[1],
        'S3': results[2],
        'REDIS': results[3],
        'KAFKA': results[4],
        'CONCIERGE_BACKEND': results[5],
        'CONCIERGE_FRONTEND': results[6],
    }


@system_handler()
async def debug_handler(request: web.Request) -> web.Response:
    return web.json_response(
        {
            'headers': dict(request.headers),
            'real_ip': get_client_ip(request),
        }
    )


@system_handler()
async def static_hash(_: web.Request) -> web.Response:
    return web.Response(text=os.environ.get('STATIC_MAIN_HASH', ''))


@system_handler()
async def csp_report(request: web.Request) -> web.Response:
    data = await request.json()
    blocked_uri = data.get('csp-report', {}).get('blocked-uri')
    logger.info('CPS Report 1', extra={'blocked_uri': blocked_uri})
    logger.info('CPS Report 2', extra=data)
    return web.Response()


@system_handler()
async def health_status(_: web.Request) -> web.Response:
    """
    Check main resources availability and system health.
    Designed for getting state for every checked resource.
    """

    resources_status = await _check_resources()

    return web.json_response(
        data={key: 'OK' if val else 'ERROR' for key, val in resources_status.items()}
    )


@system_handler()
async def health_check(_: web.Request) -> web.Response:
    """
    Check main resources availability and system health.
    Designed for generally check Healthy/NotHealthy.

    WARNING!
    Do not use it for K8S probes.
    """

    resources_status = await _check_resources()

    if not all(resources_status.values()):
        return web.Response(status=503)

    return web.json_response({'status': 'ok'})


@system_handler()
async def simple_healthcheck(_: web.Request) -> web.Response:
    """Simple health check for K8s probes (liveness and readiness)"""
    return web.Response()


@system_handler()
async def get_well_known_ms_id(_: web.Request) -> web.Response:
    return web.json_response(
        {'associatedApplications': [{'applicationId': services.config.microsoft_auth.client_id}]}
    )


@system_handler()
async def get_well_known_asset_links(_: web.Request) -> web.Response:
    """
    For Android mobile app to open links in the app
    """
    response_data: list[DataDict] = []

    if get_level() == AppLevel.prod:
        response_data = [
            {
                'relation': [
                    'delegate_permission/common.handle_all_urls',
                    'delegate_permission/common.get_login_creds',
                ],
                'target': {
                    'namespace': 'android_app',
                    'package_name': 'ua.vchasno.edo',
                    'sha256_cert_fingerprints': [
                        '5b:d2:4c:58:4f:80:64:a4:5f:50:7e:c2:70:1c:d7:da:c2:d7:fd:19',
                        'f4:f9:bf:ef:a3:f6:07:70:c7:39:ff:66:6a:cb:1e:dd:0e:a5:a2:97',
                        '2c:51:4e:08:62:96:fc:54:51:15:d2:d9:9e:58:39:c8:17:18:e2:92',
                    ],
                },
            }
        ]

    elif get_level() == AppLevel.dev:
        response_data = [
            {
                'relation': [
                    'delegate_permission/common.handle_all_urls',
                    'delegate_permission/common.get_login_creds',
                ],
                'target': {
                    'namespace': 'android_app',
                    'package_name': 'ua.vchasno.edo.dev',
                    'sha256_cert_fingerprints': [
                        'bd:3e:b3:b8:19:ad:78:9b:86:66:3a:c1:0e:3e:cd:1d:2b:60:fa:1c:82:81:c2:4f:b7:cf:fa:33:0a:9c:03:57',
                        '02:62:0a:19:74:e8:5c:69:f7:d1:66:c6:35:7b:80:c5:1b:13:42:c6:b5:85:77:7c:f9:55:a5:30:86:11:f9:34',
                    ],
                },
            }
        ]

    return web.json_response(response_data)


@system_handler()
async def get_well_known_apple_app_site_association(_: web.Request) -> web.Response:
    """
    For iOS mobile app to open links in the app
    """
    response_data: DataDict = {}

    if get_level() == AppLevel.prod:
        response_data = {
            'applinks': {
                'apps': [],
                'details': [
                    {
                        'appID': 'JNSLSBSJH7.ua.vchasno.edo',
                        'paths': [
                            '/app/documents',
                            '/app/documents/*',
                            '/registration/confirm/*',
                        ],
                    }
                ],
            }
        }
    elif get_level() == AppLevel.dev:
        response_data = {
            'applinks': {
                'apps': [],
                'details': [
                    {
                        'appID': 'JNSLSBSJH7.ua.vchasno.edo.dev',
                        'paths': [
                            '/app/documents',
                            '/app/documents/*',
                            '/registration/confirm/*',
                        ],
                    }
                ],
            }
        }

    return web.json_response(response_data)


@system_handler(disable_auth=False)
async def sentry_frontend_logs(request: web.Request) -> web.Response:
    sentry_config = services.config.sentry
    if not sentry_config:
        raise Error(Code.invalid_action)

    dsn_frontend = sentry_config.dsn_frontend
    if not dsn_frontend:
        raise Error(Code.invalid_action)

    # Allow only requests with auth_user
    auth_user: AuthUser = request[AUTH_USER_APP_KEY]
    if not auth_user:
        raise Error(Code.access_denied)

    # Request must be from WEB
    if request[AUTH_METHOD_APP_KEY] not in {AuthMethod.session, AuthMethod.sign_session}:
        raise Error(Code.access_denied)

    # dsn: https://<EMAIL>/5
    url = URL(dsn_frontend)
    project_id = url.path.split('/')[1]
    url = url.with_path(f'/api/{project_id}/envelope/')

    try:
        request_content = (await request.read()).decode()
        request_lines = [json.loads(line) for line in request_content.split('\n')]
        header = request_lines[0]
        dsn = header['dsn']
        project_id_ = dsn.split('/')[-1]
    except Exception as err:
        logger.warning('Sentry proxy. Invalid request')
        raise Error(Code.invalid_request) from err

    if dsn != dsn_frontend or project_id != project_id_:
        logger.warning(
            'Sentry proxy. Invalid dsn or project_id',
            extra={'data': request_content},
        )
        raise Error(Code.invalid_request)

    injected = False
    for line in request_lines:
        # only line with 'timestamp' is a destination for injection
        if 'timestamp' in line:
            event_user = line.get('user') or {}
            event_user_email = event_user.get('email')
            if event_user_email and event_user_email != auth_user.email:
                logger.warning(
                    'Sentry proxy. User email mismatch',
                    extra={
                        'user_email': event_user_email,
                        'auth_user_email': auth_user.email,
                    },
                )
            line['user'] = {
                **event_user,
                'email': auth_user.email,
            }
            injected = True

    if not injected:
        logger.warning('Sentry proxy. No timestamp in request. Skip.')
        raise Error(Code.invalid_request)

    request_content = '\n'.join(to_json(line) for line in request_lines)

    resp = None
    content = None
    try:
        async with services.http_client.post(
            data=request_content,
            url=url,
            timeout=ClientTimeout(total=5),
        ) as resp:
            if not resp.ok:
                content = await resp.read()
            resp.raise_for_status()
    except (TimeoutError, ClientError) as err:
        logger.warning(
            'Sentry proxy. Error on request',
            extra={
                'err_message': resp and resp.reason,
                'err_body': content and content.decode(),
            },
        )
        raise web.HTTPBadRequest from err

    return web.json_response({})


@system_handler()
async def get_security_txt(_: web.Request) -> web.Response:
    security_txt = """Contact: mailto:<EMAIL>
Expires: 2026-12-31T21:59:00.000Z
Encryption: https://vchasno.ua/files/vchasno-ua-pub.asc
Preferred-Languages: en, ua
Canonical: https://vchasno.ua/.well-known/security.txt
Canonical: https://edo.vchasno.ua/.well-known/security.txt"""

    return web.Response(text=security_txt, content_type='text/plain')
