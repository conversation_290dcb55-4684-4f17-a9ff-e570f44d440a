from http import HTTPStatus
from unittest import mock

from app.auth import concierge
from app.tests.common import prepare_app_client


async def test_health_status(aiohttp_client, monkeypatch):
    app, client = await prepare_app_client(aiohttp_client)
    monkeypatch.setattr(concierge, 'backend_healthcheck', mock.AsyncMock())
    monkeypatch.setattr(concierge, 'frontend_healthcheck', mock.AsyncMock())

    resp = await client.get('/~health/status')
    assert resp.status == HTTPStatus.OK

    resp_data = await resp.json()
    assert resp_data == {
        'DB': 'OK',
        'ES': 'OK',
        'REDIS': 'OK',
        'KAFKA': 'OK',
        'S3': 'OK',
        'CONCIERGE_BACKEND': 'OK',
        'CONCIERGE_FRONTEND': 'OK',
    }
