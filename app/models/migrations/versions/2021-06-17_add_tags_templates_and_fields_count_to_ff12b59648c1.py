"""Add tags, templates and fields count to company stats

Revision ID: ff12b59648c1
Revises: d20f02bb96ab
Create Date: 2021-06-17 14:23:46.337478


Author: a<PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa

revision = 'ff12b59648c1'
down_revision = 'd20f02bb96ab'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('companies_statistics', sa.Column('document_fields_count', sa.Integer(), nullable=True))
    op.add_column('companies_statistics', sa.Column('tags_count', sa.Integer(), nullable=True))
    op.add_column('companies_statistics', sa.Column('templates_count', sa.Integer(), nullable=True))
    op.alter_column('companies_statistics', 'roles_count', existing_type=sa.INTEGER(), nullable=True)


def downgrade():
    op.alter_column('companies_statistics', 'roles_count', existing_type=sa.INTEGER(), nullable=False)
    op.drop_column('companies_statistics', 'templates_count')
    op.drop_column('companies_statistics', 'tags_count')
    op.drop_column('companies_statistics', 'document_fields_count')
