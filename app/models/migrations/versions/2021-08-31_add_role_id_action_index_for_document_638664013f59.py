"""Add role_id action index for document actions table

Revision ID: 638664013f59
Revises: 79439bbf1aca
Create Date: 2021-08-31 11:15:24.776779


Author: y.hyzyla
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '638664013f59'
down_revision = '79439bbf1aca'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS idx_document_actions_role_id_action
        ON document_actions (role_id, action);
        """
    )


def downgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        DROP INDEX
        CONCURRENTLY
        IF EXISTS idx_document_actions_role_id_action;
        """
    )
