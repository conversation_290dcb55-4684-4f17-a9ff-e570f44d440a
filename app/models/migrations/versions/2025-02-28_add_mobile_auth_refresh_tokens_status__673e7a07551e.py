"""Add mobile_auth_refresh_tokens status+date_updated index

Revision ID: 673e7a07551e
Revises: be5934bed5a3
Create Date: 2025-02-28 11:59:47.120611

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '673e7a07551e'
down_revision = 'be5934bed5a3'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.create_index(
        'idx_mobile_auth_refresh_tokens_user_id_date_updated',
        'mobile_auth_refresh_tokens',
        ['user_id', 'date_updated'],
        unique=False,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index(
        'idx_mobile_auth_refresh_tokens_user_id_date_updated',
        table_name='mobile_auth_refresh_tokens',
        postgresql_concurrently=True,
    )
