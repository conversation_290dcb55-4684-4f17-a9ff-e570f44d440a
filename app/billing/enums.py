from __future__ import annotations

from enum import (
    Enum,
    IntEnum,
    auto,
    unique,
)
from typing import NamedTuple

from app.lib.enums import NamedEnum

BONUS_ACCOUNT_TYPES_SET = {'client_bonus', 'client_bonus_custom'}

SERVICE_ACCOUNT_TYPES_SET = {
    'service_credit_external',
    'service_credit_bonus',
    'service_debit_external',
    'service_debit_bonus',
    'service_debt',
}

TRANSACTION_TYPES_WITH_OPERATOR_SET = {
    'invoice_payment',
    'invoice_payment_cancel',
    'debit_charge_off',
    'bonus_charge_off',
    'credit_use',
    'credit_repay',
    'cross_company',
}

TRANSACTION_TYPES_WITH_AMOUNT_SET = {
    'invoice_payment',
    'invoice_payment_cancel',
    'debit_charge_off',
    'credit_use',
    'credit_repay',
    'debt_clearance',
    'cross_company',
}

TRANSACTION_TYPES_WITH_UNITS_SET = {
    'invoice_payment',
    'invoice_payment_cancel',
    'debit_charge_off',
    'bonus_income',
    'bonus_cancel',
    'bonus_charge_off',
    'credit_use',
    'cross_company',
}


@unique
class BillingAccountSource(Enum):
    """Enum of potential custom sources for billing accounts"""

    # Trial was activated by redirecting from the landing page
    from_landing = 'from_landing'

    # https://tabula-rasa.atlassian.net/browse/DOC-6633
    employees_limit_reach_ab_testing = 'employees_limit_reach_ab_testing'

    # https://tabula-rasa.atlassian.net/browse/DOC-6800
    used_paid_feature = 'used_paid_feature'


class BankEnum(NamedEnum):
    privat = auto()
    pumb = auto()


@unique
class BillStatus(IntEnum):
    """
    Status of the bill generation process.
    """

    requested = 5000
    generated = 6000
    uploaded = 7000
    signed = 7003
    signed_and_sent = 7004

    # skipped status is possible when:
    # - bill payment made by card
    skipped = 8000


@unique
class BillPaymentStatus(Enum):
    """
    Status of the bill.

    Evopay statuses:
        - requested
        - pin_requested
        - rejected
        - completed

    Pumb / Privatbank statuses:
        - rejected
        - completed

    `NULL`:
        - Used when a bill is created in vchasno or CRM without a transaction.

    `completed`:
        - Indicates a successful transaction or activation by superadmin.

    canceled / refund:
        - Set only by superadmin for mistaken activations or unaccepted payments.
    """

    requested = 'requested'
    pin_requested = 'pin_requested'  # Waiting user to pass the 3DS check

    rejected = 'rejected'
    completed = 'completed'

    canceled = 'canceled'
    refund = 'refund'


@unique
class BillPaymentSource(Enum):
    card = 'card'
    transfer = 'transfer'
    manual = 'manual'

    @property
    def is_card(self) -> bool:
        return self == BillPaymentSource.card

    @property
    def is_transfer(self) -> bool:
        return self == BillPaymentSource.transfer

    @property
    def is_manual(self) -> bool:
        return self == BillPaymentSource.manual


@unique
class CreatioBillType(Enum):
    """
    This enum is used to represent input data from the CRM system Creatio. For historical reasons,
    a type of bill is a mix of actual rates and fake pseudo-rates that represent other billing
    entities, like balance or extension.

    INFO: Don't use this enum when you need to send data to the CRM system, because we use
    "CRM proxy" service to communicate with the CRM system, and it has its own enums. Check
    "CRMBillType" enum as an example.
    """

    free = 'free'

    # Since October 2024, the rates "start" and "pro" will be deprecated, and the CRM will
    # use "start_tov", "start_fop", etc., to send rates based on the type of company.
    start = 'start'
    start_fop = 'start_fop'
    start_tov = 'start_tov'

    pro = 'pro'
    pro_fop = 'pro_fop'
    pro_tov = 'pro_tov'

    ultimate = 'ultimate'
    integration = 'integration'

    archive_small = 'archive_small'
    archive_big = 'archive_big'

    # These keys are not actual rates, but they are, due to historical reasons, passed
    #  in the "rate" field from the CRM system. See "FakeBillRate" enum for some details.
    integration_with_documents = 'integration_with_documents'
    add_employee = 'add_employee'
    documents = 'documents'


@unique
class BillPaymentType(Enum):
    document_payment = 'document_payment'
    rate_payment = 'rate_payment'


@unique
class AccountType(Enum):
    service_credit_external = 'service_credit_external'
    service_credit_bonus = 'service_credit_bonus'
    service_debit_external = 'service_debit_external'
    service_debit_bonus = 'service_debit_bonus'
    service_debt = 'service_debt'
    client_debit = 'client_debit'
    client_bonus = 'client_bonus'
    client_bonus_custom = 'client_bonus_custom'
    client_credit = 'client_credit'
    client_rate = 'client_rate'

    @property
    def is_bonus(self) -> bool:
        return self.value in BONUS_ACCOUNT_TYPES_SET

    @property
    def is_debit(self) -> bool:
        return self == AccountType.client_debit

    @property
    def is_service(self) -> bool:
        return self.value in SERVICE_ACCOUNT_TYPES_SET


class BillSource(NamedEnum):
    vchasno = 'vchasno'
    creatio = 'creatio'


class BillActivationSource(NamedEnum):
    evopay = 'evopay'
    bank = 'bank'
    superadmin = 'superadmin'


class FakeBillRate(NamedEnum):
    """
    This enum is used to represent fake account rates that are not actual account rates.

    These rates are not used in the "billing_accounts" table, but they are used in the
    "bills" table to represent a bill type, alongside real AccountRate.
    So field "rate" in "bills" table looks like this: FakeBillRate | AccountRate | None

    WARNING: don't add new rates here, use "BillServicesType" enum instead. After migration
    to "services" fields in bills, this enum will be removed or will be used only to convert
    for converting from CRM input to the proper services.
    """

    integration_with_documents = auto()
    add_employee = auto()


class AccountRate(NamedEnum):
    """
    How to name items:
    {name}_{year}_{month}
    Why? It's possible to have changes in rates more than 1 times a year. So we should
    be able to use old and new rates and know when it was created. To easily find these
    rates in documentation.

    Check-list to add new rate (backend):
    - Add new value to AccountRate enum
    - Move old value to DEPRECATED_RATES constant, or to FEATURE_FLAG_CHANGED_RATES
         if new rates should release under feature flag (FeatureFlags.ENABLE_NEW_RATES)
    - Add new value to AccountRate.priority_rank property
    - Add new value to price constants: RATES_PRICE_MAP, ...
    - Add new value to RATES_NAME_MAP constant
    - Update AccountRate "latest" properties, ex: latest_pro
    - Update *_SET = {...} constants, ex: PRO_ACCOUNT_RATES_SET
    - Add new rate config RATES_MAP
    - *If use feature flag:
        - edit should_skip_bill_activation_due_to_changed_rates
           to add or remove custom conditions to skip bill activation, ex: if buyer is fop, ...
        - update changed_rates_activation_deadline,
           to set deadline for activation old(changed) rates
    - *If use feature flag make sure it disable before release,
        and create MR to move items from FEATURE_FLAG_CHANGED_RATES to DEPRECATED_RATES
         and update AccountRate "latest" properties, ex: latest_pro

    Check-list to add new rate (frontend):
     - Add new rate to AccountRate enum
     - Update LATEST_START_RATE, LATEST_PRO_RATE, ... constants
     - Update PRO_RATES_SET, ... constants
     - Update ACCOUNT_RATE_TITLE_MAP and ACCOUNT_RATE_SHORT_TITLE_MAP constant
     - Update ALL_START_RATES, ALL_PRO_RATES, ... constants
     - Update isRateDocumentsTerminate function
     - Update EDITABLE_RATES constant
     - Update SETTINGS_PAGE_WEB_RATES constant

    History of changes in rates:
     - https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/4952369
    """

    # Deprecated
    old = auto()

    start = auto()
    start_2022_01 = auto()
    start_2022_04 = auto()
    start_2022_08 = auto()
    start_2023_10 = auto()
    start_2024_10 = auto()

    pro_free = auto()
    pro = auto()  # Trial: "Безлімітний" in super admin menu
    pro_trial = auto()
    pro_2022 = auto()
    pro_2022_01 = auto()
    pro_plus = auto()
    pro_2021 = auto()
    pro_2022_04 = auto()
    pro_2022_12 = auto()
    pro_2023_07 = auto()
    pro_2023_10 = auto()
    pro_2024_10 = auto()

    # "ПРО+" as web rate was removed in October 2022
    # We keep them for backward compatibility
    pro_plus_trial = auto()
    pro_plus_2022_04 = auto()
    pro_plus_trial_2022 = auto()
    pro_plus_2022_12 = auto()  # Trial: "ПРО+" in super admin menu
    pro_plus_trial_2022_12 = auto()  # Trial: "Тестовий період (веб)"

    enterprise = auto()

    ultimate = auto()

    # Active (web)
    free = auto()
    pro_2025_04 = auto()
    start_2025_04 = auto()
    ultimate_2022_12 = auto()

    # Active (integration)
    integration = auto()
    integration_trial = auto()  # Trial: "Тестовий період (API)"

    # Active (archive)
    # Archive functionality has its own set of rates,
    # distinct from web/integration rates
    archive_small = auto()
    archive_big = auto()

    @property
    def priority_rank(self) -> int:
        priority_map = self.get_priority_map()
        rank = priority_map.get(self, 0)
        return rank

    @staticmethod
    def get_next_web_rate(rate: AccountRate) -> AccountRate:
        """
        Get the next WEB rate that has higher priority than the given rate.

        Only the latest rate returned because we always want to upgrade to the latest rate
        when recommending the next rate.
        """
        current_rank = rate.priority_rank

        next_rates = {
            rate: rank
            for rate, rank in PRIORITY_MAP_WEB_RATES.items()
            if rank > current_rank and rate.is_latest
        }

        if not next_rates:
            # fallback to the highest rate
            return AccountRate.latest_ultimate()

        # min_rate: tuple[rate, rank] = ...
        min_rate = min(next_rates.items(), key=lambda x: x[1])
        return min_rate[0]

    def get_priority_map(self) -> dict[AccountRate, int]:
        if self.is_web:
            return PRIORITY_MAP_WEB_RATES
        if self.is_integration:
            return PRIORITY_MAP_INTEGRATION_RATES
        if self.is_archive:
            return PRIORITY_MAP_ARCHIVE_RATES
        return {}

    @staticmethod
    def trials() -> list[AccountRate]:
        return [
            AccountRate.integration_trial,  # Trial: "Тестовий період (API)"
            AccountRate.pro_plus_trial_2022_12,  # Trial: "Тестовий період (веб)"
            AccountRate.pro_trial,
            AccountRate.pro_plus_trial,
            AccountRate.pro_plus_trial_2022,
        ]

    @staticmethod
    def web_trials() -> list[AccountRate]:
        return [
            AccountRate.pro_plus_trial_2022_12,  # Trial: "Тестовий період (веб)"
            AccountRate.pro_trial,
            AccountRate.pro_plus_trial,
            AccountRate.pro_plus_trial_2022,
        ]

    @staticmethod
    def free_rates() -> list[AccountRate]:
        return [
            AccountRate.free,
            *AccountRate.trials(),
        ]

    @staticmethod
    def integration_rates() -> tuple[AccountRate, ...]:
        return (AccountRate.integration,)

    @property
    def latest(self) -> AccountRate:
        # Unskip tests:
        # test_should_skip_bill_activation_due_to_changed_rates,
        # test_enable_new_rates_feature_flag, when use feature flag in latest_* method
        # Example MR:
        # https://gitlab.vchasno.com.ua/vchasno/edo/edo/-/merge_requests/10982
        if self.is_start:
            return self.latest_start()
        if self.is_ultimate:
            return self.latest_ultimate()
        if self.is_pro:
            return self.latest_pro()
        return self.latest_free()

    @property
    def is_latest(self) -> bool:
        return self == self.latest

    @property
    def is_free(self) -> bool:
        """
        The rate is from the "free" set of rates.

        Don't confuse this with rates for which the user doesn't pay anything. The word "free" is
        the actual name of the rate, not a property of the rate. For example, during "trials",
        the user doesn't have to pay anything, but these are not included in the "free" rates.
        """
        return self in FREE_RATES_SET

    @property
    def is_trial(self) -> bool:
        return self in self.trials()

    @property
    def is_start(self) -> bool:
        return self in START_RATES_SET

    @property
    def is_pro(self) -> bool:
        return self in PRO_ACCOUNT_RATES_SET

    @property
    def is_ultimate(self) -> bool:
        return self in ULTIMATE_RATES_SET

    def is_chargeless(self, is_fop: bool) -> bool:
        """Rate with unlimited documents in web interface."""
        from app.billing.rates import CHARGELESS_RATES

        if is_fop:
            return self in CHARGELESS_RATES.fop
        return self in CHARGELESS_RATES.tov

    @property
    def is_web(self) -> bool:
        """Rates that can be used for charging web documents."""
        return self in WEB_RATES_SET

    @property
    def is_integration(self) -> bool:
        """Rates that can be used for charging API documents."""
        return self in INTEGRATION_RATES_SET

    @property
    def is_archive(self) -> bool:
        return self in ARCHIVE_RATES_SET

    @property
    def is_archive_small(self) -> bool:
        return self == AccountRate.archive_small

    @property
    def is_archive_big(self) -> bool:
        return self == AccountRate.archive_big

    @property
    def is_pro_trial(self) -> bool:
        return self in PRO_TRIAL_RATES_SET

    @property
    def is_integration_trial(self) -> bool:
        """Trial rates that can be used for charging API documents."""
        return self.is_integration and self.is_trial

    @property
    def is_web_trial(self) -> bool:
        """Trial rates that can be used for charging web documents."""
        return self.is_web and self.is_trial

    @property
    def is_deprecated(self) -> bool:
        from app.billing.constants import DEPRECATED_RATES

        return self in DEPRECATED_RATES

    @staticmethod
    def latest_pro_plus_trial() -> AccountRate:
        return AccountRate.pro_plus_trial_2022_12

    @staticmethod
    def latest_integration_trial() -> AccountRate:
        return AccountRate.integration_trial

    @staticmethod
    def latest_integration() -> AccountRate:
        return AccountRate.integration

    @staticmethod
    def latest_start() -> AccountRate:
        return AccountRate.start_2025_04

    @staticmethod
    def latest_pro() -> AccountRate:
        return AccountRate.pro_2025_04

    @staticmethod
    def latest_pro_plus() -> AccountRate:
        return AccountRate.pro_plus_2022_12

    @staticmethod
    def latest_ultimate() -> AccountRate:
        return AccountRate.ultimate_2022_12

    @staticmethod
    def latest_free() -> AccountRate:
        return AccountRate.free


@unique
class BonusType(Enum):
    bonus = 'bonus'
    promo = 'promo'


@unique
class CompanyRateStatus(Enum):
    new = 'new'
    active = 'active'
    expired = 'expired'
    canceled = 'canceled'

    @property
    def is_delete(self) -> bool:
        return self in (self.expired, self.canceled)


AccountStatus = CompanyRateStatus


@unique
class ResourceType(Enum):
    bonus = 'bonus'
    debit = 'debit'


@unique
class TransactionType(Enum):
    invoice_payment = 'invoice_payment'
    invoice_payment_cancel = 'invoice_payment_cancel'
    debit_charge_off = 'debit_charge_off'
    bonus_income = 'bonus_income'
    bonus_cancel = 'bonus_cancel'
    bonus_charge_off = 'bonus_charge_off'
    credit_use = 'credit_use'
    credit_repay = 'credit_repay'  # not used yet
    debt_clearance = 'debt_clearance'  # not used yet
    cross_company = 'cross_company'  # not used yet
    rate_payment = 'rate_payment'
    rate_payment_cancel = 'rate_payment_cancel'

    @property
    def with_operator(self) -> bool:
        return self.value in TRANSACTION_TYPES_WITH_OPERATOR_SET

    @property
    def with_amount(self) -> bool:
        return self.value in TRANSACTION_TYPES_WITH_AMOUNT_SET

    @property
    def with_units(self) -> bool:
        return self.value in TRANSACTION_TYPES_WITH_UNITS_SET

    @property
    def is_charge(self) -> bool:
        return self in {
            self.debit_charge_off,
            self.bonus_charge_off,
        }


class RateExtensionType(NamedEnum):
    employees = auto()


class RateExtensionStatus(NamedEnum):
    active = auto()
    active_trial = auto()

    # extension is waiting for payment
    pending = auto()

    # extension paid and waiting for activation by date
    wait_activation_date = auto()

    # deactivated because user didn't pay for extension
    deactivated_trial = auto()

    # deactivated because the parent rate was deactivated
    deactivated_rate = auto()

    # canceled for some reason like user requested another extension
    # that is not compatible with this one
    canceled = auto()

    @property
    def is_deactivated(self) -> bool:
        return self in {self.deactivated_trial, self.deactivated_rate, self.canceled}


FREE_RATES_SET = {AccountRate.free}


START_RATES_SET = {
    AccountRate.start,
    AccountRate.start_2022_01,
    AccountRate.start_2022_04,
    AccountRate.start_2022_08,
    AccountRate.start_2023_10,
    AccountRate.start_2024_10,
    AccountRate.start_2025_04,
}

# TODO: refactor this. Deprecated, but still used {{
PRO_ACCOUNT_RATES_SET = {
    AccountRate.pro,
    AccountRate.pro_trial,
    AccountRate.pro_2021,
    AccountRate.pro_2022,
    AccountRate.pro_2022_01,
    AccountRate.pro_2022_04,
    AccountRate.pro_2022_12,
    AccountRate.pro_2023_07,
    AccountRate.pro_2023_10,
    AccountRate.pro_2024_10,
    AccountRate.pro_2025_04,
    AccountRate.pro_plus,
    AccountRate.pro_plus_2022_04,
    AccountRate.pro_plus_2022_12,
    AccountRate.pro_plus_trial,
    AccountRate.pro_plus_trial_2022,
    AccountRate.pro_plus_trial_2022_12,
}

ULTIMATE_RATES_SET = {AccountRate.ultimate, AccountRate.ultimate_2022_12}


PRO_TRIAL_RATES_SET = {
    AccountRate.pro_trial,
    AccountRate.pro_plus_trial,
    AccountRate.pro_plus_trial_2022,
    AccountRate.pro_plus_trial_2022_12,
}

WEB_RATES_SET = {
    AccountRate.old,
    AccountRate.free,
    AccountRate.start,
    AccountRate.start_2022_01,
    AccountRate.start_2022_04,
    AccountRate.start_2022_08,
    AccountRate.start_2023_10,
    AccountRate.start_2024_10,
    AccountRate.start_2025_04,
    AccountRate.pro,
    AccountRate.pro_free,
    AccountRate.pro_plus_trial,
    AccountRate.pro_trial,
    AccountRate.pro_2021,
    AccountRate.pro_2022,
    AccountRate.pro_2022_01,
    AccountRate.pro_2022_04,
    AccountRate.pro_2022_12,
    AccountRate.pro_2023_07,
    AccountRate.pro_2023_10,
    AccountRate.pro_2024_10,
    AccountRate.pro_2025_04,
    AccountRate.pro_plus,
    AccountRate.pro_plus_2022_04,
    AccountRate.pro_plus_2022_12,
    AccountRate.pro_plus_trial_2022,
    AccountRate.pro_plus_trial_2022_12,
    AccountRate.ultimate,
    AccountRate.ultimate_2022_12,
    AccountRate.enterprise,
}


ARCHIVE_RATES_SET = {
    AccountRate.archive_small,
    AccountRate.archive_big,
}

INTEGRATION_RATES_SET = {
    AccountRate.old,
    AccountRate.integration,
    AccountRate.integration_trial,
}
# }}


# Priority map for web rates
# NOTE: do not add rates from archive or integration sets
PRIORITY_MAP_WEB_RATES: dict[AccountRate, int] = {
    # Trial rates
    AccountRate.pro_free: 0,
    AccountRate.pro_plus_trial_2022: 0,
    AccountRate.pro_plus_trial: 0,
    AccountRate.pro_plus_trial_2022_12: 0,
    AccountRate.pro_trial: 0,
    AccountRate.old: 0,
    # free rates
    AccountRate.free: 1,
    # start rates
    AccountRate.start: 2,
    AccountRate.start_2022_01: 2,
    AccountRate.start_2022_04: 2,
    AccountRate.start_2022_08: 2,
    AccountRate.start_2023_10: 2,
    AccountRate.start_2024_10: 2,
    AccountRate.start_2025_04: 2,
    # pro rates
    AccountRate.pro_plus: 3,
    AccountRate.pro_plus_2022_04: 3,
    AccountRate.pro_plus_2022_12: 3,
    AccountRate.pro: 3,
    AccountRate.pro_2021: 3,
    AccountRate.pro_2022: 3,
    AccountRate.pro_2022_01: 3,
    AccountRate.pro_2022_04: 3,
    AccountRate.pro_2022_12: 3,
    AccountRate.pro_2023_07: 3,
    AccountRate.pro_2023_10: 3,
    AccountRate.pro_2024_10: 3,
    AccountRate.pro_2025_04: 3,
    # ultimate rates
    AccountRate.ultimate: 4,
    AccountRate.ultimate_2022_12: 4,
    AccountRate.enterprise: 4,
}


# Priority map for integration rates
PRIORITY_MAP_INTEGRATION_RATES: dict[AccountRate, int] = {
    # Trial rates
    AccountRate.integration_trial: 0,
    AccountRate.old: 0,
    # integration rates
    AccountRate.integration: 1,
}

PRIORITY_MAP_ARCHIVE_RATES: dict[AccountRate, int] = {
    # Archive rates
    AccountRate.archive_small: 1,
    AccountRate.archive_big: 1,
}


class BillServicesType(NamedEnum):
    """
    Type of "services" field in the bill.

    Bill can be highly configured by "services" field, but we artificially restrict
    the number of possible variations to have a clear understanding of what we are
    storing in that field. It also helps to convert the bill to a CRM friendly format that
    doesn't have such concepts as "services" and use flat structure instead.
    """

    rate = auto()  # web, integration or archive rates
    integration_and_documents = auto()
    documents = auto()
    add_employee = auto()
    web_and_archive = auto()

    @property
    def is_add_employee(self) -> bool:
        return self == BillServicesType.add_employee

    @property
    def is_web_and_archive(self) -> bool:
        return self == BillServicesType.web_and_archive


class BillServiceType(NamedEnum):
    rate = auto()
    extension = auto()
    units = auto()


@unique
class CompanyLimit(NamedEnum):
    """
    Represents keys in company config that are used to
    limit the usage of the system
    """

    # None - unlimited, any value instead of 0 has enabled
    additional_fields = 'max_additional_fields_count'
    employees = 'max_employees_count'
    # maximum number of documents that a company can view
    documents = 'max_documents_count'
    tags = 'max_tags_count'
    automation = 'max_automation_count'
    required_fields = 'max_required_fields_count'

    # represents how many versions each document can have
    max_versions_count = 'max_versions_count'

    # How many documents are visible to the company in the archive?
    max_archive_documents_count = 'max_archive_documents_count'


@unique
class CompanyPermission(NamedEnum):
    api = 'api_enabled'
    enforce_2fa = 'can_enforce_2fa'
    external_comments = 'external_comments_enabled'
    internal_comments = 'internal_comments_enabled'
    internal_documents = 'internal_document_enabled'
    manage_employee_access = 'can_manage_employee_access'
    reviews = 'reviews_enabled'

    tags_enabled = 'tags_enabled'
    allow_ordered_reviews = 'allow_ordered_reviews'
    document_templates_enabled = 'document_templates_enabled'
    custom_documents_fields_enabled = 'custom_documents_fields_enabled'

    @property
    def is_obsolete(self) -> bool:
        # these are permissions used by old pro rate
        # they aren't used in pro rates since June 2021
        #
        # TODO: remove it after every old pro rate is expired
        # UPD: January 2024:
        # The latest customer pro rate will expire in 2025-12-31 23:59:59+02 (НБУ).
        # Technically there are also few testings rate that will expire later, but they can be
        # changed to new rates.
        return self in COMPANY_PERMISSIONS_OBSOLETE_KEYS


COMPANY_PERMISSIONS_OBSOLETE_KEYS = (
    CompanyPermission.tags_enabled,
    CompanyPermission.allow_ordered_reviews,
    CompanyPermission.document_templates_enabled,
    CompanyPermission.custom_documents_fields_enabled,
)
COMPANY_PERMISSIONS_OBSOLETE_KEYS_VALUES = [
    permission.value for permission in COMPANY_PERMISSIONS_OBSOLETE_KEYS
]


class ChargelessRates(NamedTuple):
    tov: set[AccountRate]
    fop: set[AccountRate]
