import datetime
import uuid
from decimal import Decimal
from http import HTT<PERSON>tatus
from pathlib import Path
from unittest import mock
from unittest.mock import AsyncMock

import pytest
import sqlalchemy as sa
import ujson
from dateutil.relativedelta import relativedelta
from vchasno_crm.enums import CRMBillStatus, CRMBillType

from api.errors import InvalidRequest
from app.analytics.tables import analytics_events_table
from app.analytics.types import AnalyticsEventType, GoogleAnalyticsMeta
from app.auth.db import (
    company_statistic_table,
    select_company_by_id,
)
from app.billing.api import create_free_rate
from app.billing.constants import (
    BILL_REQUISITES,
    EVOPAY_STATUS_CODE_TO_DESCRIPTION_MAP,
    ROLE_COUNT_FREE_PRO,
    UNLIMITED,
    UPLOAD_RATE,
)
from app.billing.db import (
    _insert_account,
    insert_bill,
    insert_bonus,
    select_account_by_id,
    select_accounts,
    select_active_company_rates,
    select_bank_transaction,
    select_bill_by_id,
    select_billing_company,
    select_billing_company_config,
    select_bills_by_edrpous,
    select_bonus_by_key,
    select_companies_rates_for_graph,
    select_company_accounts,
    select_company_transactions,
    select_latest_payment_status_transaction_by_bill_id,
    select_payment_status_transactions_by_bill_id,
    select_planned_company_rates,
    select_rate_by_id,
    select_rate_extension_by_bill_id,
    select_service_account_id,
    update_billing_company_config,
)
from app.billing.enums import (
    AccountRate,
    AccountStatus,
    AccountType,
    BankEnum,
    BillingAccountSource,
    BillPaymentStatus,
    BillServicesType,
    BillSource,
    BillStatus,
    BonusType,
    CompanyLimit,
    CompanyPermission,
    CompanyRateStatus,
    RateExtensionStatus,
    RateExtensionType,
    TransactionType,
)
from app.billing.schemas import ActivateBillSuperAdmin
from app.billing.tables import billing_account_table
from app.billing.tests.constants import (
    TEST_ACCOUNT_CLIENT_BONUS,
    TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
    TEST_ACCOUNT_CLIENT_DEBIT,
    TEST_BONUS,
    TEST_CUSTOM_BONUS,
    TEST_DEBIT,
)
from app.billing.tests.test_billing_crm import BILL_ADD_EMPLOYEE_DATA, CRM_AUTH_TOKEN
from app.billing.tests.utils import (
    INTEGRATION_PER_DOCUMENT_PRICE,
    INTEGRATION_PER_DOCUMENT_PRICE_SUBUNITS,
    LATEST_PRO_RATE,
    LATEST_START_RATE,
    PRO_PRICE_FOP,
    PRO_PRICE_FOP_SUBUNITS,
    PRO_PRICE_TOV,
    PRO_PRICE_TOV_SUBUNITS,
    START_PRICE_FOP,
    START_PRICE_TOV,
    START_PRICE_TOV_SUBUNITS,
    get_company_rates,
)
from app.billing.types import (
    AddBillServiceRateOptions,
    Bill,
    BillingCompanyConfig,
    BillServiceExtension,
    BillServiceRate,
    BillServiceUnits,
)
from app.billing.utils import (
    _get_rates_limits,
    get_bill_service,
    get_billing_company_config,
    get_rate_end_date_from_start_date,
    get_rate_price,
)
from app.flags import FeatureFlags
from app.landing.tests.test_internal_landing_access import HEADER_KEY
from app.lib.datetime_utils import (
    LOCAL_TZ,
    end_of_day,
    local_now,
    parse_raw_iso_datetime,
    soft_isoformat,
    to_local_datetime,
    to_utc_datetime,
    utc_now,
)
from app.lib.validators import validate_pydantic
from app.models import select_one
from app.services import services
from app.tests import common
from app.tests.common import (
    API_V2_ADD_TRIAL_RATE_URL,
    BILL_ACTIVATE_URL,
    BILLS_URL,
    CRM_BILLS_URL,
    EVOPAY_PAGE_GENERATION_URL,
    EVOPAY_WEBHOOK_URL,
    FOP_EDRPOU,
    SUPER_ADMIN_EDRPOU,
    TEST_BANK_TRANSACTION_ID_1,
    TEST_COMPANY_EDRPOU,
    TEST_UPLOAD_LEFT_COUNTER,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    cleanup_on_teardown,
    login,
    prepare_add_bill_options,
    prepare_auth_headers,
    prepare_bill,
    prepare_billing_account,
    prepare_client,
    prepare_company_data,
    prepare_referer_headers,
    prepare_user_data,
    select_bank_transactions_by_transaction_ids,
    set_company_config,
)
from worker.billing.jobs import _process_bank_transactions
from worker.billing.tests.test_bank_common import prepare_bank_transaction

RECOMMENDED_RATE_URL = '/internal-api/billing/recommended-rate'
BONUS_API_URL = '/internal-api/billing/bonuses'
DEBIT_API_URL = 'internal-api/billing/debit'
CREATE_COMPANY_RATE_URL = '/internal-api/billing/companies/rates'
UPDATE_COMPANY_RATE_URL = '/internal-api/billing/companies/rates/{rate_id}'
DELETE_COMPANY_RATE_URL = '/internal-api/billing/companies/rates/{rate_id}'
INCREASE_EMPLOYEE_AMOUNT_URL = '/internal-api/billing/companies/rates/{rate_id}/employee-amount'

COMPANY_EDRPOU_FOP = '**********'
COMPANY_EDRPOU_TOV = '********'

DATE_EXPIRED = soft_isoformat(end_of_day(utc_now() + datetime.timedelta(days=100)))
DATA_PATH = Path(__file__).parent / 'data'
FAKE_BONUS_KEY = 'fake_bonus_key'
FAKE_ID = str(uuid.uuid4())

TEST_UUID_1 = 'fcd6ea65-ba6b-4b72-9756-c5b241dd5d7d'
TEST_UUID_2 = '6a3bec47-ce5f-4f9b-ab98-3283ed0a82e5'

COMPANY_ID_1 = '********-0000-0000-0000-************'
COMPANY_ID_2 = '********-0000-0000-0000-************'

RATE_ID_1 = '*************-0000-0000-************'
RATE_ID_2 = '*************-0000-0000-************'

TODAY_DATETIME = parse_raw_iso_datetime(local_now().date().isoformat())


TEST_INTEGRATION_RATE = {
    'company_id': COMPANY_ID_1,
    'rate': AccountRate.integration,
    'status': CompanyRateStatus.active,
    'amount': 0,
    'activation_date': '2020-12-20',
    'date_expired': '2080-12-20',
    'type': AccountType.client_rate,
}
TEST_PRO_RATE = {
    'company_id': COMPANY_ID_2,
    'rate': AccountRate.pro,
    'status': CompanyRateStatus.active,
    'amount': 0,
    'activation_date': '2020-12-20',
    'date_expired': '2080-12-20',
    'type': AccountType.client_rate,
}


TODAY_STR = local_now().strftime('%Y-%m-%d')


insert_company_rate = _insert_account


async def test_activate_custom_bonus(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')
    admin_role_id = super_admin.role_id
    company_id = user.company_id
    expected_date_expired = end_of_day(
        utc_now() + datetime.timedelta(days=TEST_CUSTOM_BONUS['period'])
    )

    async with app['db'].acquire() as conn:
        # Activate custom bonus
        data = {**TEST_CUSTOM_BONUS, 'company_id': company_id}
        response = await client.post(
            f'{BONUS_API_URL}/activate-custom',
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 200

        # Check created account
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 1

        account = accounts[0]
        assert account.company_id == company_id
        assert account.initiator_id == admin_role_id
        assert account.type == AccountType.client_bonus_custom
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == TEST_CUSTOM_BONUS['units']
        assert account.units_left == TEST_CUSTOM_BONUS['units']
        assert account.date_expired == expected_date_expired
        assert account.date_deleted is None

        scb_account_id = await select_service_account_id(conn, AccountType.service_credit_bonus)

        # Check created transaction
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 1
        transaction = transactions[0]
        assert transaction.from_ == scb_account_id
        assert transaction.to_ == account.id
        assert transaction.operator_id == admin_role_id
        assert transaction.initiator_id == admin_role_id
        assert transaction.type == TransactionType.bonus_income
        assert transaction.amount == 0
        assert transaction.units == TEST_CUSTOM_BONUS['units']
        assert transaction.comment == TEST_CUSTOM_BONUS['comment']

        # Check company upload documents left counter
        company = await select_company_by_id(conn, company_id)
        expected = TEST_UPLOAD_LEFT_COUNTER + (TEST_CUSTOM_BONUS['units'] * UPLOAD_RATE)
        assert company.upload_documents_left == expected


async def test_activate_custom_bonuses_from_file(aiohttp_client):
    app, client, admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )
    user_one = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000001',
        with_upload_documents_left=False,
    )
    user_two = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000002',
        with_upload_documents_left=False,
    )
    user_three = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000003',
        with_upload_documents_left=False,
    )
    admin_role_id = admin['role_id']
    csv_file = open(DATA_PATH / 'activate_custom_bonuses.csv', 'rb')

    try:
        response = await client.post(
            '/internal-api/billing/bonuses/mass-activate-custom',
            data=[('file', csv_file)],
            headers=prepare_auth_headers(admin),
        )
        assert response.status == 200

        assert await response.json() == {
            'total': 10,
            'duplicated': 1,
            'invalid': 6,
            'invalid_row_numbers': [5, 6, 7, 8, 9, 10],
            'non_existent_edrpous': ['********'],
            'processed': 2,
            'with_errors': 0,
        }

        # Check first company
        company_id = user_one['company_id']
        async with app['db'].acquire() as conn:
            scb_account_id = await select_service_account_id(conn, AccountType.service_credit_bonus)
            accounts = await select_company_accounts(conn, company_id)
            transactions = await select_company_transactions(conn, company_id)
            company = await select_company_by_id(conn, company_id)

        assert len(accounts) == 1
        assert len(transactions) == 1
        assert company.upload_documents_left == 90 * UPLOAD_RATE

        account = accounts[0]
        assert account.company_id == company_id
        assert account.initiator_id == admin_role_id
        assert account.type == AccountType.client_bonus_custom
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == 90
        assert account.units_left == 90
        assert account.date_expired == end_of_day(utc_now() + datetime.timedelta(days=120))
        assert account.date_deleted is None

        transaction = transactions[0]
        assert transaction.from_ == scb_account_id
        assert transaction.to_ == account.id
        assert transaction.operator_id == admin_role_id
        assert transaction.initiator_id == admin_role_id
        assert transaction.type == TransactionType.bonus_income
        assert transaction.amount == 0
        assert transaction.units == 90
        assert transaction.comment is None

        # Check second company
        company_id = user_two['company_id']
        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, company_id)
            transactions = await select_company_transactions(conn, company_id)
            company = await select_company_by_id(conn, company_id)

        assert len(accounts) == 1
        assert len(transactions) == 1
        assert company.upload_documents_left == 30 * UPLOAD_RATE

        account = accounts[0]
        assert account.company_id == company_id
        assert account.initiator_id == admin_role_id
        assert account.type == AccountType.client_bonus_custom
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == 30
        assert account.units_left == 30
        assert account.date_expired == end_of_day(utc_now() + datetime.timedelta(days=60))
        assert account.date_deleted is None

        transaction = transactions[0]
        assert transaction.from_ == scb_account_id
        assert transaction.to_ == account.id
        assert transaction.operator_id == admin_role_id
        assert transaction.initiator_id == admin_role_id
        assert transaction.type == TransactionType.bonus_income
        assert transaction.amount == 0
        assert transaction.units == 30
        assert transaction.comment is None

        # Check third company
        async with app['db'].acquire() as conn:
            company_id = user_three['company_id']
            accounts = await select_company_accounts(conn, company_id)
            transactions = await select_company_transactions(conn, company_id)
            company = await select_company_by_id(conn, company_id)

            assert len(accounts) == 0
            assert len(transactions) == 0
            assert company.upload_documents_left == 0

    finally:
        if not csv_file.closed:
            csv_file.close()


async def test_activate_custom_bonus_by_regular_user(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    try:
        response = await client.post(
            f'{BONUS_API_URL}/activate-custom',
            data=ujson.dumps(TEST_CUSTOM_BONUS),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'data, status, error_code, error_details',
    [
        (
            {'company_id': FAKE_ID},
            404,
            'object_does_not_exist',
            {'id': FAKE_ID, 'type': 'company', 'type_label': 'Компанія'},
        ),
        (
            {'units': 0, 'period': 0},
            400,
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
        (
            {'units': -1, 'period': -1},
            400,
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
    ],
)
async def test_activate_custom_bonus_invalid_data(
    aiohttp_client, data, status, error_code, error_details
):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        bonus_data = {**TEST_CUSTOM_BONUS, 'company_id': super_admin.company_id, **data}
        response = await client.post(
            f'{BONUS_API_URL}/activate-custom',
            data=ujson.dumps(bonus_data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == status
        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


async def test_activate_debit(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')
    admin_role_id = super_admin.role_id
    company_id = user.company_id

    async with app['db'].acquire() as conn:
        # Activate debit account
        data = {**TEST_DEBIT, 'company_id': company_id}
        response = await client.post(
            f'{DEBIT_API_URL}/activate',
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 200

        # Check created account
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 1
        account = accounts[0]
        assert account.company_id == company_id
        assert account.type == AccountType.client_debit
        assert account.amount == TEST_DEBIT['amount']
        assert account.amount_left == TEST_DEBIT['amount']
        assert account.units == TEST_CUSTOM_BONUS['units']
        assert account.units_left == TEST_CUSTOM_BONUS['units']
        assert account.date_expired is None
        assert account.date_deleted is None

        sce_account_id = await select_service_account_id(conn, AccountType.service_credit_external)

        # Check created transaction
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 1
        transaction = transactions[0]
        assert transaction.from_ == sce_account_id
        assert transaction.to_ == account.id
        assert transaction.operator_id == admin_role_id
        assert transaction.initiator_id == admin_role_id
        assert transaction.type == TransactionType.invoice_payment
        assert transaction.amount == TEST_DEBIT['amount']
        assert transaction.units == TEST_DEBIT['units']
        assert transaction.comment == TEST_DEBIT['comment']

        # Check company upload documents left counter
        company = await select_company_by_id(conn, company_id)
        expected = TEST_UPLOAD_LEFT_COUNTER + (TEST_CUSTOM_BONUS['units'] * UPLOAD_RATE)
        assert company.upload_documents_left == expected


@pytest.mark.parametrize(
    'is_admin, sa_perms, expected_code',
    [
        (True, {}, HTTPStatus.FORBIDDEN),
        (False, {}, HTTPStatus.FORBIDDEN),
        (False, {'can_edit_client_data': True}, HTTPStatus.OK),
    ],
)
async def test_activate_debit_access(aiohttp_client, is_admin, sa_perms, expected_code):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=is_admin,
        super_admin_permissions=sa_perms,
    )
    company_id = user.company_id
    await set_company_config(app, company_id=company_id, master=True, admin_is_superadmin=True)

    response = await client.post(
        f'{DEBIT_API_URL}/activate',
        json={**TEST_DEBIT, 'company_id': company_id},
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected_code


@pytest.mark.parametrize(
    'data, status, error_code, error_details',
    [
        (
            {'company_id': FAKE_ID},
            404,
            'object_does_not_exist',
            {'id': FAKE_ID, 'type': 'company', 'type_label': 'Компанія'},
        ),
        (
            {'units': 0, 'amount': 0},
            400,
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'amount': 'value should be greater than 0',
            },
        ),
        (
            {'units': -1, 'amount': -1},
            400,
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'amount': 'value should be greater than 0',
            },
        ),
    ],
)
async def test_activate_debit_invalid_data(aiohttp_client, data, status, error_code, error_details):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        debit_data = {**TEST_DEBIT, 'company_id': super_admin.company_id, **data}
        response = await client.post(
            f'{DEBIT_API_URL}/activate',
            data=ujson.dumps(debit_data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == status
        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'user_data, bill_data, expected',
    [
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [{'type': 'units', 'units': 100}],
            },
            {
                'services_type': BillServicesType.documents,
                'services': [
                    BillServiceUnits(
                        units=100,
                        unit_price=Decimal(INTEGRATION_PER_DOCUMENT_PRICE_SUBUNITS),
                    ),
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': INTEGRATION_PER_DOCUMENT_PRICE * 100,  # в гривнях
                'crm_type': CRMBillType.documents.value,
                'crm_documents_count': 100,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,  #  в копійках
                'crm_amount': INTEGRATION_PER_DOCUMENT_PRICE * 100,
                'crm_additional_rate': None,
                'crm_service_details': 'Документи',
            },
            id='buy_documents_1',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {'services': [{'type': 'units', 'units': 400, 'unit_price': '400'}]},
            {
                'services_type': BillServicesType.documents,
                'services': [
                    BillServiceUnits(
                        units=400,
                        # price document from request is ignored
                        unit_price=Decimal(INTEGRATION_PER_DOCUMENT_PRICE_SUBUNITS),
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': INTEGRATION_PER_DOCUMENT_PRICE * 400,  # в гривнях
                'crm_type': CRMBillType.documents.value,
                'crm_documents_count': 400,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,  # в копійках
                'crm_amount': INTEGRATION_PER_DOCUMENT_PRICE * 400,
                'crm_additional_rate': None,
                'crm_service_details': 'Документи',
            },
            id='buy_documents_2',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {
                'services_type': BillServicesType.rate,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal(START_PRICE_TOV_SUBUNITS),  # в копійках
                        rate=LATEST_START_RATE,
                        date_from=TODAY_DATETIME,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': START_PRICE_TOV,  # в гривнях
                'crm_type': CRMBillType.start_tov.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': START_PRICE_TOV,
                'crm_additional_rate': None,
                'crm_service_details': 'Старт',
            },
            id='buy_start_rate',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ],
                'is_card_payment': True,
            },
            {
                'services_type': BillServicesType.rate,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal(START_PRICE_TOV_SUBUNITS),  # в копійках
                        rate=LATEST_START_RATE,
                        date_from=TODAY_DATETIME,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.skipped,
                'response_total_price': START_PRICE_TOV,  # в гривнях
                'crm_type': CRMBillType.start_tov.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': START_PRICE_TOV,
                'crm_additional_rate': None,
                'crm_service_details': 'Старт',
            },
            id='buy_start_rate_card_payment',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': (local_now() + datetime.timedelta(weeks=1000)).strftime(
                            '%Y-%m-%d'
                        ),
                    }
                ],
            },
            {
                'services_type': BillServicesType.rate,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal(PRO_PRICE_TOV_SUBUNITS),  # в копійках
                        rate=LATEST_PRO_RATE,
                        date_from=TODAY_DATETIME + relativedelta(weeks=1000),
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': PRO_PRICE_TOV,  # в гривнях
                'crm_type': CRMBillType.pro_tov.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': PRO_PRICE_TOV,
                'crm_additional_rate': None,
                'crm_service_details': 'Професійний',
            },
            id='buy_pro_rate',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_FOP,
            },
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': (local_now() + datetime.timedelta(weeks=1000)).strftime(
                            '%Y-%m-%d'
                        ),
                    }
                ],
            },
            {
                'services_type': BillServicesType.rate,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal(PRO_PRICE_FOP_SUBUNITS),  # в копійках
                        rate=LATEST_PRO_RATE,
                        date_from=TODAY_DATETIME + relativedelta(weeks=1000),
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': PRO_PRICE_FOP,  # в гривнях
                'crm_type': CRMBillType.pro_fop.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': PRO_PRICE_FOP,
                'crm_additional_rate': None,
                'crm_service_details': 'Професійний',
            },
            id='buy_pro_rate_fop',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [{'type': 'rate', 'rate': AccountRate.archive_small.value}],
            },
            {
                'services_type': BillServicesType.rate,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal('48000'),  # в копійках
                        rate=AccountRate.archive_small,
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': 480.0,  # в гривнях
                'crm_type': CRMBillType.archive_small.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': 480.0,
                'crm_additional_rate': None,
                'crm_service_details': 'Архів +1000',
            },
            id='buy_archive_small_rate',
        ),
        pytest.param(
            {
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            {
                'services': [
                    {'type': 'rate', 'rate': AccountRate.latest_start().value, 'date_from': None},
                    {'type': 'rate', 'rate': AccountRate.archive_small.value},
                ],
            },
            {
                'services_type': BillServicesType.web_and_archive,
                'services': [
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal(START_PRICE_TOV_SUBUNITS),  # в копійках
                        rate=LATEST_START_RATE,
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    ),
                    BillServiceRate(
                        units=1,
                        unit_price=Decimal('48000'),  # в копійках
                        rate=AccountRate.archive_small,
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    ),
                ],
                'custom_price': None,
                'status_id': BillStatus.requested,
                'response_total_price': 480.0 + START_PRICE_TOV,  # в гривнях
                'crm_type': CRMBillType.start_tov.value,
                'crm_documents_count': None,
                'crm_documents_cost': INTEGRATION_PER_DOCUMENT_PRICE,
                'crm_amount': 480.0 + START_PRICE_TOV,
                'crm_additional_rate': CRMBillType.archive_small.value,
                'crm_service_details': 'Старт',
            },
            id='buy_archive_small_rate_and_web',
        ),
    ],
)
async def test_add_bill(
    aiohttp_client,
    monkeypatch,
    user_data: dict,
    bill_data: dict,
    expected: dict,
    crm_box: list[dict],
    test_flags,
):
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True

    app, client, user = await prepare_client(
        aiohttp_client,
        **user_data,
        enable_pro_functionality=False,
    )

    headers = prepare_auth_headers(user)
    bill_data = {
        **bill_data,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(BILLS_URL, json=bill_data, headers=headers)
    assert response.status == 201
    resp = await response.json()

    async with app['db'].acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])

    assert isinstance(bill, Bill)
    assert bill.email == user.email
    assert bill.edrpou == user.company_edrpou
    assert bill.is_legal is True
    assert bill.services_type == expected['services_type']
    assert bill.services == tuple(expected['services'])
    assert bill.custom_price == expected['custom_price']
    assert bill.custom_price == expected['custom_price']
    assert bill.agreement is None
    assert bill.source == BillSource.vchasno
    assert bill.user_id == user.id
    assert bill.role_id == user.role_id
    assert bill.status_id == expected['status_id']
    assert bill.payment_status is None

    assert resp['bill_id'] == bill.id_
    assert resp['number'] == bill.number
    assert resp['date'] == bill.date_iso
    assert resp['buyer_name'] == bill.name
    assert resp['supplier_name'] == BILL_REQUISITES.short_name
    assert resp['service'] == get_bill_service(bill)
    assert resp['supplier_edrpou'] == BILL_REQUISITES.edrpou
    assert resp['supplier_ipn'] == BILL_REQUISITES.ipn
    assert resp['supplier_bank'] == BILL_REQUISITES.bank
    assert resp['supplier_iban'] == BILL_REQUISITES.iban
    assert resp['amount'] == expected['response_total_price']

    assert len(crm_box) == 1
    assert crm_box[0]['type'] == expected['crm_type']
    assert crm_box[0]['documents_cost'] == expected['crm_documents_cost']
    assert crm_box[0].get('documents_count') == expected['crm_documents_count']
    assert crm_box[0]['amount'] == expected['crm_amount']
    assert crm_box[0].get('additional_rate') == expected['crm_additional_rate']
    assert crm_box[0]['service_details'] == expected['crm_service_details']


async def test_add_bill_with_google_analytics(aiohttp_client):
    """
    Given a user
    When creating bill web request with google analytics meta provided by the FE
    Expected to store google analytics successfully
    """

    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)
    google_analytics_data = GoogleAnalyticsMeta(
        client_id='TEST_CLIENT_ID',
        session_id='TEST_SESSION_ID',
        session_number='TEST_SESSION_NUMBER',
    )
    bill_data = {
        'services': [
            {
                'type': 'rate',
                'rate': AccountRate.latest_start().value,
                'date_from': TODAY_STR,
            }
        ],
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        **google_analytics_data.__dict__,
    }

    # Act
    response = await client.post(BILLS_URL, json=bill_data, headers=headers)

    # Assert
    assert response.status == 201
    resp = await response.json()
    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])
        analytics_meta = await select_one(
            conn=conn,
            query=(sa.select([analytics_events_table])),
        )

    assert bill is not None
    assert analytics_meta is not None
    assert analytics_meta.bill_id == bill.id
    assert analytics_meta.type == AnalyticsEventType.bill_generated
    assert analytics_meta.data == google_analytics_data.__dict__


async def test_create_add_employee_bill_by_user(aiohttp_client, crm_box, monkeypatch):
    """
    Create 2 bills from user cabinet
    First bill should be created with active_trial status
    Second bill should be created with pending status
    """
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    rate_price = Decimal(1000)  # в копійках
    async with app['db'].acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=rate_price,
                    rate=AccountRate.latest_ultimate(),
                    date_from=None,
                    limits_employees_count=10,
                    price_per_user=Decimal('48000'),
                )
            ],
            custom_price=rate_price,
        )
        bill_acc = await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            activation_date=local_now(),
            date_expired=local_now() + datetime.timedelta(days=364),
            billing_company_config={CompanyLimit.employees.value: 10},
            type=AccountType.client_rate,
            price_per_user=100052,  # 1000.52 UAH
            bill_id=bill.id_,
        )

    headers = prepare_auth_headers(user)
    bill_data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [
            {
                'type': 'extension',
                'units': 3,
                'extension': RateExtensionType.employees.value,
            }
        ],
    }
    response = await client.post(BILLS_URL, json=bill_data, headers=headers)
    assert response.status == 201
    resp = await response.json()
    assert resp['extension_status'] == RateExtensionStatus.active_trial.value

    async with app['db'].acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])
        extension = await select_rate_extension_by_bill_id(conn, resp['bill_id'])
    assert extension.status == RateExtensionStatus.active_trial
    assert extension.type == RateExtensionType.employees
    assert extension.planned_activation_date is None
    assert extension.account_id == bill_acc.id

    assert len(crm_box) == 1
    assert crm_box[0]['employees_count'] == 3
    assert bill.services_type == BillServicesType.add_employee
    assert bill.services == (
        BillServiceExtension(
            units=3,
            unit_price=Decimal('100052'),  # 1000.52 UAH
            extension=RateExtensionType.employees,
            date_from=None,
        ),
    )

    assert bill.agreement is None
    assert bill.source == BillSource.vchasno
    assert bill.user_id == user.id
    assert bill.role_id == user.role_id
    assert resp['amount'] == 3001.56  # 1000.52 * 3

    assert resp['bill_id'] == bill.id_
    assert resp['number'] == bill.number
    assert resp['date'] == bill.date_iso
    assert resp['buyer_name'] == bill.name
    assert resp['supplier_name'] == BILL_REQUISITES.short_name
    assert resp['service'] == get_bill_service(bill)
    assert resp['supplier_edrpou'] == BILL_REQUISITES.edrpou
    assert resp['supplier_ipn'] == BILL_REQUISITES.ipn
    assert resp['supplier_bank'] == BILL_REQUISITES.bank
    assert resp['supplier_iban'] == BILL_REQUISITES.iban

    response = await client.post(BILLS_URL, json=bill_data, headers=headers)
    assert response.status == 201
    resp = await response.json()
    assert resp['extension_status'] == RateExtensionStatus.pending.value


@pytest.mark.parametrize(
    'data',
    [
        {'rate': 'pro_2023_07'},
        {'rate': 'start_2022_08'},
    ],
)
async def test_add_bill_for_deprecated_rate(aiohttp_client, data):
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
    )
    headers = prepare_auth_headers(user)
    bill_data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [
            {
                'type': 'rate',
                'rate': data['rate'],
            }
        ],
    }
    response = await client.post(BILLS_URL, data=ujson.dumps(bill_data), headers=headers)
    assert response.status == 400
    response_json = await response.json()
    assert response_json['reason'] == 'Тариф є застарілим.'


async def test_add_bill_by_logged_in_user(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    headers = prepare_referer_headers(client)
    data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [{'type': 'units', 'units': 100}],
    }
    await login(client, user.email, TEST_USER_PASSWORD)

    response = await client.post(BILLS_URL, data=ujson.dumps(data), headers=headers)
    assert response.status == 201
    data = await response.json()

    async with app['db'].acquire() as conn:
        bill = await select_bill_by_id(conn, data['bill_id'])
        assert isinstance(bill, Bill)
        assert bill.email == user.email
        assert bill.edrpou == user.company_edrpou
        assert bill.is_legal is True
        assert bill.services_type == BillServicesType.documents
        assert bill.services == (
            BillServiceUnits(
                units=100,
                unit_price=Decimal(INTEGRATION_PER_DOCUMENT_PRICE_SUBUNITS),
            ),
        )
        assert bill.agreement is None
        assert bill.source == BillSource.vchasno
        assert bill.user_id == user.id
        assert bill.role_id == user.role_id


@pytest.mark.parametrize(
    'prepare_rate, status, bill_rate, expected_error',
    [
        [
            AccountRate.latest_start(),
            AccountStatus.active,
            AccountRate.latest_start(),
            'Не можна подовжувати поточний тариф раніше ніж за 45 днів до кінця',
        ],
        [
            AccountRate.latest_pro(),
            AccountStatus.active,
            AccountRate.latest_start(),
            'Не можна оплачувати рахунок нижче за тарифом активного',
        ],
        [
            AccountRate.latest_start(),
            AccountStatus.new,
            AccountRate.latest_start(),
            'Не можна запланувати більш ніж один той самий тариф',
        ],
    ],
)
async def test_add_bill_not_logged_in_user_negative(
    aiohttp_client, prepare_rate, status, bill_rate, expected_error
):
    app, client, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            type=AccountType.client_rate,
            rate=prepare_rate,
            status=status,
            activation_date=local_now(),
            date_expired=local_now() + datetime.timedelta(days=365),
        )

    data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [{'type': 'rate', 'rate': bill_rate.value}],
    }
    response = await client.post(BILLS_URL, json=data, headers=prepare_referer_headers(client))
    assert response.status == 400
    data = await response.json()
    assert data['reason'] == expected_error


async def test_add_bonus(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        data = {**TEST_BONUS, 'date_expired': DATE_EXPIRED}
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201
        result = await response.json()

        async with app['db'].acquire() as conn:
            bonus = await select_bonus_by_key(conn, TEST_BONUS['key'])

        assert bonus.id == result['id']
        assert bonus.key == TEST_BONUS['key']
        assert bonus.title == TEST_BONUS['title']
        assert bonus.description == TEST_BONUS['description']
        assert bonus.type.value == TEST_BONUS['type']
        assert bonus.units == TEST_BONUS['units']
        assert bonus.period == TEST_BONUS['period']
        assert bonus.created_by == super_admin.role_id
        assert soft_isoformat(bonus.date_created) == result['date_created']
        assert soft_isoformat(bonus.date_expired) == DATE_EXPIRED
        assert bonus.date_deleted is None
    finally:
        await cleanup_on_teardown(app)


async def test_add_bonus_by_regular_user(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_add_bonus_existed(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == 'object_already_exists'
        assert result['details']['key'] == TEST_BONUS['key']
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'data, error_code, error_details',
    [
        (
            {'type': BonusType.bonus.value},
            'billing_bonus_type_not_allowed',
            {'type': BonusType.bonus.value},
        ),
        (
            {'units': 0, 'period': 0},
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
        (
            {'units': -1, 'period': -1},
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
    ],
)
async def test_add_bonus_invalid_data(aiohttp_client, data, error_code, error_details):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        bonus_data = {**TEST_BONUS, **data}
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(bonus_data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400
        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


async def test_cancel_bonus(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')
    admin_role_id = super_admin.role_id
    company_id = user.company_id
    delta_dt = datetime.timedelta(minutes=1)

    async with app['db'].acquire() as conn:
        scb_account_id = await select_service_account_id(conn, AccountType.service_credit_bonus)

        # Insert bonus accounts
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units': 50,
                'units_left': 50,
                'date_created': utc_now(),
            },
        )
        bonus_account_one_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units': 50,
                'units_left': 50,
                'date_created': utc_now() + delta_dt,
            },
        )
        bonus_account_two_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units': 100,
                'units_left': 100,
                'date_created': utc_now() + delta_dt * 2,
            },
        )
        custom_bonus_account_id = account.id

        # Insert debit account
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_created': utc_now() + delta_dt * 3,
            },
        )
        debit_account_id = account.id

        # Cancel bonuses
        data = {
            'company_id': company_id,
            'units': 80,
            'comment': 'Cancel 80 bonuses.',
        }
        response = await client.post(
            f'{BONUS_API_URL}/cancel',
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 200

        # Check accounts
        account = await select_account_by_id(conn, bonus_account_one_id)
        assert account.units_left == 0
        account = await select_account_by_id(conn, bonus_account_two_id)
        assert account.units_left == 20
        account = await select_account_by_id(conn, custom_bonus_account_id)
        assert account.units_left == 100
        account = await select_account_by_id(conn, debit_account_id)
        assert account.amount_left == TEST_ACCOUNT_CLIENT_DEBIT['amount']
        assert account.units_left == TEST_ACCOUNT_CLIENT_DEBIT['units']

        # Check transactions
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 2
        for tr in transactions:
            if tr.from_ == bonus_account_one_id:
                assert tr.units == 50
            elif tr.from_ == bonus_account_two_id:
                assert tr.units == 30
            else:
                assert 0, 'unexpected transaction'

            assert tr.to_ == scb_account_id
            assert tr.type == TransactionType.bonus_cancel
            assert tr.amount == 0
            assert tr.comment == 'Cancel 80 bonuses.'
            assert tr.operator_id == admin_role_id
            assert tr.initiator_id == admin_role_id


@pytest.mark.parametrize(
    'data, error_code, error_details',
    [
        ({'units': 20}, 'invalid_request', {'comment': 'is required'}),
        ({'comment': 'Cancel bonuses.'}, 'invalid_request', {'units': 'is required'}),
        (
            {'company_id': None, 'units': 20, 'comment': 'Cancel bonuses.'},
            'invalid_request',
            {'company_id': 'value is not a string'},
        ),
        (
            {'units': 31, 'comment': 'Cancel bonuses.'},
            'billing_not_enough_resources',
            None,
        ),
    ],
)
async def test_cancel_bonus_invalid_data(aiohttp_client, data, error_code, error_details):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    company_id = super_admin.company_id
    await set_company_config(app, company_id=company_id, master=True, admin_is_superadmin=True)

    try:
        # Insert bonus account
        async with app['db'].acquire() as conn:
            await _insert_account(
                conn,
                {
                    **TEST_ACCOUNT_CLIENT_BONUS,
                    'company_id': company_id,
                    'initiator_id': FAKE_ID,
                },
            )
        # Cancel bonuses
        response = await client.post(
            f'{BONUS_API_URL}/cancel',
            data=ujson.dumps({'company_id': company_id, **data}),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


async def test_cancel_debit(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')
    admin_role_id = super_admin.role_id
    company_id = user.company_id
    delta_dt = datetime.timedelta(minutes=1)

    async with app['db'].acquire() as conn:
        sce_account_id = await select_service_account_id(conn, AccountType.service_credit_external)

        # Insert debit accounts
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount': 1000,
                'amount_left': 1000,
                'units': 10,
                'units_left': 10,
                'date_created': utc_now(),
            },
        )
        debit_account_one_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount': 3000,
                'amount_left': 3000,
                'units': 50,
                'units_left': 50,
                'date_created': utc_now() + delta_dt,
            },
        )
        debit_account_two_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount': 9000,
                'amount_left': 9000,
                'units': 100,
                'units_left': 100,
                'date_created': utc_now() + delta_dt * 2,
            },
        )
        debit_account_three_id = account.id

        # Insert bonus account
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_created': utc_now() + delta_dt * 3,
            },
        )
        bonus_account_id = account.id

        # Cancel documents
        data = {
            'company_id': company_id,
            'units': 30,
            'comment': 'Cancel 30 documents.',
        }
        response = await client.post(
            f'{DEBIT_API_URL}/cancel',
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 200

        # Check accounts
        account = await select_account_by_id(conn, debit_account_one_id)
        assert account.amount_left == 0
        assert account.units_left == 0
        account = await select_account_by_id(conn, debit_account_two_id)
        assert account.amount_left == 1800
        assert account.units_left == 30
        account = await select_account_by_id(conn, debit_account_three_id)
        assert account.amount_left == 9000
        assert account.units_left == 100
        account = await select_account_by_id(conn, bonus_account_id)
        assert account.amount_left == TEST_ACCOUNT_CLIENT_BONUS['amount']
        assert account.units_left == TEST_ACCOUNT_CLIENT_BONUS['units']

        # Check transactions
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 2
        for tr in transactions:
            assert tr.to_ == sce_account_id
            assert tr.type == TransactionType.invoice_payment_cancel
            assert tr.comment == 'Cancel 30 documents.'
            assert tr.operator_id == admin_role_id
            assert tr.initiator_id == admin_role_id

            if tr.from_ == debit_account_one_id:
                assert tr.amount == 1000
                assert tr.units == 10
            elif tr.from_ == debit_account_two_id:
                assert tr.amount == 1200
                assert tr.units == 20
            else:
                assert 0, 'unexpected transaction'


@pytest.mark.parametrize(
    'data, error_code, error_details',
    [
        ({'units': 30}, 'invalid_request', {'comment': 'is required'}),
        ({'comment': 'Cancel bonuses.'}, 'invalid_request', {'units': 'is required'}),
        (
            {'company_id': None, 'units': 30, 'comment': 'Cancel bonuses.'},
            'invalid_request',
            {'company_id': 'value is not a string'},
        ),
        (
            {'units': 101, 'comment': 'Cancel bonuses.'},
            'billing_not_enough_resources',
            None,
        ),
    ],
)
async def test_cancel_debit_invalid_data(aiohttp_client, data, error_code, error_details):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    company_id = super_admin.company_id
    await set_company_config(app, company_id=company_id, master=True, admin_is_superadmin=True)

    try:
        # Insert debit account
        async with app['db'].acquire() as conn:
            await _insert_account(
                conn,
                {
                    **TEST_ACCOUNT_CLIENT_DEBIT,
                    'company_id': company_id,
                    'initiator_id': FAKE_ID,
                },
            )
        # Cancel bonuses
        response = await client.post(
            f'{BONUS_API_URL}/cancel',
            data=ujson.dumps({'company_id': company_id, **data}),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


async def test_delete_bonus(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        result = await response.json()
        delete_id = result['id']
        response = await client.delete(
            f'{BONUS_API_URL}/{delete_id}', headers=prepare_auth_headers(super_admin)
        )
        assert response.status == 204

        async with app['db'].acquire() as conn:
            bonus = await select_bonus_by_key(conn, TEST_BONUS['key'])
            assert bonus.date_deleted is not None
    finally:
        await cleanup_on_teardown(app)


async def test_delete_bonus_by_regular_user(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        result = await response.json()
        delete_id = result['id']
        response = await client.delete(
            f'{BONUS_API_URL}/{delete_id}', headers=prepare_auth_headers(user)
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_delete_bonus_not_existed(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        response = await client.delete(
            f'{BONUS_API_URL}/{FAKE_ID}', headers=prepare_auth_headers(super_admin)
        )
        assert response.status == 404
        result = await response.json()
        assert result['code'] == 'object_does_not_exist'
    finally:
        await cleanup_on_teardown(app)


async def test_delete_bonus_wrong_type(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        async with app['db'].acquire() as conn:
            bonus = await insert_bonus(conn, {**TEST_BONUS, 'type': BonusType.bonus.value})

        response = await client.delete(
            f'{BONUS_API_URL}/{bonus.id}', headers=prepare_auth_headers(super_admin)
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == 'billing_bonus_type_not_allowed'
        assert result['details']['type'] == BonusType.bonus.value
    finally:
        await cleanup_on_teardown(app)


async def test_update_bonus(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        data = {**TEST_BONUS, 'date_expired': DATE_EXPIRED}
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        result = await response.json()
        update_id = result['id']
        bonus_data = {
            'type': result['type'],
            'created_by': result['created_by'],
            'date_created': result['date_created'],
            'date_expired': result['date_expired'],
            'date_deleted': result['date_deleted'],
        }
        update_data = {
            'key': 'test_promo_2',
            'title': 'Test Promo 2',
            'units': 100,
            'period': 90,
            'description': 'This is a test promo 2',
        }
        response = await client.patch(
            f'{BONUS_API_URL}/{update_id}',
            data=ujson.dumps(update_data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 200

        result = await response.json()
        assert result['id'] == update_id
        assert result['key'] == update_data['key']
        assert result['title'] == update_data['title']
        assert result['type'] == bonus_data['type']
        assert result['units'] == update_data['units']
        assert result['period'] == update_data['period']
        assert result['created_by'] == bonus_data['created_by']
        assert result['description'] == update_data['description']
        assert result['date_created'] == bonus_data['date_created']
        assert result['date_expired'] == bonus_data['date_expired']
        assert result['date_deleted'] == bonus_data['date_deleted']
    finally:
        await cleanup_on_teardown(app)


async def test_update_bonus_by_regular_user(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    user = await prepare_user_data(app, email='<EMAIL>')

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        result = await response.json()
        update_id = result['id']
        response = await client.patch(
            f'{BONUS_API_URL}/{update_id}',
            data=ujson.dumps({'title': 'new_title'}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_update_bonus_existed(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps(TEST_BONUS),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        response = await client.post(
            BONUS_API_URL,
            data=ujson.dumps({**TEST_BONUS, 'key': 'bonus_key_2'}),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 201

        result = await response.json()
        update_id = result['id']
        response = await client.patch(
            f'{BONUS_API_URL}/{update_id}',
            data=ujson.dumps({'key': TEST_BONUS['key']}),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == 'object_already_exists'
        assert result['details']['key'] == TEST_BONUS['key']
    finally:
        await cleanup_on_teardown(app)


async def test_update_bonus_not_existed(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        response = await client.patch(
            f'{BONUS_API_URL}/{FAKE_ID}',
            data=ujson.dumps({'period': 90}),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 404
        result = await response.json()
        assert result['code'] == 'object_does_not_exist'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'init_data, update_data, error_code, error_details',
    [
        (
            {'type': BonusType.bonus.value},
            {'units': 100},
            'billing_bonus_type_not_allowed',
            {'type': BonusType.bonus.value},
        ),
        (
            {},
            {'units': 0, 'period': 0},
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
        (
            {},
            {'units': -1, 'period': -1},
            'invalid_request',
            {
                'units': 'value should be greater than 0',
                'period': 'value should be greater than 0',
            },
        ),
    ],
)
async def test_update_bonus_invalid_data(
    aiohttp_client, init_data, update_data, error_code, error_details
):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    try:
        async with app['db'].acquire() as conn:
            bonus = await insert_bonus(conn, {**TEST_BONUS, **init_data})

        response = await client.patch(
            f'{BONUS_API_URL}/{bonus.id}',
            data=ujson.dumps(update_data),
            headers=prepare_auth_headers(super_admin),
        )
        assert response.status == 400

        result = await response.json()
        assert result['code'] == error_code
        if error_details:
            assert result['details'] == error_details
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'data, expected',
    [
        pytest.param(
            {
                'rate': 'integration',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2019-12-19',
                'end_date': '3019-12-19',
            },
            {
                'rate': 'integration',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'status': CompanyRateStatus.active,
                'start_date': '2019-12-19',
                'end_date': '3019-12-19',
            },
            id='integration',
        ),
        pytest.param(
            {
                'rate': 'pro',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            {
                'rate': 'pro',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'status': CompanyRateStatus.active,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            id='pro_from_now',
        ),
        pytest.param(
            {
                'rate': 'pro',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2019-12-19',
                'end_date': '3019-12-19',
            },
            {
                'rate': 'pro',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'status': CompanyRateStatus.active,
                'start_date': '2019-12-19',
                'end_date': '3019-12-19',
            },
            id='pro_static_dates',
        ),
        pytest.param(
            {
                'rate': 'archive_small',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '3019-12-19',
                'end_date': '3019-12-20',
            },
            {
                'rate': 'archive_small',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'status': CompanyRateStatus.new,
                'start_date': '3019-12-19',
                'end_date': '3019-12-20',
            },
            id='archive_small',
        ),
    ],
)
async def test_add_company_rate(aiohttp_client, data, expected):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(
        app,
        email='<EMAIL>',
        company_id=TEST_UUID_1,
        enable_pro_functionality=False,
    )

    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with app['db'].acquire() as conn:
        rates = await select_companies_rates_for_graph(conn, [TEST_UUID_1])

    def _to_date_str(date):
        return to_local_datetime(date).strftime('%Y-%m-%d') if date else None

    assert len(rates) == 1
    rate = rates[0]
    assert rate.rate.value == expected['rate']
    assert rate.amount == expected['amount']
    assert rate.company_id == expected['company_id']
    assert rate.status == expected['status']
    assert _to_date_str(rate.start_date) == expected['start_date']
    assert _to_date_str(rate.end_date) == expected['end_date']


async def test_update_company_rate_config(aiohttp_client):
    data = {
        'rate': AccountRate.pro_plus_trial_2022_12.value,
        'amount': 2000,
        'company_id': TEST_UUID_1,
        'start_date': TODAY_STR,
        'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
    }

    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(
        app,
        email='<EMAIL>',
        company_id=TEST_UUID_1,
        enable_pro_functionality=False,
    )

    # Set "Web trial"
    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with app['db'].acquire() as conn:
        billing_company_config = await get_billing_company_config(conn, company_id=TEST_UUID_1)
        assert billing_company_config.reviews_enabled is True
        assert billing_company_config.internal_document_enabled is True

    # Set rate "Start" (assert company_config updated correctly)
    data.update({'rate': AccountRate.latest_start().value})
    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with app['db'].acquire() as conn:
        billing_company_config = await get_billing_company_config(conn, company_id=TEST_UUID_1)
        assert billing_company_config.reviews_enabled is False
        assert billing_company_config.internal_document_enabled is False


async def test_extend_current_active_rate(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(app, email='<EMAIL>', company_id=TEST_UUID_1)

    headers = prepare_auth_headers(super_admin)

    response = await client.post(
        path=CREATE_COMPANY_RATE_URL,
        json={
            'rate': 'integration',
            'amount': 3000,
            'company_id': TEST_UUID_1,
            'start_date': '2019-12-19',
            'end_date': '3019-12-19',
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED

    response = await client.post(
        path=CREATE_COMPANY_RATE_URL,
        json={
            'rate': 'integration',
            'amount': 3000,
            'company_id': TEST_UUID_1,
            'extend_active_rate': True,
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, TEST_UUID_1)
        planned_rates = await select_planned_company_rates(conn, TEST_UUID_1)

    active_rate = [rate for rate in active_rates if rate.rate == AccountRate.integration][0]
    planned_rate = [rate for rate in planned_rates if rate.rate == AccountRate.integration][0]

    assert planned_rate.start_date == active_rate.end_date


async def test_evopay_payment_plan_two_rates_validation(aiohttp_client, test_flags):
    """
    Can't plan 2 rates (evopay payment). [DOC-7409]
    """
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    app, client, user = await prepare_client(aiohttp_client)
    payload = {'payment': {'status': 4}, 'payload': services.config.evopay.evopay_webhook_key}

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            status=AccountStatus.active,
            type_=AccountType.client_rate,
            date_expired=local_now() + datetime.timedelta(days=5),
        )

    bill_data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [
            {
                'type': 'rate',
                'rate': AccountRate.latest_start().value,
            }
        ],
    }
    bills_ids = []
    for _ in range(2):
        response = await client.post(BILLS_URL, json=bill_data, headers=prepare_auth_headers(user))
        assert response.status == 201, await response.json()
        bill_id = (await response.json())['bill_id']
        bills_ids.append(bill_id)

    for bill_id in bills_ids:
        response = await client.post(
            path=EVOPAY_WEBHOOK_URL,
            json={**payload, 'project_order_id': bill_id},
        )
        assert response.status == 200

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, user.company_id)
        planned_rates = await select_planned_company_rates(conn, user.company_id)
        bills = await select_bills_by_edrpous(conn, [user.company_edrpou])
        transaction = await select_latest_payment_status_transaction_by_bill_id(conn, bills_ids[1])

    assert len(active_rates) == 2  # free, start
    assert len(planned_rates) == 1  # only first bill activated
    assert {bills[0].payment_status, bills[1].payment_status} == {None, BillPaymentStatus.completed}
    # second bill payment_status != completed, because we can't plan two rates,
    # but second transaction payment_status == completed,
    #   because transaction successfully processed
    assert transaction.payment_status == BillPaymentStatus.completed


@pytest.mark.parametrize(
    'data, expected_billing_config',
    [
        pytest.param(
            {
                'rate': 'integration',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            {
                CompanyPermission.api.value: True,
            },
            id='integration',
        ),
        pytest.param(
            {
                'rate': AccountRate.pro.value,
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            {
                CompanyPermission.custom_documents_fields_enabled.value: True,
                CompanyPermission.document_templates_enabled.value: True,
                CompanyPermission.internal_documents.value: True,
                CompanyPermission.reviews.value: True,
                CompanyPermission.allow_ordered_reviews.value: True,
                CompanyPermission.tags_enabled.value: True,
                CompanyLimit.additional_fields.value: None,
                CompanyLimit.employees.value: None,
                CompanyLimit.tags.value: None,
                CompanyLimit.automation.value: None,
                CompanyLimit.documents.value: None,
                CompanyLimit.required_fields.value: None,
                CompanyLimit.max_versions_count.value: 0,
                CompanyPermission.enforce_2fa.value: True,
                CompanyPermission.external_comments.value: True,
                CompanyPermission.internal_comments.value: True,
                CompanyPermission.manage_employee_access.value: True,
            },
            id='web',
        ),
        pytest.param(
            {
                'rate': AccountRate.archive_big,
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            {
                'max_archive_documents_count': 5000,
            },
            id='archive_big_1',
        ),
        pytest.param(
            {
                'rate': AccountRate.archive_small,
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': TODAY_STR,
                'end_date': (local_now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
            },
            {
                'max_archive_documents_count': 1000,
            },
            id='archive_big_2',
        ),
    ],
)
async def test_add_company_rate_update_config(
    aiohttp_client,
    data: dict,
    expected_billing_config: dict,
):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(
        app, email='<EMAIL>', company_id=TEST_UUID_1, enable_pro_functionality=False
    )

    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == HTTPStatus.CREATED, await response.json()

    async with app['db'].acquire() as conn:
        billing_company = await select_billing_company(conn, company_id=TEST_UUID_1)

    if expected_billing_config is None:
        assert billing_company is None
    else:
        assert billing_company is not None, 'Billing company config not found'
        assert billing_company.config == BillingCompanyConfig(**expected_billing_config)


@pytest.mark.parametrize(
    'existed_rates, data, expected',
    [
        pytest.param(
            [
                {
                    **TEST_INTEGRATION_RATE,
                    'company_id': TEST_UUID_1,
                    'activation_date': '2019-12-20',
                    'date_expired': '2019-12-22',
                },
            ],
            {
                'rate': 'integration',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2019-12-19',
                'end_date': '3019-12-24',
            },
            {
                'status': HTTPStatus.BAD_REQUEST,
                'error': {
                    'code': 'invalid_request',
                    'reason': 'Вже існує тариф, що пересікається по часу',
                    'details': {},
                },
            },
            id='integration_duplicated',
        ),
        pytest.param(
            [
                {
                    **TEST_PRO_RATE,
                    'company_id': TEST_UUID_1,
                    'activation_date': '2020-12-20',
                    'date_expired': '2020-12-30',
                },
            ],
            {
                'rate': 'pro',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2020-12-19',
                'end_date': '3020-12-30',
            },
            {
                'status': HTTPStatus.BAD_REQUEST,
                'error': {
                    'code': 'invalid_request',
                    'reason': 'Вже існує тариф, що пересікається по часу',
                    'details': {},
                },
            },
            id='pro_duplicated',
        ),
        pytest.param(
            [
                {
                    **TEST_PRO_RATE,
                    'company_id': TEST_UUID_1,
                    'activation_date': '2020-12-20',
                    'date_expired': '2020-12-30',
                },
            ],
            {
                'rate': 'integration',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2019-12-19',
                'end_date': '3019-12-24',
            },
            {'status': HTTPStatus.CREATED},
            id='integration_ok_with_web',
        ),
        pytest.param(
            [
                {
                    **TEST_PRO_RATE,
                    'company_id': TEST_UUID_1,
                    'activation_date': '2020-12-20',
                    'date_expired': '2020-12-30',
                },
            ],
            {
                'rate': 'archive_small',
                'amount': 2000,
                'company_id': TEST_UUID_1,
                'start_date': '2019-12-19',
                'end_date': '3019-12-24',
            },
            {'status': HTTPStatus.CREATED},
            id='integration_ok_with_archive',
        ),
    ],
)
async def test_add_company_duplication(
    aiohttp_client,
    existed_rates: list[dict],
    data: dict,
    expected: dict,
):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(app, email='<EMAIL>', company_id=TEST_UUID_1)

    async with app['db'].acquire() as conn:
        await insert_company_rate(conn, existed_rates)

    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == expected['status'], await response.json()
    if response.status != HTTPStatus.CREATED:
        response_data = await response.json()
        assert response_data == expected['error']


@pytest.mark.parametrize(
    'existed_rates, update_data, expected',
    [
        (
            [
                {
                    'id': TEST_UUID_1,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 0,
                    'activation_date': '2019-12-20',
                    'date_expired': '2019-12-20',
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.new,
                    'amount': 0,
                    'activation_date': '2020-12-20',
                    'date_expired': '2020-12-30',
                    'type': AccountType.client_rate,
                },
            ],
            {'id': TEST_UUID_1},
            {'amount': 0, 'start_date': '2019-12-20', 'end_date': '2019-12-20'},
        ),
        (
            [
                {
                    'id': TEST_UUID_1,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 0,
                    'activation_date': '2019-12-20',
                    'date_expired': '2019-12-20',
                    'type': AccountType.client_rate,
                }
            ],
            {'id': TEST_UUID_1, 'start_date': '2030-12-20', 'end_date': '2030-12-20'},
            {'amount': 0, 'start_date': '2030-12-20', 'end_date': '2030-12-20'},
        ),
    ],
)
async def test_update_rate(aiohttp_client, existed_rates, update_data, expected):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(app, email='<EMAIL>', company_id=TEST_UUID_1)
    async with app['db'].acquire() as conn:
        await insert_company_rate(conn, existed_rates)

    rate_id = update_data.pop('id')
    response = await client.patch(
        UPDATE_COMPANY_RATE_URL.format(rate_id=rate_id),
        json=update_data,
        headers=header,
    )
    assert response.status == HTTPStatus.OK, await response.text()
    async with app['db'].acquire() as conn:
        rate = await select_rate_by_id(conn, rate_id)

    def _to_date_str(date):
        return to_local_datetime(date).strftime('%Y-%m-%d') if date else None

    assert rate.amount == expected['amount']
    assert _to_date_str(rate.start_date) == expected['start_date']
    assert _to_date_str(rate.end_date) == expected['end_date']


@pytest.mark.parametrize(
    'existed_rates, rate_id, are_limits_changed',
    [
        (
            [
                {
                    'id': TEST_UUID_1,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'activation_date': '2019-12-20',
                    'date_expired': '2019-12-20',
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.new,
                    'amount': 200,
                    'activation_date': '2020-12-20',
                    'date_expired': '2020-12-30',
                    'type': AccountType.client_rate,
                },
            ],
            TEST_UUID_1,
            True,
        ),
        (
            [
                {
                    'id': TEST_UUID_2,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.latest_ultimate(),
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'activation_date': '2022-11-20',
                    'date_expired': '2023-11-20',
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_1,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'activation_date': '2022-11-20',
                    'date_expired': '2023-11-20',
                    'type': AccountType.client_rate,
                },
            ],
            TEST_UUID_1,
            False,
        ),
        (
            [
                {
                    'id': TEST_UUID_1,
                    'company_id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'activation_date': '2019-12-20',
                    'date_expired': '2019-12-20',
                    'type': AccountType.client_rate,
                }
            ],
            TEST_UUID_1,
            True,
        ),
    ],
)
async def test_delete_rate(aiohttp_client, existed_rates, rate_id, are_limits_changed):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1, edrpou=TEST_COMPANY_EDRPOU)
    await prepare_user_data(app, email='<EMAIL>', company_id=TEST_UUID_1)
    async with app['db'].acquire() as conn:
        await insert_company_rate(conn, existed_rates)

    response = await client.delete(DELETE_COMPANY_RATE_URL.format(rate_id=rate_id), headers=header)
    assert response.status == HTTPStatus.OK, await response.text()
    async with services.db.acquire() as conn:
        rate = await select_rate_by_id(conn, rate_id)

    assert rate.status == CompanyRateStatus.canceled

    if not are_limits_changed:
        async with services.db.acquire() as conn:
            active_rates = await select_active_company_rates(conn, TEST_UUID_1)
            assert _get_rates_limits(active_rates) == {
                **UNLIMITED,
                # currently ALL rates have not infinite document version depth
                CompanyLimit.max_versions_count: 50,
            }


@pytest.mark.parametrize(
    'rate, existing_rate, status',
    [
        (AccountRate.latest_integration_trial(), None, 201),
        (AccountRate.latest_pro_plus_trial(), None, 201),
        (AccountRate.latest_pro(), None, 400),
        (
            AccountRate.latest_integration_trial(),
            AccountRate.latest_pro_plus_trial(),
            201,
        ),
        (
            AccountRate.latest_pro_plus_trial(),
            AccountRate.latest_integration_trial(),
            201,
        ),
        (
            AccountRate.latest_integration_trial(),
            AccountRate.latest_integration_trial(),
            400,
        ),
        (AccountRate.latest_pro_plus_trial(), AccountRate.latest_pro_plus_trial(), 400),
        (AccountRate.latest_integration_trial(), AccountRate.latest_integration(), 400),
    ],
)
async def test_add_trial_company_rate(aiohttp_client, rate, existing_rate, status):
    app, client, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        await conn.execute(
            company_statistic_table.insert().values(
                {'company_id': user.company_id, 'roles_count': ROLE_COUNT_FREE_PRO}
            )
        )
        if existing_rate:
            await insert_company_rate(
                conn,
                {
                    'id': TEST_UUID_1,
                    'company_id': user.company_id,
                    'rate': existing_rate,
                    'status': CompanyRateStatus.active,
                    'amount': 0,
                    'activation_date': '2021-01-01',
                    'date_expired': '2021-02-01',
                    'type': AccountType.client_rate,
                },
            )

    r = await client.post(
        API_V2_ADD_TRIAL_RATE_URL,
        json={'rate': rate.value},
        headers=prepare_auth_headers(user),
    )
    assert r.status == status


@pytest.mark.parametrize(
    'company_edrpou, bill, price, bill_type',
    [
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ]
            },  # bill
            START_PRICE_FOP,  # price
            'rate_payment',  # bill_type
            id='start_rate',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },  # bill
            PRO_PRICE_FOP,  # price
            'rate_payment',  # bill_type
            id='pro_rate',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [{'type': 'units', 'units': 500}],
            },  # bill
            500 * INTEGRATION_PER_DOCUMENT_PRICE,  # price
            'document_payment',  # bill_type
            id='units',
        ),
        pytest.param(
            COMPANY_EDRPOU_TOV,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ]
            },  # bill
            START_PRICE_TOV,  # price
            'rate_payment',  # bill_type
            id='start_rate_tov',
        ),
    ],
)
async def test_evopay_url_generation(
    aiohttp_client,
    company_edrpou: str,
    bill: dict,
    price: float,
    bill_type: str,
    test_flags,
):
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    _, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        enable_pro_functionality=False,
    )

    headers = prepare_auth_headers(user)
    bill_data = {
        **bill,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(BILLS_URL, data=ujson.dumps(bill_data), headers=headers)
    assert response.status == 201

    resp = await response.json()
    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])

    payment_data = {
        'bill_id': bill.id_,
        'role_id': user.role_id,
        'bill_type': bill_type,
        'price': price * 100,
        'currency': 'UAH',
        'description': 'TEST CARD PAYMENT',
    }
    response = await client.post(
        EVOPAY_PAGE_GENERATION_URL,
        json=payment_data,
        headers=headers,
    )
    assert response.status == 200, await response.json()
    resp = await response.json()
    assert isinstance(resp['data']['url'], str)


@pytest.mark.parametrize(
    'company_edrpou, request_bill, expected',
    [
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {'rate': AccountRate.latest_start().value},
            id='start_rate_fop',
        ),
        pytest.param(
            COMPANY_EDRPOU_TOV,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {'rate': AccountRate.latest_start().value},
            id='start_rate_pro',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {'rate': AccountRate.latest_pro().value},
            id='pro_rate_fop',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_integration().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {'rate': AccountRate.latest_integration().value},
            id='integration_rate_fop',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {'services': [{'type': 'units', 'units': 500}]},
            {'documents': 500},
            id='units_fop_1',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            {'services': [{'type': 'units', 'units': 10000}]},
            {'documents': 10000},
            id='units_fop_2',
        ),
    ],
)
async def test_evopay_webhook(
    aiohttp_client,
    company_edrpou,
    request_bill,
    expected,
    test_flags,
    crm_box,
):
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    _, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=company_edrpou,
        enable_pro_functionality=False,
    )

    assert len(crm_box) == 0
    headers = prepare_auth_headers(user)
    bill_data = {
        **request_bill,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        path=BILLS_URL,
        data=ujson.dumps(bill_data),
        headers=headers,
    )
    assert response.status == 201

    resp = await response.json()
    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])

        config = services.config.evopay
        payload = {'project_order_id': bill.id_, 'payload': config.evopay_webhook_key}

        # Payment requested
        response = await client.post(
            path=EVOPAY_WEBHOOK_URL,
            data=ujson.dumps({'payment': {'status': 1}, **payload}),
        )
        assert response.status == 200
        p_transactions = await select_payment_status_transactions_by_bill_id(conn, bill.id_)
        assert BillPaymentStatus.requested in (p.payment_status for p in p_transactions)

        # Pin requested
        response = await client.post(
            path=EVOPAY_WEBHOOK_URL,
            data=ujson.dumps({'payment': {'status': 2}, **payload}),
        )
        assert response.status == 200
        p_transactions = await select_payment_status_transactions_by_bill_id(conn, bill.id_)
        assert BillPaymentStatus.pin_requested in (p.payment_status for p in p_transactions)

        # Payment successfull
        response = await client.post(
            path=EVOPAY_WEBHOOK_URL,
            data=ujson.dumps({'payment': {'status': 4}, **payload}),
        )
        assert response.status == 200
        p_transactions = await select_payment_status_transactions_by_bill_id(conn, bill.id_)
        assert BillPaymentStatus.completed in (p.payment_status for p in p_transactions)

        if expected_rate := expected.get('rate'):
            active_rates = await select_active_company_rates(conn, user.company_id)
            rate = [rate for rate in active_rates if rate.rate.value == expected_rate]
            assert len(rate) == 1
            rate = rate[0]
            assert rate.status == CompanyRateStatus.active
            assert rate.start_date.date() == datetime.datetime.today().date()
            assert (
                rate.end_date.date()
                == to_utc_datetime(get_rate_end_date_from_start_date(rate.start_date)).date()
            )
            assert rate.amount == get_rate_price(rate.rate, edrpou=user.company_edrpou) * 100

            # 1 - sending bill creation
            # 2 - sending rate
            assert len(crm_box) == 2

            bill_creation_crm_event = crm_box[0]
            assert bill_creation_crm_event['status'] == CRMBillStatus.new.value

        elif expected_documents := expected.get('documents'):
            accounts = await select_company_accounts(conn, user.company_id)
            assert len(accounts) == 1
            account = accounts[0]
            assert account.units_left == expected_documents
            return


async def test_fail_evopay_webhook(aiohttp_client, test_flags):
    """
    Given an attempt to pay with card
    If Evopay declines payment
    Description should be created in database
    """

    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    _, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='**********',
        enable_pro_functionality=False,
    )

    headers = prepare_auth_headers(user)
    bill_data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [{'type': 'units', 'units': 500}],
    }
    response = await client.post(
        path=BILLS_URL,
        json=bill_data,
        headers=headers,
    )
    assert response.status == 201

    resp = await response.json()
    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])

        error_status_code = 2005

        config = services.config.evopay
        payload = {'project_order_id': bill.id_, 'payload': config.evopay_webhook_key}
        response = await client.post(
            path=EVOPAY_WEBHOOK_URL,
            json={
                'payment': {'status': 3, 'transaction': {'status_code': error_status_code}},
                **payload,
            },
        )
        assert response.status == 200

        p_transactions = await select_payment_status_transactions_by_bill_id(conn, bill.id_)
        assert p_transactions[0].status_code == error_status_code

        response = await client.get(path=f'/internal-api/bills/{bill.id_}')
        assert response.status == 200
        data = await response.json()

        assert (
            data['payment_status_description']
            == EVOPAY_STATUS_CODE_TO_DESCRIPTION_MAP[error_status_code]
        )


async def test_retrieve_bill(aiohttp_client, test_flags):
    test_flags[FeatureFlags.ENABLE_NEW_RATES.name] = True
    _, client, user = await prepare_client(aiohttp_client)

    headers = prepare_auth_headers(user)
    bill_data = {
        'edrpou': user.company_edrpou,
        'email': user.email,
        'name': 'test company name',
        'services': [{'type': 'rate', 'rate': AccountRate.latest_start().value}],
    }
    response = await client.post(
        path=BILLS_URL,
        json=bill_data,
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED

    resp = await response.json()
    bill_id = resp['bill_id']

    response = await client.get(
        path=f'/internal-api/bills/{bill_id}',
        headers=headers,
    )
    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert data.pop('id') is not None
    assert data == {
        'edrpou': '********',
        'status_id': 5000,
        'payment_status': None,
        'payment_status_description': None,
        'document_id': None,
        'services_type': 'rate',
        'rate': LATEST_START_RATE.value,
        'count_documents': None,
        'services': [
            {
                'type': 'rate',
                'units': 1,
                'unit_price': str(START_PRICE_TOV_SUBUNITS),
                'rate': LATEST_START_RATE.value,
                'date_from': None,
                'limits_employees_count': None,
                'price_per_user': None,
            }
        ],
    }


@pytest.mark.parametrize(
    'request_bill, expected_rate, expected_config',
    [
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {
                'rate': AccountRate.latest_pro(),
                'amount': PRO_PRICE_TOV_SUBUNITS,
                'employee_amount': 3,
                'units': 0,
                'status': CompanyRateStatus.active,
            },
            BillingCompanyConfig(
                max_versions_count=50,
                max_automation_count=0,
                can_enforce_2fa=True,
                max_additional_fields_count=0,
                reviews_enabled=True,
                max_employees_count=3,
                can_manage_employee_access=True,
                max_required_fields_count=0,
                max_documents_count=3000,
                internal_document_enabled=True,
                internal_comments_enabled=True,
                external_comments_enabled=True,
            ),
            id='pro_rate',
        ),
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_start().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {
                'rate': AccountRate.latest_start(),
                'amount': START_PRICE_TOV_SUBUNITS,
                'employee_amount': 2,
                'units': 0,
                'status': CompanyRateStatus.active,
            },
            BillingCompanyConfig(
                external_comments_enabled=True,
                max_documents_count=400,
                internal_comments_enabled=True,
                can_manage_employee_access=True,
                max_employees_count=2,
                max_required_fields_count=0,
                max_automation_count=0,
                max_versions_count=0,
                max_additional_fields_count=0,
                max_tags_count=0,
                can_enforce_2fa=False,
            ),
            id='start_rate',
        ),
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.archive_small.value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {
                'rate': AccountRate.archive_small,
                'amount': 480 * 100,
                'employee_amount': None,
                'units': 0,
                'status': CompanyRateStatus.active,
            },
            BillingCompanyConfig(
                max_archive_documents_count=1000,
            ),
            id='archive_small',
        ),
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.archive_big.value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            {
                'rate': AccountRate.archive_big,
                'amount': 1440 * 100,
                'employee_amount': None,
                'units': 0,
                'status': CompanyRateStatus.active,
            },
            BillingCompanyConfig(
                max_archive_documents_count=5000,
            ),
            id='archive_big',
        ),
    ],
)
async def test_activate_bill_by_superadmin_for_client_rate(
    aiohttp_client,
    request_bill: dict,
    expected_rate: dict,
    expected_config: BillingCompanyConfig,
    crm_box,
):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    assert len(crm_box) == 0
    headers = prepare_auth_headers(super_admin)

    bill_data = {
        **request_bill,
        'email': super_admin.email,
        'edrpou': super_admin.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        path=BILLS_URL,
        json=bill_data,
        headers=headers,
    )
    assert response.status == 201
    response_json = await response.json()
    bill_id = response_json['bill_id']
    crm_box.clear()

    start_date = local_now()
    end_date = local_now() + relativedelta(months=6)
    response = await client.post(
        path=BILL_ACTIVATE_URL.format(bill_id=bill_id),
        json={
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
        },
        headers=headers,
    )
    assert response.status == 200

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, super_admin.company_id)
        assert len(active_rates) == 1
        rate = active_rates[0]
        assert rate.amount == expected_rate['amount']
        assert rate.bill_id == bill_id
        assert rate.employee_amount == expected_rate['employee_amount']
        assert rate.rate == expected_rate['rate']
        assert rate.status == expected_rate['status']
        assert rate.units == expected_rate['units']
        assert rate.status == CompanyRateStatus.active
        assert to_local_datetime(rate.start_date).date() == start_date.date()
        assert to_local_datetime(rate.end_date).date() == end_date.date()

        config = await get_billing_company_config(conn, company_id=super_admin.company_id)
        assert config.model_dump() == expected_config.model_dump()

    assert len(crm_box) == 1


@pytest.mark.parametrize(
    'request_bill, expected_account, expected_config',
    [
        pytest.param(
            {'services': [{'type': 'units', 'units': 10_000}]},
            {
                'units_left': 10_000,
                'units': 10_000,
            },
            BillingCompanyConfig(
                # This type of bill only top-up documents. For API access
                # user should buy a client rate "integration"
                api_enabled=False,
            ),
            id='integration_documents',
        )
    ],
)
async def test_active_bill_by_superadmin_for_documents(
    aiohttp_client,
    request_bill: dict,
    expected_account: dict,
    expected_config: BillingCompanyConfig,
    crm_box,
    mailbox,
):
    """
    Check that super admin can activate a bill for top-up documents on the company balance
    """
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    assert len(crm_box) == 0
    headers = prepare_auth_headers(super_admin)

    bill_data = {
        **request_bill,
        'email': super_admin.email,
        'edrpou': super_admin.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        path=BILLS_URL,
        json=bill_data,
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    response = await response.json()

    assert len(crm_box) == 1
    assert len(mailbox) == 0
    crm_box.clear()

    response = await client.post(
        path=BILL_ACTIVATE_URL.format(bill_id=response['bill_id']),
        headers=headers,
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        accounts = await select_company_accounts(conn, super_admin.company_id, only_debit=True)
        assert len(accounts) == 1
        account = accounts[0]
        assert account.units_left == expected_account['units_left']
        assert account.units == expected_account['units']
        assert account.rate == AccountRate.integration
        assert account.type == AccountType.client_debit
        assert account.date_deleted is None
        assert account.date_expired is None
        assert account.date_created is not None
        assert account.initiator_id is None

        config = await get_billing_company_config(conn, company_id=super_admin.company_id)
        assert config.model_dump() == expected_config.model_dump()

    assert len(crm_box) == 1
    assert len(mailbox) == 0


@pytest.mark.parametrize(
    'request_bill, expected_account, expected_rate, expected_config',
    [
        pytest.param(
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.integration.value,
                        'date_from': TODAY_STR,
                    },
                    {'type': 'units', 'units': 10_000},
                ],
            },
            # Both account and rate should be created.
            # Pay attention that both have rate: "integration", not "integration_with_documents"
            # because "integration_with_documents" is not real rate, it's used only for creating
            # combined bills
            {
                'rate': AccountRate.integration,
                'units_left': 10_000,
                'units': 10_000,
                'type': AccountType.client_debit,
            },
            {
                'rate': AccountRate.integration,
                'amount': 7200 * 100,
                'employee_amount': None,
                'units': 0,
                'status': CompanyRateStatus.active,
            },
            BillingCompanyConfig(
                api_enabled=True,
            ),
            id='integration_with_documents',
        ),
    ],
)
async def test_active_bill_by_superadmin_for_combined_rate(
    aiohttp_client,
    request_bill: dict,
    expected_account: dict,
    expected_rate: dict,
    expected_config: BillingCompanyConfig,
    crm_box,
    mailbox,
):
    """
    Check that super admin can activate a bill for top-up documents on the company balance
    """
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    assert len(crm_box) == 0
    headers = prepare_auth_headers(super_admin)

    bill_data = {
        **request_bill,
        'email': super_admin.email,
        'edrpou': super_admin.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        path=BILLS_URL,
        json=bill_data,
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED
    response = await response.json()

    assert len(crm_box) == 1
    assert len(mailbox) == 0
    crm_box.clear()

    start_date = local_now()
    end_date = local_now() + relativedelta(months=6)

    response = await client.post(
        path=BILL_ACTIVATE_URL.format(bill_id=response['bill_id']),
        json={
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        accounts = await select_company_accounts(conn, super_admin.company_id, only_debit=True)
        assert len(accounts) == 1
        account = accounts[0]
        assert account.units_left == expected_account['units_left']
        assert account.units == expected_account['units']
        assert account.rate == expected_account['rate']
        assert account.type == expected_account['type']
        assert account.date_deleted is None
        assert account.date_expired is None
        assert account.date_created is not None
        assert account.initiator_id is None

        rates = await select_active_company_rates(conn, company_id=super_admin.company_id)
        assert len(rates) == 1
        rate = rates[0]
        assert rate.amount == expected_rate['amount']
        assert rate.employee_amount == expected_rate['employee_amount']
        assert rate.rate == expected_rate['rate']
        assert rate.status == expected_rate['status']
        assert rate.units == expected_rate['units']
        assert rate.status == CompanyRateStatus.active
        assert to_local_datetime(rate.start_date).date() == start_date.date()
        assert to_local_datetime(rate.end_date).date() == end_date.date()

    assert len(crm_box) == 1
    assert len(mailbox) == 0


async def test_wrong_price_bill_payment(aiohttp_client, test_flags):
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    _, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='**********',
        enable_pro_functionality=False,
    )

    headers = prepare_auth_headers(user)
    response = await client.post(
        path=BILLS_URL,
        data=ujson.dumps(
            {
                'email': user.email,
                'edrpou': user.company_edrpou,
                'name': 'test company name',
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
        ),
        headers=headers,
    )
    assert response.status == 201
    resp = await response.json()

    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, resp['bill_id'])
    payment_data = {
        'bill_id': bill.id_,
        'role_id': user.role_id,
        'bill_type': 'rate_payment',
        'price': 100,
        'currency': 'UAH',
        'description': 'TEST CARD PAYMENT',
    }
    response = await client.post(
        EVOPAY_PAGE_GENERATION_URL,
        data=ujson.dumps(payment_data),
        headers=headers,
    )
    assert response.status == 400


@pytest.mark.parametrize('status_code', [(200), (403)])
async def test_evopay_webhook_auth(aiohttp_client, status_code, test_flags):
    test_flags[FeatureFlags.ENABLE_EVOPAY_PAYMENT.name] = True
    _, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='**********',
        enable_pro_functionality=False,
    )

    config = services.config.evopay
    webhook_key = config.evopay_webhook_key if status_code == 200 else 'fakekeytest'

    headers = prepare_auth_headers(user)
    response = await client.post(
        path=BILLS_URL,
        data=ujson.dumps(
            {
                'email': user.email,
                'edrpou': user.company_edrpou,
                'name': 'test company name',
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_pro().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
        ),
        headers=headers,
    )
    assert response.status == 201
    resp = await response.json()

    response = await client.post(
        path=EVOPAY_WEBHOOK_URL,
        data=ujson.dumps(
            {
                'payment': {'status': 1},
                'project_order_id': resp['bill_id'],
                'payload': webhook_key,
            },
        ),
    )

    assert response.status == status_code


@pytest.mark.parametrize(
    'rate_data, update_data, expected',
    [
        (
            {
                'rate': 'ultimate',
                'amount': 200_000,
                'company_id': TEST_UUID_1,
                'employee_amount': 30,
                'start_date': '2050-01-01',
                'end_date': '2051-01-01',
            },
            {
                'employee_amount': 30,
                'price_per_employee': 200000,
            },
            {
                'employee_amount': 60,
                'amount': 6_200_000,
            },
        ),
        (
            {
                'rate': 'ultimate',
                'amount': 1_000_000,
                'company_id': TEST_UUID_1,
                'employee_amount': 10,
                'start_date': '2050-01-01',
                'end_date': '2051-01-01',
            },
            {
                'employee_amount': 90,
                'price_per_employee': 10000,
            },
            {
                'employee_amount': 100,
                'amount': 1_900_000,
            },
        ),
    ],
)
async def test_add_employees_amount(aiohttp_client, rate_data, update_data, expected):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    header = prepare_auth_headers(super_admin)

    await prepare_company_data(app, id=TEST_UUID_1)
    await prepare_user_data(
        app,
        email='<EMAIL>',
        company_id=TEST_UUID_1,
        enable_pro_functionality=False,
    )

    response = await client.post(CREATE_COMPANY_RATE_URL, json=rate_data, headers=header)
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        rates = await select_companies_rates_for_graph(conn, [TEST_UUID_1])
        rate = rates[0]
        await update_billing_company_config(
            conn=conn,
            company_id=TEST_UUID_1,
            config={CompanyLimit.employees.value: rate_data['employee_amount']},
        )

    response = await client.patch(
        INCREASE_EMPLOYEE_AMOUNT_URL.format(rate_id=rate.id_),
        json=update_data,
        headers=header,
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        config = await get_billing_company_config(conn, company_id=rate.company_id)

        rates = await select_companies_rates_for_graph(conn, [TEST_UUID_1])
        rate = rates[0]

    assert rate.amount == expected['amount']
    assert rate.employee_amount == expected['employee_amount']
    assert config.max_employees_count == expected['employee_amount']


@pytest.mark.parametrize(
    'active_rate, recommended_rate',
    [
        # Free rate
        # Documents are on 100%, rate is not expiring
        (
            {
                'rate': AccountRate.latest_free(),
                'documents_left': 25,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            AccountRate.latest_start(),
        ),
        # Documents are on less than 10%, rate is not expiring
        (
            {
                'rate': AccountRate.latest_free(),
                'documents_left': 3,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            AccountRate.latest_start(),
        ),
        # Documents are on less than 10%, rate is expiring
        (
            {
                'rate': AccountRate.latest_free(),
                'documents_left': 3,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_start(),
        ),
        # Documents are on 100%, rate is expiring
        (
            {
                'rate': AccountRate.latest_free(),
                'documents_left': 25,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_start(),
        ),
        # Start rate
        # Documents are on 50%, not expiring in 30 days
        (
            {
                'rate': AccountRate.latest_start(),
                'documents_left': 150,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            None,
        ),
        # Documents are on less than 10%, not expiring in 30 days
        (
            {
                'rate': AccountRate.latest_start(),
                'documents_left': 10,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            AccountRate.latest_pro(),
        ),
        # Documents are on 50%, rate expiring in 30 days
        (
            {
                'rate': AccountRate.latest_start(),
                'documents_left': 100,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_start(),
        ),
        # Documents are on less than 10%, rate expiring in 30 days
        (
            {
                'rate': AccountRate.latest_start(),
                'documents_left': 20,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_start(),
        ),
        # Pro rate
        # Documents are on 100%, not expiring in 30 days
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 2000,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            None,
        ),
        # Documents are on 50%, not expiring in 30 days
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 1000,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            None,
        ),
        # Documents are on less than 10%, not expiring in 30 days
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 70,  # 75 is 10% of 750 (units for rate)
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            AccountRate.latest_ultimate(),
        ),
        # Documents are on 50%, rate expiring in 30 days
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 1000,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_pro(),
        ),
        # old pro, Documents are on 50%, rate expiring in 30 days
        (
            {
                'rate': AccountRate.pro_2022_12,
                'documents_left': 2000,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_pro(),
        ),
        # Documents are on less than 10%, rate expiring in 30 days
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 100,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_pro(),
        ),
        # Ultimate rate
        # Rate not expiring
        (
            {
                'rate': AccountRate.latest_ultimate(),
                'documents_left': 0,
                'date_expired': (local_now() + datetime.timedelta(weeks=50)),
            },
            None,
        ),
        # Rate is expiring in 30 days
        (
            {
                'rate': AccountRate.latest_ultimate(),
                'documents_left': 0,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_ultimate(),
        ),
        # old ultimate Rate is expiring in 30 days
        (
            {
                'rate': AccountRate.ultimate,
                'documents_left': 0,
                'date_expired': (local_now() + datetime.timedelta(days=15)),
            },
            AccountRate.latest_ultimate(),
        ),
        # Employee limit
        (
            {
                'rate': AccountRate.latest_start(),
                'documents_left': 200,
                'date_expired': (local_now() + datetime.timedelta(days=55)),
                'current_employers': 2,
            },
            AccountRate.latest_pro(),
        ),
        # Employee limit pro
        (
            {
                'rate': AccountRate.latest_pro(),
                'documents_left': 200,
                'date_expired': (local_now() + datetime.timedelta(days=55)),
                'current_employers': 3,
            },
            AccountRate.latest_ultimate(),
        ),
    ],
)
async def test_recommended_rate(aiohttp_client, active_rate, recommended_rate, test_flags):
    """Tests endpoint for recommended rate"""
    test_flags[FeatureFlags.ENABLE_NEW_RATES.name] = True

    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    # Add coworkers if it needs in test (for test case when user have employers limit)
    if current_employers := active_rate.get('current_employers'):
        for new_user in range(1, current_employers):
            await prepare_user_data(app, email=f'a{new_user}@gmail.com')

    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )
    if active_rate['rate'].is_free:
        async with services.db.acquire() as conn:
            await create_free_rate(
                conn,
                company_id=super_admin.company_id,
                company_edrpou=super_admin.company_edrpou,
            )
    header = prepare_auth_headers(super_admin)

    if not active_rate['rate'].is_free:
        data = {
            'rate': active_rate['rate'],
            'amount': 100,
            'company_id': super_admin.company_id,
            'status': CompanyRateStatus.active.value,
            'start_date': TODAY_STR,
            'end_date': active_rate['date_expired'].strftime('%Y-%m-%d'),
        }
        response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
        assert response.status == HTTPStatus.CREATED

    # Ultimate doesn't have client_bonus
    if not active_rate['rate'].is_ultimate:
        async with services.db.acquire() as conn:
            if active_rate['rate'].is_free:
                accounts = await select_company_accounts(
                    conn=conn,
                    company_id=super_admin.company_id,
                )
            else:
                accounts = await select_company_accounts(
                    conn=conn,
                    company_id=super_admin.company_id,
                    only_bonus=True,
                    ignore_new_rates=True,
                )
            await conn.execute(
                billing_account_table.update()
                .values(units_left=active_rate['documents_left'])
                .where(billing_account_table.c.id == accounts[-1].id)
            )

    response = await client.get(RECOMMENDED_RATE_URL, headers=header)
    assert response.status == HTTPStatus.OK

    data = await response.json()
    assert data['rate'] == recommended_rate

    # Add integration to all rates and expect nothing changes
    data = {
        'rate': AccountRate.latest_integration(),
        'amount': 100,
        'company_id': super_admin.company_id,
        'status': CompanyRateStatus.active.value,
        'start_date': TODAY_STR,
        'end_date': active_rate['date_expired'].strftime('%Y-%m-%d'),
    }
    response = await client.post(CREATE_COMPANY_RATE_URL, json=data, headers=header)
    assert response.status == HTTPStatus.CREATED

    response = await client.get(RECOMMENDED_RATE_URL, headers=header)
    assert response.status == HTTPStatus.OK

    data = await response.json()
    assert data['rate'] == recommended_rate


@pytest.mark.parametrize(
    'data, flag, edrpou, expected_amount',
    [
        # FOP
        (
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_integration().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            True,
            '5' * 10,
            1200,
        ),
        # TOV
        (
            {
                'services': [
                    {
                        'type': 'rate',
                        'rate': AccountRate.latest_integration().value,
                        'date_from': TODAY_STR,
                    }
                ],
            },
            True,
            '3' * 8,
            7200,
        ),
    ],
)
async def test_add_bill_different_integration_price(
    aiohttp_client, data, flag, edrpou, expected_amount, test_flags
):
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
        company_edrpou=edrpou,
    )
    headers = prepare_auth_headers(user)
    bill_data = {
        **data,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(BILLS_URL, data=ujson.dumps(bill_data), headers=headers)
    assert response.status == 201
    resp = await response.json()

    assert resp['amount'] == expected_amount


async def test_add_bill_send_bill_email(
    aiohttp_client,
    test_flags,
    mailbox,
    monkeypatch,
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        enable_pro_functionality=False,
        email=TEST_USER_EMAIL,
    )

    async_mock = AsyncMock(return_value=b'some_pdf')
    monkeypatch.setattr('worker.billing.jobs.bill_to_pdf', async_mock)

    headers = prepare_auth_headers(user)
    bill_data = {
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'services': [
            {
                'type': 'rate',
                'rate': AccountRate.latest_start().value,
                'date_from': TODAY_STR,
            }
        ],
    }
    response = await client.post(BILLS_URL, json=bill_data, headers=headers)
    assert response.status == HTTPStatus.CREATED

    assert len(mailbox) == 2
    target_mails = mailbox.by_email(user.email)
    assert len(target_mails) == 1

    assert target_mails[0]['Subject'] == 'Рахунок на оплату послуг Вчасно'
    assert target_mails[0]['To'] == TEST_USER_EMAIL
    attachment = target_mails[0].get_payload()[1]
    assert attachment.get_payload(decode=True) == b'some_pdf'

    # Duplicate email to sales team
    sales_mails = mailbox.by_email(services.config.app.sales_email)
    assert len(sales_mails) == 1
    assert sales_mails[0]['To'] == services.config.app.sales_email
    assert sales_mails[0]['Subject'] == 'Рахунок на оплату послуг Вчасно'
    attachment = sales_mails[0].get_payload()[1]
    assert attachment.get_payload(decode=True) == b'some_pdf'


@pytest.mark.parametrize(
    'edrpou, expected_web_rates, expected_integrations_rates',
    [
        pytest.param(
            COMPANY_EDRPOU_TOV,
            [
                {
                    'rate_set': 'web',
                    'rate': 'free',
                    'can_buy': False,
                    'can_extend': False,
                    'price_per_year': None,
                    'price_per_month': None,
                    'documents_to_send': 25,
                    'comments': True,
                    'manage_employees_access': False,
                    'internal_documents': False,
                    'reviews': False,
                    'enforce_2fa': False,
                    'employees': 2,
                    'tags': 0,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 0,
                    'signing_incoming_documents': None,
                    'documents_to_view': 50,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': LATEST_START_RATE.value,
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': START_PRICE_TOV,
                    'price_per_month': START_PRICE_TOV / 12,
                    'documents_to_send': 250,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': False,
                    'reviews': False,
                    'enforce_2fa': False,
                    'employees': 2,
                    'tags': 0,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 1,
                    'signing_incoming_documents': None,
                    'documents_to_view': 400,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': LATEST_PRO_RATE.value,
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': PRO_PRICE_TOV,
                    'price_per_month': PRO_PRICE_TOV / 12,
                    'documents_to_send': 2000,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': True,
                    'reviews': True,
                    'enforce_2fa': True,
                    'employees': 3,
                    'tags': None,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 2,
                    'signing_incoming_documents': None,
                    'documents_to_view': 3000,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': 'ultimate_2022_12',
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': None,
                    'price_per_month': None,
                    'documents_to_send': None,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': True,
                    'reviews': True,
                    'enforce_2fa': True,
                    'employees': None,
                    'tags': None,
                    'additional_fields': None,
                    'templates': None,
                    'required_fields': None,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 3,
                    'signing_incoming_documents': None,
                    'documents_to_view': None,
                    'extensions': None,
                },
            ],
            [
                {
                    'rate_set': 'integration',
                    'rate': 'integration',
                    'can_buy': True,
                    'can_extend': False,
                    'can_buy_documents': False,
                    'price_per_year': 7200.0,
                    'price_per_document': INTEGRATION_PER_DOCUMENT_PRICE,
                    'signing_documents': True,
                    'comments': True,
                    'tarification': True,
                },
            ],
            id='tov',
        ),
        pytest.param(
            COMPANY_EDRPOU_FOP,
            [
                {
                    'rate_set': 'web',
                    'rate': 'free',
                    'can_buy': False,
                    'can_extend': False,
                    'price_per_year': None,
                    'price_per_month': None,
                    'documents_to_send': 25,
                    'comments': True,
                    'manage_employees_access': False,
                    'internal_documents': False,
                    'reviews': False,
                    'enforce_2fa': False,
                    'employees': 2,
                    'tags': 0,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 0,
                    'signing_incoming_documents': None,
                    'documents_to_view': 50,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': LATEST_START_RATE.value,
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': START_PRICE_FOP,
                    'price_per_month': START_PRICE_FOP / 12,
                    'documents_to_send': None,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': False,
                    'reviews': False,
                    'enforce_2fa': False,
                    'employees': 2,
                    'tags': 0,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 1,
                    'signing_incoming_documents': None,
                    'documents_to_view': 400,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': LATEST_PRO_RATE.value,
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': PRO_PRICE_FOP,
                    'price_per_month': PRO_PRICE_FOP / 12,
                    'documents_to_send': None,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': True,
                    'reviews': True,
                    'enforce_2fa': True,
                    'employees': 5,
                    'tags': None,
                    'additional_fields': 0,
                    'templates': 0,
                    'required_fields': 0,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 2,
                    'signing_incoming_documents': None,
                    'documents_to_view': 3000,
                    'extensions': None,
                },
                {
                    'rate_set': 'web',
                    'rate': 'ultimate_2022_12',
                    'can_buy': True,
                    'can_extend': False,
                    'price_per_year': None,
                    'price_per_month': None,
                    'documents_to_send': None,
                    'comments': True,
                    'manage_employees_access': True,
                    'internal_documents': True,
                    'reviews': True,
                    'enforce_2fa': True,
                    'employees': None,
                    'tags': None,
                    'additional_fields': None,
                    'templates': None,
                    'required_fields': None,
                    'antivirus': True,
                    'cloud_storage': True,
                    'kep_keys': 3,
                    'signing_incoming_documents': None,
                    'documents_to_view': None,
                    'extensions': None,
                },
            ],
            [
                {
                    'rate_set': 'integration',
                    'rate': 'integration',
                    'can_buy': True,
                    'can_extend': False,
                    'can_buy_documents': False,
                    'price_per_year': 1200.0,  # the price of this rate should be different for FOP
                    'price_per_document': INTEGRATION_PER_DOCUMENT_PRICE,
                    'signing_documents': True,
                    'comments': True,
                    'tarification': True,
                },
            ],
            id='fop',
        ),
    ],
)
async def test_get_rates_checkout_properties(
    aiohttp_client,
    test_flags,
    edrpou: str,
    expected_web_rates: list[dict],
    expected_integrations_rates: list[dict],
):
    """
    Basic happy-path test for getting rates checkout properties
    """
    test_flags[FeatureFlags.ENABLE_NEW_RATES.name] = True
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        enable_pro_functionality=False,
        company_edrpou=edrpou,
    )

    test_flags[FeatureFlags.ENABLE_ARCHIVE_RATES.name] = True

    response = await client.get(
        path='/internal-api/billing/checkout/properties',
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    data = await response.json()

    rates_data_web = data['rates_web']
    rates_data_integration = data['rates_integration']
    rates_data_archive = data['rates_archive']

    assert rates_data_web == expected_web_rates
    assert rates_data_integration == expected_integrations_rates
    assert rates_data_archive == [
        {
            'rate_set': 'archive',
            'rate': 'archive_small',
            'can_buy': True,
            'can_extend': False,
            'price_per_year': 480.0,
            'price_per_month': 40.0,
            'archive_documents_to_view': 1000,
        },
        {
            'rate_set': 'archive',
            'rate': 'archive_big',
            'can_buy': True,
            'can_extend': False,
            'price_per_year': 1440.0,
            'price_per_month': 120.0,
            'archive_documents_to_view': 5000,
        },
    ]


async def test_get_company_available_rates_extensions(aiohttp_client):
    """
    Basic happy-path test for getting available rates for company
    """
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)
    async with app['db'].acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            date_expired=local_now() + datetime.timedelta(days=364),
            billing_company_config={CompanyLimit.employees.value: 10},
            type=AccountType.client_rate,
            price_per_user=100052,  # 1000.52 UAH
        )

    response = await client.get(
        path='/internal-api/billing/checkout/properties',
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    response_rates = response_json['rates_web']

    ultimate_rate = next((r for r in response_rates if r['rate'] == 'ultimate_2022_12'), None)
    assert ultimate_rate is not None, 'Ultimate rate not found'

    assert len(ultimate_rate['extensions']) == 1
    assert ultimate_rate['extensions'][0] == {
        'type': 'employees',
        'price_per_unit_base': 1000.52,
        'price_per_unit_total': 1000.52,
    }


async def test_activate_add_employee_bill_by_superadmin(aiohttp_client, crm_box):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=super_admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )
    async with app['db'].acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            user=super_admin,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=Decimal(1000),
                    rate=AccountRate.latest_ultimate(),
                    date_from=None,
                    limits_employees_count=10,
                    price_per_user=Decimal('48000'),
                )
            ],
            custom_price=Decimal(1000),
        )
        await prepare_billing_account(
            conn,
            company_id=super_admin.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            activation_date=local_now(),
            date_expired=local_now() + datetime.timedelta(days=365),
            billing_company_config={CompanyLimit.employees.value: 10},
            type=AccountType.client_rate,
            price_per_user=100052,  # 1000.52 UAH
            bill_id=bill.id_,
        )

    assert len(crm_box) == 0
    response = await client.post(
        path=CRM_BILLS_URL,
        json=BILL_ADD_EMPLOYEE_DATA,
        headers={HEADER_KEY: CRM_AUTH_TOKEN},
    )
    assert response.status == 201
    response_json = await response.json()
    bill_id = response_json['bill_id']

    response = await client.post(
        path=BILL_ACTIVATE_URL.format(bill_id=bill_id),
        headers=prepare_auth_headers(super_admin),
    )
    assert response.status == 200, await response.json()

    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, bill_id)
        assert bill.payment_status == BillPaymentStatus.completed
        config = await get_billing_company_config(conn, company_id=super_admin.company_id)
        assert config.max_employees_count == 15
        # 2 - cause send rate to crm (new max_employee_count value),
        #  and send add_employee bill (new status)
        assert len(crm_box) == 2


@pytest.mark.parametrize(
    'data',
    [
        {
            'start_date': 'invalid_date',
            'end_date': 'invalid_date',
        },
        {
            'start_date': local_now().strftime('%Y-%m-%d'),
        },
        {
            'start_date': (local_now() + relativedelta(months=3)).strftime('%Y-%m-%d'),
            'end_date': local_now().strftime('%Y-%m-%d'),
        },
    ],
)
async def test_activate_bill_by_superadmin_invalid_data(data):
    data['rate'] = AccountRate.latest_start().value
    with pytest.raises(InvalidRequest):
        validate_pydantic(ActivateBillSuperAdmin, data)


async def test_activate_trial_when_employees_limit_reached(aiohttp_client, test_flags):
    app, client, user = await common.prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ACTIVATE_TRIAL_WHEN_EMPLOYEES_LIMIT_REACHED.name] = True

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            status=AccountStatus.active,
            date_expired=local_now() + datetime.timedelta(days=365),
            billing_company_config={CompanyLimit.employees.value: 1},
            type=AccountType.client_rate,
        )

    response = await client.post(
        path='/internal-api/billing/add-trial-when-employees-limit-reached',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        billing_accounts = await select_accounts(
            conn=conn,
            clause=sa.and_(billing_account_table.c.company_id == user.company_id),
        )

    rates = [account.rate for account in billing_accounts]
    assert len(rates) == 3
    assert AccountRate.latest_start() in rates
    assert AccountRate.latest_free() in rates
    assert AccountRate.latest_pro_plus_trial() in rates

    for account in billing_accounts:
        if account.rate == AccountRate.latest_pro_plus_trial():
            assert account.source == BillingAccountSource.employees_limit_reach_ab_testing


async def test_dont_allow_to_activate_trial_when_employee_limit_not_reached(
    aiohttp_client, test_flags
):
    app, client, user = await common.prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ACTIVATE_TRIAL_WHEN_EMPLOYEES_LIMIT_REACHED.name] = True

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            status=AccountStatus.active,
            date_expired=local_now() + datetime.timedelta(days=365),
            billing_company_config={CompanyLimit.employees.value: 2},
            type=AccountType.client_rate,
        )

    response = await client.post(
        path='/internal-api/billing/add-trial-when-employees-limit-reached',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.FORBIDDEN


async def test_activate_trial_when_used_paid_feature(aiohttp_client, test_flags):
    app, client, user = await common.prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ACTIVATE_TRIAL_WHEN_USED_PAID_FEATURE.name] = True

    response = await client.post(
        path='/internal-api/billing/add-trial-when-used-paid-feature',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        billing_accounts = await select_accounts(
            conn=conn,
            clause=sa.and_(billing_account_table.c.company_id == user.company_id),
        )

    rates = [account.rate for account in billing_accounts]
    assert len(rates) == 2
    assert AccountRate.latest_free() in rates
    assert AccountRate.latest_pro_plus_trial() in rates

    for account in billing_accounts:
        if account.rate == AccountRate.latest_pro_plus_trial():
            assert account.source == BillingAccountSource.used_paid_feature

    response = await client.post(
        path='/internal-api/billing/add-trial-when-used-paid-feature',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST


async def test_activate_trial_when_used_paid_feature_after_higher_rate(aiohttp_client, test_flags):
    app, client, user = await common.prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ACTIVATE_TRIAL_WHEN_USED_PAID_FEATURE.name] = True
    now = local_now()

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_pro_plus_trial(),
            status=AccountStatus.expired,
            date_expired=local_now() - datetime.timedelta(days=5),
            type=AccountType.client_rate,
            source=BillingAccountSource.used_paid_feature,
            activation_date=now + datetime.timedelta(minutes=1),
        )

    response = await client.post(
        path='/internal-api/billing/add-trial-when-used-paid-feature',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.expired,
            date_expired=local_now() - datetime.timedelta(days=5),
            type=AccountType.client_rate,
            activation_date=now + datetime.timedelta(minutes=2),
        )

    response = await client.post(
        path='/internal-api/billing/add-trial-when-used-paid-feature',
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        billing_accounts = await select_accounts(
            conn=conn,
            clause=sa.and_(
                billing_account_table.c.company_id == user.company_id,
                billing_account_table.c.status == AccountStatus.active,
            ),
        )

    rates = [account.rate for account in billing_accounts]
    assert len(rates) == 2
    assert AccountRate.latest_free() in rates
    assert AccountRate.latest_pro_plus_trial() in rates

    for account in billing_accounts:
        if account.rate == AccountRate.latest_pro_plus_trial():
            assert account.source == BillingAccountSource.used_paid_feature


@pytest.mark.parametrize(
    'bank, transaction_body, activation_skipped',
    [
        # Another edrpou
        (
            BankEnum.pumb,
            {'senderTaxId': '********'},
            True,
        ),
        # The same edrpou
        (
            BankEnum.pumb,
            {'senderTaxId': '********'},
            False,
        ),
        # Bank edrpou
        (
            BankEnum.pumb,
            {'senderTaxId': '********'},
            False,
        ),
        # No edrpou
        (
            BankEnum.pumb,
            {},
            False,
        ),
        # Another edrpou (privat)
        (
            BankEnum.privat,
            {'AUT_CNTR_CRF': '********'},
            True,
        ),
    ],
)
async def test_skip_activation_different_edrpou(
    aiohttp_client,
    bank,
    transaction_body,
    activation_skipped,
):
    app, client, user = await prepare_client(aiohttp_client)
    async with app['db'].acquire() as conn:
        bill = await insert_bill(
            conn,
            prepare_add_bill_options(
                edrpou=user.company_edrpou,
                email=user.email,
                services_type=BillServicesType.rate,
                services=[
                    AddBillServiceRateOptions(
                        units=1,
                        unit_price=1,
                        rate=AccountRate.latest_start(),
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
                custom_price=Decimal(2400),
            ),
        )

    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, bill.id)
        transaction = prepare_bank_transaction(
            {
                'transaction_id': TEST_BANK_TRANSACTION_ID_1,
                'amount': START_PRICE_TOV,
                'bank': bank,
                'transaction_description': f'Оплата рахунка № ВЧ-0{bill.seqnum}',
                'transaction_body': transaction_body,
            }
        )
    mocked_client = mock.AsyncMock()
    mocked_client.fetch_transactions.return_value = [transaction]
    await _process_bank_transactions(mocked_client, {}, mock.Mock())

    # Asserts
    async with services.db.acquire() as conn:
        db_transactions = await select_bank_transactions_by_transaction_ids(
            conn=conn,
            transaction_ids=[transaction.transaction_id],
        )
        if activation_skipped:
            assert db_transactions[0].is_processed is False
            assert db_transactions[0].details == 'Transaction sender is not bill owner'
        else:
            assert db_transactions[0].is_processed is True
            assert db_transactions[0].details is None


@pytest.mark.parametrize(
    'days, rate_with_same_priority, activation_skipped',
    [
        # 30 days <= - deactivate unlimited, activate start/pro
        # (from activate rate date to end date)
        (
            30,
            False,
            False,
        ),
        # The same but exist rate with the same priority, skip activation
        (
            30,
            True,
            True,
        ),
        # 31 >= - raise error
        (
            31,
            False,
            True,
        ),
    ],
)
async def test_deactivate_unlimited_rate_on_transaction_processing_if_is_trial(
    aiohttp_client,
    days,
    rate_with_same_priority,
    activation_skipped,
    crm_box,
):
    app, client, user = await prepare_client(aiohttp_client)
    rate_price = Decimal(5005_00)  # в копійках

    async with app['db'].acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=rate_price,
                    rate=AccountRate.latest_start(),
                    date_from=None,
                    limits_employees_count=None,
                    price_per_user=None,
                )
            ],
            custom_price=rate_price / 100,
        )
        await prepare_billing_account(
            conn,
            initiator_id=user.id,
            company_id=user.company_id,
            rate=AccountRate.pro,
            status=AccountStatus.active,
            type_=AccountType.client_rate,
            activation_date=local_now(),
            date_expired=local_now() + datetime.timedelta(days=days),
            billing_company_config={CompanyLimit.employees.value: 10},
        )
        if rate_with_same_priority:
            await prepare_billing_account(
                conn,
                initiator_id=user.id,
                company_id=user.company_id,
                rate=AccountRate.latest_pro(),
                status=AccountStatus.active,
                type_=AccountType.client_rate,
                activation_date=local_now(),
                date_expired=local_now() + datetime.timedelta(days=days),
                billing_company_config={CompanyLimit.employees.value: 10},
            )

    mocked_client = mock.AsyncMock()
    transactions = [
        prepare_bank_transaction(
            {
                'transaction_id': TEST_BANK_TRANSACTION_ID_1,
                'amount': rate_price / 100,
                'bank': BankEnum.privat,
                'transaction_description': f'ВЧ-{bill.seqnum}',
            }
        )
    ]
    mocked_client.fetch_transactions.return_value = transactions
    await _process_bank_transactions(mocked_client, {}, mock.Mock())
    async with app['db'].acquire() as conn:
        transactions = await select_bank_transactions_by_transaction_ids(
            conn, transaction_ids=[TEST_BANK_TRANSACTION_ID_1]
        )
        client_rates = await get_company_rates(company_id=user.company_id)
        company_config = await select_billing_company_config(conn, company_id=user.company_id)

        if activation_skipped:
            assert (
                transactions[0].details == 'Не можна оплачувати рахунок нижче за тарифом активного'
            )
            assert transactions[0].is_processed is False
            assert {client_rates[0].rate, client_rates[1].rate} == {
                AccountRate.free,
                AccountRate.pro,
            }
            assert company_config.max_employees_count == 10
        else:
            assert transactions[0].details is None
            assert transactions[0].is_processed is True
            assert {client_rates[0].rate, client_rates[1].rate} == {
                AccountRate.free,
                AccountRate.latest_start(),
            }
            assert company_config.max_employees_count == 2

            assert len(crm_box) == 3  # deactivated unlim, activated start, bank transaction in crm

            assert {crm_box[0]['name'], crm_box[1]['name']} == {
                CRMBillType.start_tov.value,
                CRMBillType.unlimited.value,
            }
            assert {crm_box[0]['status'], crm_box[1]['status']} == {
                CompanyRateStatus.active.value,
                CompanyRateStatus.canceled.value,
            }


@pytest.mark.skip('Uncomment test if use AccountRate.latest_* with feature flag')
@pytest.mark.parametrize(
    'rate, local_now, expected_bill_activated',
    [
        ('start', datetime.datetime(2025, 3, 31, tzinfo=LOCAL_TZ), True),
        ('start', datetime.datetime(2025, 4, 1, tzinfo=LOCAL_TZ), False),
        ('pro', datetime.datetime(2025, 4, 1, tzinfo=LOCAL_TZ), True),
        ('pro', datetime.datetime(2025, 4, 2, tzinfo=LOCAL_TZ), False),
    ],
)
async def test_enable_new_rates_feature_flag(
    aiohttp_client, test_flags, mock_local_now, rate, local_now, expected_bill_activated
):
    """
    Create bill with old rate (start_2024_10)
    Enable new rates feature flag
    Create bill with new rate (start_2025_04)
    Assert that both bills are created and older bill not activated if local_now > 2025-04-02
    """
    mock_local_now.return_value = local_now
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=FOP_EDRPOU)

    bill_data = {
        'rate': rate,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        CRM_BILLS_URL,
        json=bill_data,
        headers={HEADER_KEY: CRM_AUTH_TOKEN},
    )
    old_bill = await response.json()
    assert response.status == 201

    test_flags[FeatureFlags.ENABLE_NEW_RATES.name] = True

    response = await client.post(
        CRM_BILLS_URL,
        json=bill_data,
        headers={HEADER_KEY: CRM_AUTH_TOKEN},
    )
    new_bill = await response.json()
    assert response.status == 201

    async with app['db'].acquire() as conn:
        bills = await select_bills_by_edrpous(conn, [user.company_edrpou])
        assert len(bills) == 2
        assert {old_bill['number'], new_bill['number']} == {bill.number for bill in bills}
        bills_by_number = {bill.number: bill for bill in bills}
        old_bill_db = bills_by_number[old_bill['number']]
        new_bill_db = bills_by_number[new_bill['number']]
        if rate == 'pro':
            assert old_bill_db.single_rate_service.rate == AccountRate.pro_2024_10
            assert new_bill_db.single_rate_service.rate == AccountRate.pro_2025_04
        else:
            assert old_bill_db.single_rate_service.rate == AccountRate.start_2024_10
            assert new_bill_db.single_rate_service.rate == AccountRate.start_2025_04

    mocked_client = mock.AsyncMock()
    transactions = [
        prepare_bank_transaction(
            {
                'transaction_id': TEST_BANK_TRANSACTION_ID_1,
                'amount': 1800 if rate == 'start' else 4800,
                'bank': BankEnum.pumb,
                'transaction_description': f'ВЧ-{old_bill_db.number}',
            }
        )
    ]
    mocked_client.fetch_transactions.return_value = transactions
    await _process_bank_transactions(mocked_client, {}, mock.Mock())

    async with app['db'].acquire() as conn:
        bill = await select_bill_by_id(conn, old_bill['bill_id'])
        transaction = await select_bank_transaction(
            conn=conn, transaction_id=TEST_BANK_TRANSACTION_ID_1
        )
        if expected_bill_activated:
            assert bill.payment_status == BillPaymentStatus.completed
            assert transaction.details is None
        else:
            assert bill.payment_status is None
            assert transaction.details == 'Bill has old price (created before April 2025)'
