import io
import logging
from http import HTTPStatus
from uuid import uuid4

from aiohttp import web
from multidict import MultiDict

from api.downloads.utils import stream_buffer
from api.errors import AccessDenied, Code, InvalidRequest, ServerError
from api.private.super_admin.db import insert_super_admin_action
from api.private.super_admin.views import API_BILLING_TAG
from api.public.decorators import login_required as token_login_required
from app.analytics.db import add_analytics_event
from app.analytics.types import AnalyticsEvent, AnalyticsEventType
from app.auth import db as auth_db
from app.auth.constants import USER_APP_KEY
from app.auth.decorators import (
    login_required,
    super_admin_permission_required,
)
from app.auth.enums import RoleActivationSource
from app.billing import api, sa_utils, types, utils
from app.billing import validators as billing_validators
from app.billing.api import activate_custom_bonus as activate_custom_bonus_api
from app.billing.api import activate_debit as activate_debit_api
from app.billing.api import cancel_bonus as cancel_bonus_api
from app.billing.api import cancel_debit as cancel_debit_api
from app.billing.constants import (
    BILL_REQUISITES,
    EVOPAY_STATUS_CODE_TO_DESCRIPTION_MAP,
    EVOPAY_STATUS_TO_BILLING_MAP,
)
from app.billing.db import (
    _update_company_rate,
    insert_bill,
    insert_bonus,
    select_active_company_rates,
    select_latest_payment_status_transaction_by_bill_id,
    update_billing_company_config,
)
from app.billing.db import delete_bonus as delete_bonus_func
from app.billing.db import update_bonus as update_bonus_func
from app.billing.enums import (
    AccountRate,
    BillActivationSource,
    BillingAccountSource,
    BillPaymentSource,
    BillPaymentStatus,
    BillSource,
    BillStatus,
    CompanyLimit,
)
from app.billing.integrations.evopay import EvopayClient
from app.billing.sa_utils import get_marketing_campaign_audiences, set_marketing_campaign_audiences
from app.billing.schemas import (
    ActivateBillSuperAdmin,
    CreateBillResponseSchema,
    CreateBillSchema,
    RetrieveBillSchema,
)
from app.billing.types import (
    CheckoutProperties,
    IncreaseEmployeesSchema,
)
from app.billing.utils import (
    activate_service_by_bill_payment,
    add_employees_rate_extension,
    bill_to_pdf,
    bill_to_response,
    calculate_amount_on_adding_employees,
    cancel_bill,
    cancel_bill_resources,
    create_payment_status_transaction,
    get_bill_resources,
    get_price_per_document,
    get_rate_extension_employee_units,
    get_recommended_web_rate,
    process_evopay_payment,
    process_extra_roles,
    schedule_bill_processing,
)
from app.billing.validators import (
    ActivateRateValidator,
    EvopayPageGenerationValidator,
    _validate_rate_exists,
    validate_activate_custom_bonuses_from_file,
    validate_add_bill_from_creatio,
    validate_add_bill_from_web,
    validate_add_bonus,
    validate_add_company_rate,
    validate_add_trial_company_rate,
    validate_add_trial_due_to_employees_limit_reach,
    validate_add_trial_due_to_used_paid_feature,
    validate_bill_exists_by_id,
    validate_cancel_bill,
    validate_company_max_employees_count,
    validate_delete_bonus,
    validate_delete_company_rate,
    validate_order_request,
    validate_retrieve_bill,
    validate_update_bonus,
    validate_update_company_rate,
)
from app.crm.utils import send_bill_to_crm, send_rate_to_crm
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib.database import DBRow
from app.lib.enums import SuperAdminActionType
from app.lib.helpers import (
    client_rate_limit,
    json_response,
    namedtuple_to_dict,
    to_json,
)
from app.lib.integrations import private_integration
from app.lib.redirects import redirect
from app.lib.urls import build_url
from app.openapi.decorators import openapi_docs, openapi_docs_ignore
from app.profile import validators as profile_validators
from app.profile.db import select_company_users
from app.profile.utils import update_esputnik_company_rate
from app.profile.validators import UpdateBillStatusSchema
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='creatio')
async def add_bill_from_creatio(request: web.Request) -> web.Response:
    """Add bill from creatio"""
    data = await validators.validate_json_request(request)
    logger.info('Bill from CRM raw data', extra={'data': data})
    extension_status = None

    async with services.db.acquire() as conn:
        options = await validate_add_bill_from_creatio(conn, raw_data=data)

        async with conn.begin():
            bill = await insert_bill(conn, options)

            for service in options.services:
                if utils.is_employee_extension_service(service):
                    extension_status = await add_employees_rate_extension(
                        conn, options=options, bill=bill
                    )

    await schedule_bill_processing(
        bill_id=bill.id_,
        is_card_payment=False,
    )

    logger.info(
        'Bill added from CRM',
        extra={
            'bill_id': bill.id_,
            'edrpou': options.edrpou,
            'email': options.email,
        },
    )

    response = await bill_to_response(bill, extension_status=extension_status)
    return web.json_response(status=201, data=response.to_api())


@login_required
async def recommended_rate_handler(request: web.Request, user: DBRow) -> web.Response:
    """Returns next recommended web rate for a company"""
    recommended_rate = await get_recommended_web_rate(user.company_id)
    return web.json_response({'rate': recommended_rate})


@openapi_docs(
    summary=_('Отримати властивості тарифів'),
    description=_('Це дані для сторінки де ми показуємо список тарифів для купівлі чи продовження'),
    response=CheckoutProperties,
)
@login_required
async def get_checkout_properties(__: web.Request, user: DBRow) -> web.Response:
    """
    Get rates properties for the checkout page
    """

    # Sometimes user may call that route without company
    if not user.company_edrpou:
        properties = CheckoutProperties()
        return web.json_response(properties.model_dump(mode='json'))

    async with services.db_readonly.acquire() as conn:
        active_rates = await select_active_company_rates(conn, company_id=user.company_id)
        custom_price_per_document = await get_price_per_document(conn, company_id=user.company_id)

    properties = utils.get_checkout_properties(
        active_rates=active_rates,
        custom_price_per_document=custom_price_per_document,
        company_edrpou=user.company_edrpou,
    )

    return web.json_response(properties.model_dump(mode='json'))


async def evopay_process_webhook(request: web.Request) -> web.Response:
    """Process webhook from evopay payment"""

    data = await validators.validate_json_request(request)
    config = services.config.evopay
    if not config:
        raise ServerError(
            code=Code.temporary_unavailable,
            reason=_('Evopay не налаштований на сервері'),
        )

    webhook_key = data.get('payload')

    if not webhook_key or webhook_key != config.evopay_webhook_key:
        raise AccessDenied(reason=_('Неавторизований сервіс.'))

    evopay_transaction = data['payment'].get('transaction')
    bill_id, payment_status_id, status_code = (
        data['project_order_id'],
        data['payment']['status'],
        evopay_transaction and evopay_transaction.get('status_code'),
    )
    payment_status = EVOPAY_STATUS_TO_BILLING_MAP.get(payment_status_id)

    if not payment_status:
        raise ServerError(reason=_('Невідомий статус оплати від Evopay'))

    logger.info(
        'Received status update from evopay',
        extra={
            'bill_id': bill_id,
            'status': payment_status.value,
            'status_code': status_code,
        },
    )

    async with services.db.acquire() as conn:
        await create_payment_status_transaction(
            conn=conn,
            bill_id=bill_id,
            payment_status=payment_status,
            status_code=status_code,
            source=BillPaymentSource.card,
        )

        if payment_status != BillPaymentStatus.completed:
            return web.Response(status=200)

        await process_evopay_payment(conn, bill_id)

    return web.Response(status=200)


@login_required
async def evopay_generate_page(request: web.Request, user: DBRow) -> web.Response:
    """Generation of evopay redirect page"""

    if not get_flag(FeatureFlags.ENABLE_EVOPAY_PAYMENT):
        raise AccessDenied(reason=_('Оплата карткою наразі недоступна.'))

    raw_data = validators.validate(
        EvopayPageGenerationValidator,
        await validators.validate_json_request(request),
    )

    async with services.db.acquire() as conn:
        data = await validate_order_request(
            conn=conn,
            bill_id=raw_data['bill_id'],
            user_id=user.id,
            price=raw_data['price'],
            currency=raw_data['currency'],
            description=raw_data['description'],
        )

    redirect_url = await EvopayClient().initiate_payment_process(data)
    return web.json_response({'data': {'url': redirect_url}})


@openapi_docs(
    summary=_('Створити новий рахунок'),
    request_json=CreateBillSchema,
    response=CreateBillResponseSchema,
    response_status=HTTPStatus.CREATED,
)
async def add_bill_from_web(request: web.Request) -> web.Response:
    """
    Create a new bill for a company. This route can be used in unauthenticated cases
    """
    raw_data = await validators.validate_json_request(request)
    user: DBRow | None = request.get(USER_APP_KEY)
    extension_status = None

    async with services.db.acquire() as conn:
        options, analytics_meta = await validate_add_bill_from_web(
            conn=conn,
            raw_data=raw_data,
            user=user,
            source=BillSource.vchasno,
        )

        async with conn.begin():
            bill = await insert_bill(conn, options)

            for service in options.services:
                if utils.is_employee_extension_service(service):
                    extension_status = await add_employees_rate_extension(
                        conn, options=options, bill=bill
                    )

            # Write analytics to handle GA event later during payment
            if analytics_meta:
                await add_analytics_event(
                    conn=conn,
                    event=AnalyticsEvent(
                        id=str(uuid4()),
                        type=AnalyticsEventType.bill_generated,
                        email=options.email,
                        data=analytics_meta.__dict__,
                        bill_id=bill.id,
                        company_id=options.company_id,
                    ),
                )

    await schedule_bill_processing(
        bill_id=bill.id_,
        is_card_payment=options.is_card_payment,
    )
    await send_bill_to_crm(bill_id=bill.id_)

    logger.info(
        'Bill added',
        extra={
            'bill_id': bill.id_,
            'edrpou': options.edrpou,
            'email': options.email,
        },
    )

    response = await bill_to_response(bill, extension_status=extension_status)
    return web.json_response(status=HTTPStatus.CREATED, data=response.to_api())


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_custom_bonus(request: web.Request, user: DBRow) -> web.Response:
    """Activate custom bonus for company by super admin."""
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        await activate_custom_bonus_api(conn=conn, data={**data, 'role_id': user.role_id})
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.activate_custom_bonus,
            extra_details=[
                {
                    'company_id': data['company_id'],
                    'units': data['units'],
                    'period': data['period'],
                }
            ],
        )
    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_custom_bonuses_from_file(request: web.Request, user: DBRow) -> web.Response:
    """Mass custom bonuses activation by super admin.

    To activate bonuses need to upload CSV file with next columns:
        - company EDRPOU
        - bonuses quantity
        - bonuses period
    """
    async with request.app['db'].acquire() as conn:
        valid_data, counters = await validate_activate_custom_bonuses_from_file(
            conn, await validators.validate_post_request(request)
        )
        role_id = user['role_id']
        processed = 0
        with_errors = 0

        for bonus in valid_data:
            company_id = bonus['company_id']
            units = bonus['units']
            period = bonus['period']

            try:
                await activate_custom_bonus_api(
                    conn,
                    {
                        'role_id': role_id,
                        'company_id': company_id,
                        'units': units,
                        'period': period,
                    },
                )
                processed += 1
            except Exception as err:
                with_errors += 1
                logger.warning(
                    'Unable to activate custom bonus from file',
                    exc_info=True,
                    extra={'bonus': bonus, 'error': err},
                )

        counters = {**counters, 'processed': processed, 'with_errors': with_errors}

    return web.json_response(counters)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_debit(request: web.Request, user: DBRow) -> web.Response:
    """Activate debit account for company by super admin."""
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        await activate_debit_api(conn=conn, data={**data, 'role_id': user.role_id})
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.activate_debit,
            extra_details=[
                {
                    'company_id': data['company_id'],
                    'amount': data['amount'],
                    'units': data['units'],
                }
            ],
        )
    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_bill_service(request: web.Request, user: DBRow) -> web.Response:
    bill_id = request.match_info['bill_id']

    async with services.db.acquire() as conn:
        bill = await validate_bill_exists_by_id(conn, bill_id)

        data = await validators.validate_json_request(request, allow_blank=True)
        data['services_type'] = bill.services_type
        valid_data = validators.validate_pydantic(ActivateBillSuperAdmin, data)

        await activate_service_by_bill_payment(
            conn=conn,
            bill=bill,
            source=BillActivationSource.superadmin,
            start_date=valid_data.start_date,
            end_date=valid_data.end_date,
        )
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.activate_bill,
            extra_details=[{'bill_id': bill_id}],
        )

    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def add_bonus(request: web.Request, user: DBRow) -> web.Response:
    """Add new bonus by super admin.

    A company can receive this bonus via ``billing.api.activate_bonus``
    function.

    Bonus must have a unique ``key``.

    Bonus with ``bonus`` type - service bonus used to reward companies for
    special actions (e.g. company registration).
    Bonus with ``promo`` type - promo action created by managers, each one have
    a unique link. If user navigates to this link, his company will receive an
    appropriate bonus.

    Temporarily, don't allow to add bonuses with ``bonus`` type, it can be
    added only via database migration.
    """
    async with services.db.acquire() as conn:
        data = await validate_add_bonus(
            conn,
            {
                **await validators.validate_json_request(request),
                'created_by': user.role_id,
            },
        )
        bonus = await insert_bonus(conn, data)

        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.activate_bonus,
            extra_details=[
                {
                    'key': data['key'],
                    'title': data['title'],
                    'type': data['type'],
                    'units': data['units'],
                    'period': data['period'],
                }
            ],
        )

    return web.json_response(status=201, data=namedtuple_to_dict(bonus), dumps=to_json)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def cancel_bonus(request: web.Request, user: DBRow) -> web.Response:
    """Cancel given bonuses from company's bonus accounts."""
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        await cancel_bonus_api(conn=conn, data={**data, 'role_id': user.role_id})
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.cancel_bonus,
            extra_details=[
                {
                    'company_id': data['company_id'],
                    'units': data['units'],
                }
            ],
        )
    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def cancel_debit_handler(request: web.Request, user: DBRow) -> web.Response:
    """Cancel documents from company's debit accounts."""
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        await cancel_debit_api(conn=conn, data={**data, 'role_id': user.role_id})
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.cancel_debit,
            extra_details=[
                {
                    'company_id': data['company_id'],
                    'units': data['units'],
                }
            ],
        )
    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def delete_bonus(request: web.Request, user: DBRow) -> web.Response:
    """Delete bonus by super admin.

    Allow to delete ``promo`` type bonus only.

    Temporarily, don't allow to delete bonuses with ``bonus`` type, it can be
    deleted only via database migration.
    """
    async with request.app['db'].acquire() as conn:
        data = await validate_delete_bonus(conn, {'bonus_id': request.match_info['bonus_id']})
        await delete_bonus_func(conn, data['bonus_id'])
        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.delete_bonus,
            extra_details=[{'bonus_id': data['bonus_id']}],
        )

    return web.json_response(status=204)


@openapi_docs(
    summary=_('Отримати рахунок'),
    description=_('Отримати дані рахунку по його ідентифікатору'),
    response=RetrieveBillSchema,
)
async def retrieve_bill(request: web.Request) -> web.Response:
    """
    Retrieve information about a bill by its ID. It should work without authentication because
    unauthenticated users can create bills, so they should be able to retrieve them as well.

    Why not use GraphQL? Someone implemented it as a regular endpoint, and no one has been brave
    enough to refactor it.
    """

    async with services.db.acquire() as conn:
        bill = await validate_retrieve_bill(conn, bill_id=request.match_info['bill_id'])
        transaction = await select_latest_payment_status_transaction_by_bill_id(
            conn=conn, bill_id=bill.id_
        )
        description = None
        if transaction and transaction.status_code:
            description = EVOPAY_STATUS_CODE_TO_DESCRIPTION_MAP.get(transaction.status_code)

    # We pretend that document_id becomes available only after the bill is signed and sent,
    # but actually, it's available right after "uploaded" status.
    document_id: str | None = None
    if bill.document_id is not None and bill.status == BillStatus.signed_and_sent:
        document_id = bill.document_id

    response = RetrieveBillSchema(
        id=bill.id_,
        edrpou=BILL_REQUISITES.edrpou,
        status_id=bill.status,
        payment_status=bill.payment_status,
        payment_status_description=description.value if description else None,
        services_type=bill.services_type,
        services=list(bill.services),
        document_id=document_id,
        rate=bill._deprecated_rate,
        count_documents=bill._deprecated_count_documents,
    )

    return web.json_response(response.to_api())


@openapi_docs(
    summary=_('Отримати HTML рахунку'),
    description=_('Отримати HTML рахунку по його ідентифікатору'),
)
async def retrieve_bill_html(request: web.Request) -> web.Response:
    """
    Convert a bill to HTML page for rendering it on URL2PDF service.
    """
    bill_id: str = request.match_info['bill_id']

    async with services.db.acquire() as conn:
        bill = await validate_retrieve_bill(conn, bill_id=bill_id)

    html = await utils.bill_to_html(bill=bill)
    return web.Response(body=html, content_type='text/html')


@client_rate_limit(limit=3)
async def retrieve_bill_pdf(request: web.Request) -> web.StreamResponse:
    bill_id = request.match_info['bill_id']
    async with services.db.acquire() as conn:
        bill = await validate_retrieve_bill(conn, bill_id)

    filename = 'Рахунок'
    headers = {'Content-Disposition': f'Attachment; filename="{filename}.pdf"'}
    response = web.StreamResponse(headers=MultiDict(headers))
    response.content_type = 'application/pdf'
    await response.prepare(request)

    pdf = await bill_to_pdf(bill.id_)
    if not pdf:
        raise ServerError(
            code=Code.temporary_unavailable,
            reason=_('Не вдалося згенерувати файл рахунку'),
        )

    await stream_buffer(io.BytesIO(pdf), response)
    await response.write_eof()

    return response


@client_rate_limit(limit=3)
async def send_bill_email(request: web.Request) -> web.Response:
    bill_id = request.match_info['bill_id']
    async with services.db.acquire() as conn:
        bill = await validate_retrieve_bill(conn, bill_id)

    await schedule_bill_processing(
        bill_id=bill.id_,
        is_card_payment=False,
    )
    return web.Response(status=HTTPStatus.OK)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_bonus(request: web.Request, _: DBRow) -> web.Response:
    """Update bonus data by super admin.

    Allow to update ``promo`` type bonus only.

    Temporarily, don't allow to update bonuses with ``bonus`` type, it can be
    updated only via database migration.
    """
    async with request.app['db'].acquire() as conn:
        data = await validate_update_bonus(
            conn,
            {
                **await validators.validate_json_request(request),
                'bonus_id': request.match_info['bonus_id'],
            },
        )
        bonus = await update_bonus_func(conn, data)

    response_data = namedtuple_to_dict(bonus) if bonus else {}
    return json_response(data=response_data)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def add_company_rate(request: web.Request, user: DBRow) -> web.Response:
    """Add rate and activate rate for company by super admins"""

    raw_data = await validators.validate_json_request(request)
    data = validators.validate(ActivateRateValidator, raw_data)
    data['role_id'] = user.role_id
    data['amount'] = raw_data['amount']

    async with request.app['db'].acquire() as conn:
        rate = await validate_add_company_rate(conn, user, data)

        await api.add_company_rate(conn, rate=rate, user=user)
        company_users = await select_company_users(conn, company_id=rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=rate.rate,
        status=rate.status,
        is_on=True,
        check_active_status=True,
    )

    await send_rate_to_crm(account_id=rate.id_)

    return web.Response(status=HTTPStatus.CREATED)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def increase_employees_amount(request: web.Request, user: DBRow) -> web.Response:
    """
    Increasing employees amount in company which have Ultimate rate.
    - Update billing_accounts.employee_amount
    - Update billing_accounts.amount
    - Update companies_config.admin_config.max_employees_count

    Employee amount and price per employee is required to calculate total price and
    add it up to existing price.
    """

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(IncreaseEmployeesSchema, data)

    update_max_employees_config = True

    async with services.db.acquire() as conn:
        rate = await _validate_rate_exists(conn, request.match_info['rate_id'])
        company_id = rate.company_id
        active_rates = await select_active_company_rates(conn, company_id)

        if not rate.rate.is_ultimate or not rate.employee_amount:
            raise InvalidRequest(
                reason=_('Not allowed to change employee amount for not ultimate rate')
            )
        web_trial = next(
            (r for r in active_rates if r.rate == AccountRate.pro_plus_trial_2022_12), None
        )

        ultimate_rate_max_employees_limit = rate.employee_amount
        extension_employees = await get_rate_extension_employee_units(conn, rate.id_)
        company_config_employees_limit = await validate_company_max_employees_count(
            conn, rate.company_edrpou
        )
        price = calculate_amount_on_adding_employees(
            rate=rate,
            employee_amount=valid_data.employee_amount,
            price_per_employee=valid_data.price_per_employee,
        )

        # Don't update company_config if with ultimate rate also active web_trial
        # (pro_plus_trial_2022_12), and ultimate rate employee limit
        # is no equal to company_config_employees_limit
        if (
            web_trial
            and ultimate_rate_max_employees_limit + extension_employees
            != company_config_employees_limit
        ):
            update_max_employees_config = False

        async with conn.begin():
            # TODO: Investigate, do we need update this field ?
            await _update_company_rate(
                conn=conn,
                rate_id=rate.id_,
                data={
                    'employee_amount': ultimate_rate_max_employees_limit
                    + valid_data.employee_amount,
                    'amount': price,
                },
            )

            if update_max_employees_config:
                await update_billing_company_config(
                    conn=conn,
                    company_id=company_id,
                    config={
                        CompanyLimit.employees.value: company_config_employees_limit
                        + valid_data.employee_amount
                    },
                )

            await process_extra_roles(conn, company_id=company_id)

        logger.info(
            'Change company max_employees_count',
            extra={
                'initiator_type': RoleActivationSource.super_admin.value,
                'initiator_email': user.email,
                'company_id': company_id,
                'company_rate_id': rate.id_,
                'update_max_employees_config': update_max_employees_config,
                'ultimate_rate_max_employees_limit': ultimate_rate_max_employees_limit,
                'company_config_employees_limit': company_config_employees_limit,
                'extension_employees': extension_employees,
                'added_employee_count': valid_data.employee_amount,
            },
        )

    return web.HTTPOk()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_company_rate(request: web.Request, user: DBRow) -> web.Response:
    """
    Update rates range dates or amount. Also activate or deactivate
    rate if needed
    """
    async with request.app['db'].acquire() as conn:
        ctx = await validate_update_company_rate(conn, request)
        await api.update_company_rate_superadmin(conn, ctx, user)
        company_users = await select_company_users(conn, company_id=ctx.rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=ctx.rate.rate,
        status=ctx.new_status,
        is_on=True,
        check_active_status=True,
    )

    return web.Response(status=HTTPStatus.OK)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def delete_company_rate(request: web.Request, user: DBRow) -> web.Response:
    """Delete (deactivate) company rate"""

    async with services.db.acquire() as conn:
        rate = await validate_delete_company_rate(conn, request)
        await api.delete_company_rate(
            conn=conn,
            rate=rate,
            user=user,
        )
        company_users = await select_company_users(conn, company_id=rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=rate.rate,
        status=rate.status,
        is_on=False,
        check_active_status=True,
    )
    await send_rate_to_crm(account_id=rate.id_)

    return web.Response(status=HTTPStatus.OK)


@login_required
async def add_trial_company_rate(request: web.Request, user: DBRow) -> web.Response:
    """
    Add and activate trial rate. The user can activate their own company
    test account on their own
    """
    async with request.app['db'].acquire() as conn:
        rate = await validate_add_trial_company_rate(conn, request, user)
        await api.add_company_rate(conn, rate=rate, user=user)
        company_users = await select_company_users(conn, company_id=rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=rate.rate,
        status=rate.status,
        is_on=True,
    )

    await send_rate_to_crm(account_id=rate.id_)

    return web.Response(status=HTTPStatus.CREATED)


@token_login_required
async def add_trial_company_rate_api(request: web.Request, user: DBRow) -> web.Response:
    """Add and activate trial rate via public api."""
    async with request.app['db'].acquire() as conn:
        rate = await validate_add_trial_company_rate(conn, request, user)
        await api.add_company_rate(conn, rate=rate, user=user)
        company_users = await select_company_users(conn, company_id=rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=rate.rate,
        status=rate.status,
        is_on=True,
    )

    await send_rate_to_crm(account_id=rate.id_)

    return web.Response(status=HTTPStatus.CREATED)


async def activate_trial_from_esputnik_campaign(request: web.Request) -> web.Response:
    """
    Activate trial for a user's company from a esputnik email link
    """

    email = request.rel_url.query.get('email')
    edrpou = request.rel_url.query.get('edrpou')

    if email is None or edrpou is None:
        logger.info('Email or edrpou is not provided for trial activation')
        return redirect(route_name='main')

    marketing_targets = await get_marketing_campaign_audiences()
    current_target = types.MarketingCampaignTarget(email=email, edrpou=edrpou, is_used=False)

    if current_target not in marketing_targets:
        logger.info(
            msg='Fail attempt to activate trial from esputnik marketing campaign',
            extra={
                'email': email,
                'edrpou': edrpou,
                'marketing_targets_count': (marketing_targets and len(marketing_targets)),
            },
        )
        return redirect(route_name='main')

    async with services.db.acquire() as conn:
        company = await auth_db.select_company_by_edrpou(
            conn=conn,
            edrpou=edrpou,
        )
        user = await auth_db.select_user_by_email(
            conn=conn,
            email=email,
            company_edrpou=edrpou,
        )
        if not company or not user:
            logger.info(
                'Attempt to activate esputnik marketing campaign with invalid email or edrpou',
                extra={'email': email, 'edrpou': edrpou},
            )
            return redirect(route_name='main')

        await sa_utils.activate_trial_from_esputnik_marketing_campaign(
            conn=conn,
            user=user,
            company=company,
        )

    # Update marketing targets in redis to restrict second usage of the campaign
    marketing_targets.remove(current_target)
    marketing_targets.add(types.MarketingCampaignTarget(email=email, edrpou=edrpou, is_used=True))
    await set_marketing_campaign_audiences(marketing_targets=list(marketing_targets))

    logger.info(
        msg='Trial activated from esputnik marketing campaign',
        extra={'email': email, 'edrpou': edrpou},
    )

    return web.HTTPFound(
        location=build_url(
            route='app',
            tail=f'/settings/companies/{company.id}/rates',
            get={'esputnik_campaign_trial_activated': 1},
        )
    )


@login_required
async def add_trial_when_employees_limit_reached(request: web.Request, user: DBRow) -> web.Response:
    """
    Activate trial when employees limit reached

    Date introduced - 08/2024
    Part of A/B testing process
    Ticket - https://tabula-rasa.atlassian.net/browse/DOC-6633
    """

    if not get_flag(FeatureFlags.ACTIVATE_TRIAL_WHEN_EMPLOYEES_LIMIT_REACHED):
        raise ServerError(code=Code.temporary_unavailable)

    if not user.company_id:
        raise InvalidRequest(reason=_('Для активації тарифу потрібно завершити реєстрацію'))

    async with services.db.acquire() as conn:
        company = await profile_validators.validate_company_exists(
            conn=conn, company_id=user.company_id
        )
        if not await billing_validators.is_company_eligible_for_trial_when_employees_limit_reached(
            conn=conn,
            company=company,
        ):
            raise AccessDenied(reason=_('Функціонал недоступний для вашої компанії.'))

        company_rate = await validate_add_trial_due_to_employees_limit_reach(conn=conn, user=user)
        await api.add_company_rate(conn=conn, rate=company_rate, user=user)
        company_users = await select_company_users(conn, company_id=company_rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=company_rate.rate,
        status=company_rate.status,
        is_on=True,
    )

    await send_rate_to_crm(account_id=company_rate.id_)

    await services.kafka.send_record(
        topic=topics.ESPUTNIK_SEND_TRIAL_ENABLED_EVENT,
        value={
            'company_id': company_rate.company_id,
            'trial_type': BillingAccountSource.employees_limit_reach_ab_testing.value,
        },
    )

    return web.HTTPCreated()


@login_required
async def add_trial_when_used_paid_feature(request: web.Request, user: DBRow) -> web.Response:
    """
    Activate trial when customer used paid feature

    Date introduced - 11/2024
    https://tabula-rasa.atlassian.net/browse/DOC-6800
    """

    if not get_flag(FeatureFlags.ACTIVATE_TRIAL_WHEN_USED_PAID_FEATURE):
        raise ServerError(code=Code.temporary_unavailable)

    if not user.company_id:
        raise InvalidRequest(reason=_('Для активації тарифу потрібно завершити реєстрацію'))

    async with services.db.acquire() as conn:
        company_rate = await validate_add_trial_due_to_used_paid_feature(conn=conn, user=user)
        await api.add_company_rate(conn=conn, rate=company_rate, user=user)
        company_users = await select_company_users(conn, company_id=company_rate.company_id)

    await update_esputnik_company_rate(
        kafka=request.app['kafka'],
        users=company_users,
        rate=company_rate.rate,
        status=company_rate.status,
        is_on=True,
    )

    await send_rate_to_crm(account_id=company_rate.id_)

    return web.HTTPCreated()


@openapi_docs(
    summary=_('Скасувати рахунок'),
    description=_(
        'Змінює статус рахунку на canceled або refund, '
        'а також скасовує усі надані рахунком ресурси '
        '(тариф, додатковий тариф, документи, розширення користувачів)'
    ),
    request_json=UpdateBillStatusSchema,
    tags=[API_BILLING_TAG],
)
@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def cancel_bill_handler(request: web.Request, admin: DBRow) -> web.Response:
    """
    Superadmin api endpoint to cancel bills and granted resources.

    Exist next cases:
    - rate
        - rate is active
        - rate is not exist (bill not paid)
        - rate is planned
    - rate and additional rate (archive)
        - rate and archive rate is active
        - rate and archive rate is planned
    - rate extension
        - rate extension is active
        - rate extension is active_trial (bill not paid)
        - rate extension not active
        - rate extension is active and company have web trial (not implemented, raise error)
    - units (documents)
        - units is active
        - units already used (user send document, raise error)
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(UpdateBillStatusSchema, raw_data)

    async with services.db.acquire() as conn:
        bill = await validate_cancel_bill(conn, data.bill_id)
        bill_resources = await get_bill_resources(bill)

        async with conn.begin():
            updated_rates = await cancel_bill_resources(
                conn, bill_resources=bill_resources, edrpou=bill.edrpou, initiator=admin
            )
            await cancel_bill(conn, bill, data.status, initiator=admin)

    for rate in updated_rates:
        await send_rate_to_crm(account_id=rate.id_)
    await send_bill_to_crm(bill_id=bill.id_)

    return web.json_response({'status': 'ok'})
