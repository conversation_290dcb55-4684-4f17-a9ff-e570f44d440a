import logging

import yarl
from aiohttp import ClientTimeout, web
from conciergelib import ConciergeProfile, ConciergeUserInfo
from conciergelib.aiohttp.context import (
    get_concierge_user,
    set_concierge_user,
)
from conciergelib.aiohttp.utils import get_request_concierge_user
from conciergelib.protocol import ConciergeError

from api.errors import TemporaryUnavailableError
from app.auth.db import select_base_user
from app.auth.types import BaseUser
from app.lib import tracking
from app.lib.database import DBConnection
from app.services import services
from app.vchasno_profile import select_vchasno_user
from app.vchasno_profile.types import VchasnoUser

logger = logging.getLogger(__name__)

# Reimport next functions to prevent auto removing them as unused imports
__all__ = [
    'get_concierge_user',
    'set_concierge_user',
    'get_request_concierge_user',
]


def is_concierge_cookie_set(request: web.Request) -> bool:
    """
    Get concierge cookie from the request object.
    """
    config = services.config.concierge
    if not config:
        return False

    return request.cookies.get(config.cookie_name) is not None


def delete_concierge_cookie(response: web.Response) -> None:
    """
    Delete concierge cookie from the response object.
    """
    config = services.config.concierge
    if not config:
        return

    # Domain is required here to match domain of the cookie to delete. Without it browser will
    # use the domain of the current app "edo.vchasno.ua" and will not delete the cookie because
    # the concierge cookie is set on ".vchasno.ua".
    response.del_cookie(name=config.cookie_name, domain=config.cookie_domain)


async def sign_in_with_update(
    conn: DBConnection,
    *,
    user_id: str,
    user: BaseUser | None = None,
    vchasno_user: VchasnoUser | None = None,
) -> None:
    """
    Sign in concierge user and update user profile.

    To sign in user to concierge, we attach "user_id" to the anonymous session that was
    created automatically by nginx+concierge. After that, we send information about the user to
    concierge to update or create the user profile associated with the user_id. After that
    concierge will be able to get the user_id and user profile by the concierge session cookie, and
    then pass it to the app via the request headers.

    Concierge sign is also prolonging the session lifetime.
    """
    log_extra = {'user_id': user_id}

    client = services.concierge_client

    # NOTE: the concierge user only exists in requests defined in Nginx config. See
    # devops/helm/vchasno-app/edo repository for more details.
    concierge_user = get_concierge_user()
    if not concierge_user:
        logger.warning(msg='No concierge user found', extra=log_extra)
        return

    tracking.concierge_updates_started_count.inc()
    try:
        logger.info('Concierge sign in', extra=log_extra)
        await client.sign_in(user=concierge_user, user_id=user_id)
        logger.info('Concierge signed in', extra=log_extra)
    except ConciergeError:
        tracking.concierge_updates_failed_count.inc()
        logger.exception('Concierge sign in error')
        return
    except Exception:
        tracking.concierge_updates_failed_count.inc()
        logger.exception('Unhandled concierge sign in error')
        return

    try:
        await update_user_profile(
            conn=conn,
            user_id=user_id,
            edo_user=user,
            vchasno_user=vchasno_user,
        )
    except ConciergeError:
        tracking.concierge_updates_failed_count.inc()
        logger.exception('Concierge update error')
    except Exception:
        tracking.concierge_updates_failed_count.inc()
        logger.exception('Unhandled concierge update error')
        return

    logger.info('Concierge signed in and updated', extra=log_extra)


async def sign_out() -> ConciergeUserInfo | None:
    """
    Sign out current user from concierge
    """
    client = services.concierge_client

    user = get_concierge_user()
    if not user:
        return None

    logger.info('Concierge called sign out', extra={'user_id': user.user_id})
    return await client.sign_out(user=user)


async def sign_in() -> ConciergeUserInfo | None:
    """
    Sign in current user to concierge
    """

    client = services.concierge_client

    user = get_concierge_user()
    if not user:
        return None

    logger.info('Concierge called sign in', extra={'user_id': user.user_id})
    return await client.sign_in(user=user, user_id=user.user_id)  # type: ignore[arg-type]


async def backend_healthcheck() -> None:
    url = yarl.URL(services.config.concierge.backend_url) / '~health/check'
    async with services.http_client.get(url, timeout=ClientTimeout(total=2)) as resp:
        resp.raise_for_status()
        resp_json = await resp.json()
        if resp_json.get('status') != 'ok':
            logger.exception(
                msg='Concierge backend healthcheck failed',
                extra={'response_json': resp_json},
            )
            raise TemporaryUnavailableError()


async def frontend_healthcheck() -> None:
    url = yarl.URL(services.config.concierge.frontend_url) / '~health/check'
    async with services.http_client.get(url, timeout=ClientTimeout(total=2)) as resp:
        resp.raise_for_status()
        resp_json = await resp.json()
        if resp_json.get('status') != 'ok':
            logger.exception(
                msg='Concierge frontend healthcheck failed',
                extra={'response_json': resp_json},
            )
            raise TemporaryUnavailableError()


def _prepare_concierge_profile_update(
    *,
    user_id: str,
    edo_user: BaseUser | None,
    vchasno_user: VchasnoUser | None,
) -> ConciergeProfile | None:
    """
    Build a ConciergeProfile from EDO and/or Vchasno data for concierge sync.

    The profile (basic user info only) is returned to other Vchasno prdocuts during authentication
    """

    # Case when user is registered in both EDO and Vchasno ecosystem. Most data is taken from EDO
    # because we historically used EDO as the main source of truth for user data in concierge.
    if edo_user is not None and vchasno_user is not None:
        return ConciergeProfile(
            user_id=user_id,
            first_name=vchasno_user.first_name,
            last_name=vchasno_user.last_name,
            email=vchasno_user.email,
            # Phones in Vchasno users are created specifically for authentication
            auth_phone=vchasno_user.phone,
            # Phones in EDO users are primarily used for contacting them by support and sales
            # teams. They also are used for 2FA authentication, but we need to move this logic
            # to "auth_phone" field in the future.
            contact_phone=edo_user.phone,
            is_email_confirmed=edo_user.email_confirmed,
            is_super_admin=False,  # TODO: remove this field, noone uses it
        )

    # Case when user is registered in the Vchasno ecosystem but not in EDO yet. This is the
    # new case to support registration with phone only for ecosystem products that support
    # it (TTN, KEP)
    if edo_user is None and vchasno_user is not None:
        return ConciergeProfile(
            user_id=user_id,
            first_name=vchasno_user.first_name,
            last_name=vchasno_user.last_name,
            email=vchasno_user.email,
            contact_phone=None,
            auth_phone=vchasno_user.phone,
            # We don't support email confirmation for Vchasno users without an EDO account yet
            is_email_confirmed=False,
            is_super_admin=False,  # TODO: remove this field, noone uses it
        )

    # Unexpected cases:
    # - User is registered in EDO but not in the Vchasno ecosystem — we always try to create
    #   a user in the Vchasno ecosystem when user registers in EDO, so this case should not
    #   happen.
    # - User is not registered in EDO and not in the Vchasno ecosystem — nothing we can do
    #   here, so we just skip the profile update.
    logger.warning(
        msg='Concierge update profile: user not found',
        extra={'user_id': user_id, 'edo_user': edo_user, 'vchasno_user': vchasno_user},
    )
    return None


async def update_user_profile(
    conn: DBConnection,
    *,
    user_id: str,
    edo_user: BaseUser | None = None,
    vchasno_user: VchasnoUser | None = None,
) -> None:
    """
    Update concierge user profile based on a combination of EDO and Vchasno user objects.

    You can pass already fetched "edo_user" and "vchasno_user" objects to the function to avoid
    fetching them from the database again if you already have them in the context of the request.
    Otherwise, this function will fetch them on demand from the database.

    Check "_prepare_concierge_profile_update" function for more details about how the profile is
    built based on the user objects
    """

    if not edo_user:
        edo_user = await select_base_user(conn, user_id=user_id)
    if not vchasno_user:
        vchasno_user = await select_vchasno_user(conn=conn, user_id=user_id)

    profile = _prepare_concierge_profile_update(
        user_id=user_id,
        edo_user=edo_user,
        vchasno_user=vchasno_user,
    )
    if not profile:
        return

    logger.info(
        'Concierge update profile',
        extra={'user_id': user_id, 'profile': profile.to_log_extra()},
    )

    # HINT: you can catch ConciergeError outside this function to handle requests errors
    await services.concierge_client.update_profile(profile=profile, user_id=user_id)


async def sign_out_user_sessions(*, user_id: str) -> None:
    """
    Logout all user sessions in concierge
    """
    client = services.concierge_client

    try:
        await client.sign_out_sessions(user_id=user_id)
    except Exception:
        logger.exception('Concierge sign out sessions error')
        return
