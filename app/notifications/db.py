import logging

from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.sql import ClauseElement

from app.auth.tables import user_table
from app.auth.types import AuthUser
from app.lib.database import DBConnection, DBRow
from app.lib.types import DataDict
from app.models import exists, select_all, select_one
from app.notifications.enums import (
    PendingNotificationGroup,
)
from app.notifications.tables import (
    notification_table,
    pending_notification_table,
    unsubscription_table,
)

logger = logging.getLogger(__name__)


async def insert_notifications(conn: DBConnection, data: list[DataDict]) -> None:
    if not data:
        logger.warning('Attempt to insert empty notifications list')
        return

    await conn.execute(notification_table.insert().values(data))


async def insert_unsubscription(conn: DBConnection, email: str) -> None:
    await conn.execute(
        insert(unsubscription_table).values({'email': email}).on_conflict_do_nothing()
    )


async def remove_unsubscription(conn: DBConnection, email: str) -> None:
    await conn.execute(unsubscription_table.delete().where(unsubscription_table.c.email == email))


async def is_unsubscribed(conn: DBConnection, email: str) -> bool:
    result = await exists(
        conn=conn,
        select_from=unsubscription_table,
        clause=unsubscription_table.c.email == email,
    )
    if result:
        logger.info('Email is unsubscribed', extra={'email': email})
    return result


async def insert_pending_notification(
    conn: DBConnection,
    created_by: str,
    document_id: str,
    group: PendingNotificationGroup,
) -> str:
    return await conn.scalar(
        pending_notification_table.insert()
        .values(
            created_by=created_by,
            document_id=document_id,
            group=group,
        )
        .returning(pending_notification_table.c.id)
    )


async def select_notification_by(conn: DBConnection, clause: ClauseElement) -> DBRow:
    return await select_one(conn, (notification_table.select().where(clause)))


async def select_pending_notifications_by_document_id(
    conn: DBConnection,
    document_id: str,
) -> list[DBRow]:
    return await select_all(
        conn,
        (
            pending_notification_table.select()
            .where(pending_notification_table.c.document_id == document_id)
            .order_by(pending_notification_table.c.date_created)
        ),
    )


async def update_popup_data_for_user(
    conn: DBConnection, auth_user: AuthUser, popup_data: DataDict
) -> None:
    extra = auth_user.extra or {}
    extra.update(popup_data)
    data = {'extra': extra}

    await conn.scalar(
        user_table.update()
        .values(data)
        .where(user_table.c.id == auth_user.id_)
        .returning(user_table.c.id)
    )
