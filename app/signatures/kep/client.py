import logging
from urllib.parse import urljoin

import ujson
from aiohttp import ClientError, ClientTimeout

from api.errors import Code, Error
from app.auth.types import AuthUser
from app.lib.eusign_utils import client_dynamic_key_session_create
from app.lib.helpers import decode_base64_str, generate_base64_str
from app.lib.types import DataDict
from app.proxy.utils import get_server_key_certificate
from app.services import services
from app.signatures.kep.enums import KEPUrl
from app.signatures.kep.exceptions import (
    VchasnoKepRequestError,
    VchasnoKepRequestInternalError,
    VchasnoKepRequestTimeoutError,
)
from app.signatures.kep.types import KEPCertificate, KEPCertificateResponse

logger = logging.getLogger(__name__)


async def vchasno_kep_integration_request(path: KEPUrl, data: DataDict) -> DataDict:
    config = services.config.kep
    if config is None:
        raise Error(Code.not_implemented)

    cert = await get_server_key_certificate(
        services.http_client, urljoin(config.host, KEPUrl.server_certificates)
    )
    session, session_data = await client_dynamic_key_session_create(
        expire_time=3600,
        certificate=cert,
    )

    encrypted = session.session_encrypt(ujson.dumps(data).encode())
    try:
        async with services.http_client.post(
            urljoin(config.host, path),
            json={
                'authData': generate_base64_str(session_data),
                'encryptedData': generate_base64_str(encrypted),
            },
            headers={
                'Authorization': config.auth_token,
            },
            timeout=ClientTimeout(total=10),
        ) as response:
            response.raise_for_status()
            resp_json = await response.json()
            encrypted_response = resp_json['encryptedData']
            return session.session_decrypt_vchasno_kep(decode_base64_str(encrypted_response))
    except TimeoutError as err:
        raise VchasnoKepRequestTimeoutError(log_extra={'path': path}) from err
    except ClientError as err:
        raise VchasnoKepRequestError(log_extra={'path': path}) from err
    except Exception as err:
        raise VchasnoKepRequestInternalError(log_extra={'path': path}) from err


async def get_kep_user_certificates(auth_user: AuthUser) -> KEPCertificateResponse:
    """
    Get user certificates from KEP
    """

    data = {
        'email': auth_user.email,
    }
    if auth_user.company_edrpou:
        data['edrpou'] = auth_user.company_edrpou

    response_data = await vchasno_kep_integration_request(KEPUrl.user_certificates, data)
    certificates = response_data.get('certificates', [])

    return KEPCertificateResponse(
        certificates=[
            KEPCertificate(**cert)
            for cert in certificates
            if cert['status'] in ('registered', 'restored') and cert['storage_type'] == 'cloud'
        ],
        is_mobile_logged=response_data.get('is_mobile_logged', False),
    )


async def sync_vchasno_profile(data: DataDict) -> None:
    """
    Send Vchasno Profile sync event to KEP to update user profile here
    """
    await vchasno_kep_integration_request(path=KEPUrl.vchasno_profile_sync, data=data)
