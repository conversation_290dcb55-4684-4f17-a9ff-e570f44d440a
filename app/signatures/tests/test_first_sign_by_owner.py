import io
import uuid
from http import HTTPStatus
from typing import Any

import pytest
import ujson

from api.enums import Vendor
from app.auth.db import (
    insert_company,
    insert_token,
    select_company_by_id,
    select_role_by,
    select_user_by_token_hash,
    update_role,
)
from app.auth.enums import RoleStatus
from app.auth.helpers import generate_hash_sha512
from app.billing.db import select_company_accounts, update_account_counter
from app.contacts.db import select_contact_person
from app.documents.db import select_document_by_id
from app.groups.db import insert_group_member
from app.groups.utils import add_group
from app.lib.enums import DocumentStatus, UserRole
from app.models import count
from app.notifications.db import select_pending_notifications_by_document_id
from app.services import services
from app.signatures.db import select_document_signers, select_signatures
from app.signatures.tables import document_signer_table, signature_table
from app.tests.common import (
    AGROYARD_EDRPOU,
    EMPTY_JSON,
    TEST_BILLING_ACCOUNT,
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_UPLOAD_LEFT_COUNTER,
    TEST_USER_EMAIL,
    UAPROM_EDRPOU,
    UKRPOSHTA_EDRPOU,
    VCHASNO_EDRPOU,
    cleanup_on_teardown,
    get_billing_accounts,
    get_document,
    prepare_auth_headers,
    prepare_billing_account,
    prepare_client,
    prepare_company_data,
    prepare_coworker_role,
    prepare_document_data,
    prepare_signature_form_data,
    prepare_user_data,
    send_document,
    set_company_config,
    sign_and_send_document,
    sign_document,
    update_company_config,
)

TEST_COMPANY_USER_EMAIL = '<EMAIL>'
TEST_RECIPIENT_EDRPOU = '********'
TEST_RECIPIENT_CONTACT_EMAIL = '<EMAIL>'
TEST_RECIPIENT_EMAIL = '<EMAIL>'
TEST_RECIPIENT_EMAILS = f'{TEST_RECIPIENT_EMAIL}, {TEST_RECIPIENT_CONTACT_EMAIL}'
TEST_REJECT_COMMENT = 'Document is rejected'


@pytest.mark.parametrize(
    'owner_edrpou, recipient_edrpou, receive_direct',
    [
        (VCHASNO_EDRPOU, TEST_RECIPIENT_EDRPOU, True),
        (TEST_RECIPIENT_EDRPOU, VCHASNO_EDRPOU, False),
    ],
)
@pytest.mark.parametrize('can_receive_inbox, ', [True, False])
@pytest.mark.parametrize(
    'grouped_emails_for_recipient, expected_direct, expected_pending',
    [(False, 1, 0), (True, 0, 1)],
)
async def test_create_pending_notification_signing_doc(
    mailbox,
    aiohttp_client,
    grouped_emails_for_recipient,
    expected_direct,
    expected_pending,
    can_receive_inbox,
    owner_edrpou,
    recipient_edrpou,
    receive_direct,
):
    app, client, owner = await prepare_client(
        aiohttp_client,
        company_edrpou=owner_edrpou,
        create_billing_account=True,
    )

    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=recipient_edrpou,
        can_receive_inbox=can_receive_inbox,
        create_billing_account=True,
    )
    async with app['db'].acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=owner.company_id,
            grouped_emails_for_recipient=grouped_emails_for_recipient,
        )

    document = await prepare_document_data(
        app,
        owner,
        another_recipients=[recipient],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    document_id = document.id

    await sign_and_send_document(
        client,
        document_id,
        owner,
        recipient_email=TEST_RECIPIENT_EMAIL,
        recipient_edrpou=recipient_edrpou,
    )

    # Direct mailbox
    expected = expected_direct * can_receive_inbox * receive_direct
    assert len(mailbox) == expected
    if expected:
        assert mailbox[0]['To'] == TEST_RECIPIENT_EMAIL

    # Pending notifications
    async with app['db'].acquire() as conn:
        count_pending = await select_pending_notifications_by_document_id(conn, document_id)
        assert len(count_pending) == int(expected_pending or not receive_direct)


async def test_document_email_recipient_comma_separated(mailbox, aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_RECIPIENT_EDRPOU,
                'emails': TEST_RECIPIENT_EMAILS.split(', '),
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    try:
        await sign_and_send_document(
            client,
            document.id,
            owner,
            recipient_edrpou=TEST_RECIPIENT_EDRPOU,
            recipient_email=TEST_RECIPIENT_EMAILS,
        )

        async with app['db'].acquire() as conn:
            first_contact = await select_contact_person(
                conn=conn,
                email=TEST_RECIPIENT_EMAIL,
                edrpou=TEST_RECIPIENT_EDRPOU,
                company_id=owner.company_id,
            )
            assert first_contact.main_recipient is True

            second_contact = await select_contact_person(
                conn=conn,
                email=TEST_RECIPIENT_CONTACT_EMAIL,
                edrpou=TEST_RECIPIENT_EDRPOU,
                company_id=owner.company_id,
            )
            assert second_contact.main_recipient is False

        assert len(mailbox) == 2
        assert set(TEST_RECIPIENT_EMAILS.split(', ')) == {mail['To'] for mail in mailbox}
    finally:
        await cleanup_on_teardown(app)


async def test_document_paid_by_recipient(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=UKRPOSHTA_EDRPOU,
        create_billing_account=True,
    )
    await set_company_config(
        app,
        company_id=recipient.company_id,
        allow_pay_as_recipient=True,
    )

    document = await prepare_document_data(app, owner, another_recipients=[recipient])

    try:
        # Owner's balance before sign process
        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        await sign_and_send_document(
            client,
            document.id,
            owner,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )

        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            accounts = await select_company_accounts(conn, recipient.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, recipient.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER
    finally:
        await cleanup_on_teardown(app)


async def test_document_uploaded_by_third_party(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    third_party = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=AGROYARD_EDRPOU,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app, owner, another_recipients=[recipient], uploaded_by=third_party.role_id
    )

    try:
        await sign_and_send_document(
            client,
            document.id,
            owner,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )

        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            accounts = await select_company_accounts(conn, recipient.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, recipient.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            accounts = await select_company_accounts(conn, third_party.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, third_party.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('can_receive_notifications, ', [True, False])
async def test_flow(mailbox, aiohttp_client, can_receive_notifications):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    document = await prepare_document_data(app, owner)
    document_id = document.id

    # Can't sign without recipient
    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(owner),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 400

    # Add recipient
    await prepare_company_data(app, TEST_RECIPIENT_EDRPOU)
    response = await client.post(
        f'/internal-api/documents/{document_id}/change-recipient',
        data=ujson.dumps(
            {
                'edrpou': TEST_RECIPIENT_EDRPOU,
                'emails': [TEST_RECIPIENT_CONTACT_EMAIL],
                'is_emails_hidden': False,
            }
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.edrpou_recipient == TEST_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_RECIPIENT_CONTACT_EMAIL
        assert document.status_id == DocumentStatus.ready_to_be_signed.value

        assert (
            await select_contact_person(
                conn=conn,
                email=TEST_RECIPIENT_CONTACT_EMAIL,
                edrpou=TEST_RECIPIENT_EDRPOU,
                company_id=owner.company_id,
            )
            is None
        )

    # Sign by owner
    first_recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_CONTACT_EMAIL,
        user_role=UserRole.user.value,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        can_receive_inbox=can_receive_notifications,
        can_receive_notifications=can_receive_notifications,
    )

    await sign_document(
        client=client,
        document_id=document_id,
        signer=owner,
        recipient_edrpou=first_recipient.company_edrpou,
        recipient_email=first_recipient.email,
    )

    document = await get_document(document_id=document_id)
    assert document.status == DocumentStatus.signed_and_sent
    assert document.edrpou_recipient == first_recipient.company_edrpou
    assert document.email_recipient == first_recipient.email

    await send_document(
        client=client,
        document_id=document_id,
        sender=owner,
    )
    document = await get_document(document_id=document_id)
    assert document.status == DocumentStatus.signed_and_sent
    assert len(mailbox) == (1 if can_receive_notifications else 0)

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.signed_and_sent.value
        assert document.edrpou_recipient == TEST_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_RECIPIENT_CONTACT_EMAIL

        accounts = await select_company_accounts(conn, owner.company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
        company = await select_company_by_id(conn, owner.company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        first_contact = await select_contact_person(
            conn=conn,
            email=first_recipient.email,
            edrpou=first_recipient.company_edrpou,
            company_id=owner.company_id,
        )
        assert first_contact.main_recipient is True

    # Sign by recipient
    second_recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        user_role=UserRole.user.value,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        create_billing_account=True,
    )

    await sign_document(
        client=client,
        document_id=document_id,
        signer=second_recipient,
    )
    document = await get_document(document_id=document_id)
    assert document.status == DocumentStatus.finished

    await send_document(
        client=client,
        document_id=document_id,
        sender=second_recipient,
    )
    document = await get_document(document_id=document_id)
    assert document.status == DocumentStatus.finished
    assert len(mailbox) == (1 if can_receive_notifications else 0)

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None
        assert document.edrpou_recipient == TEST_RECIPIENT_EDRPOU
        assert document.email_recipient == TEST_RECIPIENT_CONTACT_EMAIL

        accounts = await select_company_accounts(conn, second_recipient.company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
        company = await select_company_by_id(conn, second_recipient.company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

        contact = await select_contact_person(
            conn=conn,
            email=owner.email,
            edrpou=owner.company_edrpou,
            company_id=second_recipient.company_id,
        )
        assert contact.main_recipient is True

        first_contact = await select_contact_person(
            conn=conn,
            email=first_recipient.email,
            edrpou=first_recipient.company_edrpou,
            company_id=owner.company_id,
        )
        assert first_contact.main_recipient is False

        second_contact = await select_contact_person(
            conn=conn,
            email=second_recipient.email,
            edrpou=second_recipient.company_edrpou,
            company_id=owner.company_id,
        )
        assert second_contact.main_recipient is True


async def test_flow_expect_multiple_signatures(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    other_owner = await prepare_user_data(app, email=TEST_COMPANY_USER_EMAIL)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )
    other_recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_RECIPIENT_CONTACT_EMAIL,
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=2,
    )
    document_id = document.id
    url = f'/internal-api/documents/{document_id}/signatures'

    try:
        # First signature by owner
        key_serial_number = str(uuid.uuid4())
        stamp_serial_number = str(uuid.uuid4())
        response = await client.post(
            url,
            data=prepare_signature_form_data(
                owner,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
                recipient=recipient,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 1
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.signed.value

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Second signature by same owner is accepted
        response = await client.post(
            url,
            data=prepare_signature_form_data(owner, recipient=recipient),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        # Signature attempt by other owner with same key/stamp will be rejected
        response = await client.post(
            url,
            data=prepare_signature_form_data(
                other_owner,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
                recipient=recipient,
            ),
            headers=prepare_auth_headers(other_owner),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'key'
        assert data['details']['serial_number'] == key_serial_number

        response = await client.post(
            url,
            data=prepare_signature_form_data(
                other_owner,
                append_stamp_data=True,
                recipient=recipient,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(other_owner),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'stamp'
        assert data['details']['serial_number'] == stamp_serial_number

        # Second signature by other owner
        await sign_and_send_document(
            client,
            document_id,
            other_owner,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 3
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.signed_and_sent.value

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Owner is able to add another signature
        response = await client.post(
            url,
            data=prepare_signature_form_data(other_owner, recipient=recipient),
            headers=prepare_auth_headers(other_owner),
        )
        assert response.status == 201

        # First signature by recipient
        key_serial_number = str(uuid.uuid4())
        stamp_serial_number = str(uuid.uuid4())
        response = await client.post(
            url,
            data=prepare_signature_form_data(
                recipient,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 201, await response.json()

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 5
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.approved.value

        # Second attempt for signature by same recipient will be accepted
        response = await client.post(
            url,
            data=prepare_signature_form_data(recipient),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 201

        # Signature attempt with previously used key/stamp will be rejected
        response = await client.post(
            url,
            data=prepare_signature_form_data(
                other_recipient,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
            ),
            headers=prepare_auth_headers(other_recipient),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'key'
        assert data['details']['serial_number'] == key_serial_number

        response = await client.post(
            url,
            data=prepare_signature_form_data(
                other_recipient,
                append_stamp_data=True,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(other_recipient),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'stamp'
        assert data['details']['serial_number'] == stamp_serial_number

        # Second signature by recipient
        await sign_and_send_document(client, document_id, other_recipient)

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 7
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

        # Another attempt to adding signature by other recipient will be accepted
        response = await client.post(
            url,
            data=prepare_signature_form_data(other_recipient),
            headers=prepare_auth_headers(other_recipient),
        )
        assert response.status == 201

        # Final check for billing counts
        async with app['db'].acquire() as conn:
            # Owner
            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

            # Recipient
            accounts = await select_company_accounts(conn, recipient.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, recipient.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER
    finally:
        await cleanup_on_teardown(app)


async def test_flow_expect_multiple_signatures_sign_flow_invoice(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    other_owner = await prepare_user_data(app, email=TEST_COMPANY_USER_EMAIL)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        owner,
        another_recipients=[recipient],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=0,
    )
    document_id = document.id

    send_url = f'/internal-api/documents/{document_id}/send'
    signatures_url = f'/internal-api/documents/{document_id}/signatures'

    try:
        response = await client.post(
            signatures_url,
            data=prepare_signature_form_data(owner, recipient=recipient),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        response = await client.post(send_url, data=EMPTY_JSON, headers=prepare_auth_headers(owner))
        assert response.status == 200

        response = await client.post(
            signatures_url,
            data=prepare_signature_form_data(other_owner, recipient=recipient),
            headers=prepare_auth_headers(other_owner),
        )
        assert response.status == 201

        response = await client.post(
            send_url, data=EMPTY_JSON, headers=prepare_auth_headers(other_owner)
        )
        assert response.status == 200

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 2
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
    finally:
        await cleanup_on_teardown(app)


async def test_flow_internal_document(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    document = await prepare_document_data(
        app, owner, is_internal=True, status_id=DocumentStatus.ready_to_be_signed.value
    )
    document_id = document.id
    url_add_signature = f'/internal-api/documents/{document_id}/signatures'

    try:
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(owner),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 1
            document = await select_document_by_id(conn, document_id)
            assert document.edrpou_recipient is None
            assert document.email_recipient is None
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
    finally:
        await cleanup_on_teardown(app)


async def test_flow_internal_document_multiple_signatures(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    second_owner = await prepare_user_data(app, email=TEST_COMPANY_USER_EMAIL)
    other_company_user = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        is_internal=True,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=0,
        another_owners=[second_owner],
        create_owner_signer=True,
    )
    document_id = document.id
    url_add_signature = f'/internal-api/documents/{document_id}/signatures'

    try:
        # First signature by owner
        key_serial_number = str(uuid.uuid4())
        stamp_serial_number = str(uuid.uuid4())
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(
                owner,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 1
            document = await select_document_by_id(conn, document_id)
            assert document.edrpou_recipient is None
            assert document.email_recipient is None
            assert document.status_id == DocumentStatus.signed.value

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Second signature by same owner is accepted
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(owner),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 2
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.signed.value

        # Signature attempt by other owner with same key/stamp will be rejected
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(
                second_owner,
                append_stamp_data=True,
                key_serial_number=key_serial_number,
            ),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'key'
        assert data['details']['serial_number'] == key_serial_number

        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(
                second_owner,
                append_stamp_data=True,
                stamp_serial_number=stamp_serial_number,
            ),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert data['details']['type'] == 'stamp'
        assert data['details']['serial_number'] == stamp_serial_number

        # Signature by second owner
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(second_owner),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 3
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Second owner is able to add another signature
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(second_owner),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 201

        # Don't allow signatures from another company
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(other_company_user),
            headers=prepare_auth_headers(other_company_user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_flow_internal_document_reject(mailbox, aiohttp_client, fcm_message_handler):
    app, client, owner = await prepare_client(
        aiohttp_client, create_billing_account=True, create_mobile_active_session=True
    )
    second_owner = await prepare_user_data(
        app, email=TEST_COMPANY_USER_EMAIL, create_mobile_active_session=True
    )

    document = await prepare_document_data(
        app,
        owner,
        is_internal=True,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=0,
        another_owners=[second_owner],
        create_owner_signer=True,
    )
    document_id = document.id
    url_add_signature = f'/internal-api/documents/{document_id}/signatures'
    url_reject = f'/internal-api/documents/{document_id}/reject'

    try:
        # Reject document by owner
        response = await client.post(
            url_reject,
            data=ujson.dumps({'text': TEST_REJECT_COMMENT}),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 200, await response.text()

        async with app['db'].acquire() as conn:
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.reject.value
            assert document.date_finished is not None

        assert len(mailbox) == 2
        emails = {mailbox[0]['To'], mailbox[1]['To']}
        assert emails == {owner.email, second_owner.email}

        assert len(fcm_message_handler) == 2
        for message in fcm_message_handler:
            assert message['message']['notification']['title'] == 'Підписання було відхилено'

        # First signature by owner
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(owner, append_stamp_data=True),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 1
            document = await select_document_by_id(conn, document_id)
            assert document.edrpou_recipient is None
            assert document.email_recipient is None
            assert document.status_id == DocumentStatus.signed.value

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Reject document by second owner
        response = await client.post(
            url_reject,
            data=ujson.dumps({'text': TEST_REJECT_COMMENT}),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 200, await response.text()

        async with app['db'].acquire() as conn:
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.reject.value
            assert document.date_finished is not None

        assert len(mailbox) == 4
        emails = {mailbox[0]['To'], mailbox[1]['To']}
        assert emails == {owner.email, second_owner.email}

        # Second signature by second owner
        response = await client.post(
            url_add_signature,
            data=prepare_signature_form_data(second_owner),
            headers=prepare_auth_headers(second_owner),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            assert await count(conn, signature_table) == 2
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'recipient_email, recipient_company_edrpou, can_receive_notifications, expected_response',
    [
        pytest.param(
            TEST_RECIPIENT_EMAIL,
            TEST_COMPANY_EDRPOU,
            True,
            {
                'status': HTTPStatus.BAD_REQUEST,
                'reason': 'Власник документу не може бути одержувачем',
            },
            id='self_edrpou_with_notifications',
        ),
        pytest.param(
            TEST_USER_EMAIL,
            TEST_RECIPIENT_EDRPOU,
            True,
            {
                'status': HTTPStatus.OK,
            },
            id='self_email_with_notifications',
        ),
        pytest.param(
            TEST_USER_EMAIL,
            TEST_RECIPIENT_EDRPOU,
            False,
            {
                'status': HTTPStatus.OK,
            },
            id='self_email_without_notifications',
        ),
    ],
)
async def test_flow_sign_and_send_to_self(
    mailbox,
    aiohttp_client,
    recipient_email,
    recipient_company_edrpou,
    can_receive_notifications,
    expected_response: dict,
):
    app, client, sender = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        can_receive_inbox=can_receive_notifications,
        can_receive_notifications=can_receive_notifications,
    )
    sender_headers = prepare_auth_headers(sender)
    is_self_edrpou = sender.company_edrpou == recipient_company_edrpou

    if is_self_edrpou:
        assert sender.company_edrpou == recipient_company_edrpou
        recipient = await prepare_user_data(
            app,
            company_edrpou=recipient_company_edrpou,
            email=recipient_email,
            can_receive_inbox=can_receive_notifications,
            can_receive_notifications=can_receive_notifications,
        )
    else:
        # Create 2nd role for sender with different company EDRPOU
        async with app['db'].acquire() as conn:
            recipient_company_id = await insert_company(
                conn,
                {
                    'edrpou': recipient_company_edrpou,
                    'is_legal': True,
                    'upload_documents_left': TEST_UPLOAD_LEFT_COUNTER,
                },
            )
            await prepare_billing_account(conn, recipient_company_id)
            recipient_role = await prepare_coworker_role(
                conn,
                {
                    'company_id': recipient_company_id,
                    'status': RoleStatus.active,
                    'user_id': sender.id,
                    'can_receive_inbox': can_receive_notifications,
                    'can_receive_notifications': can_receive_notifications,
                },
            )
            recipient_token = await insert_token(
                conn, {'role_id': recipient_role.id, 'vendor': Vendor.api.value}
            )
            recipient = await select_user_by_token_hash(
                conn, generate_hash_sha512(recipient_token), raw_token=recipient_token
            )
    recipient_headers = prepare_auth_headers(recipient)

    document = await prepare_document_data(app, sender)
    document_id = document.id

    response = await client.post(
        f'/internal-api/documents/{document_id}/change-recipient',
        data=ujson.dumps(
            {
                'edrpou': recipient_company_edrpou,
                'emails': recipient_email.split(', '),
                'is_emails_hidden': False,
            }
        ),
        headers=sender_headers,
    )
    assert response.status == expected_response['status']
    if response.status != HTTPStatus.OK:
        data = await response.json()
        assert data['reason'] == expected_response['reason']
        return

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.edrpou_recipient == recipient_company_edrpou
        assert document.email_recipient == recipient_email
        assert document.status_id == DocumentStatus.ready_to_be_signed.value

    assert len(mailbox) == 0

    await sign_and_send_document(
        client,
        document_id,
        sender,
        sign_data=prepare_signature_form_data(sender, owner=sender, recipient=recipient),
    )

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.signed_and_sent.value

    assert len(mailbox) == (1 if can_receive_notifications else 0)

    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=prepare_signature_form_data(recipient),
        headers=recipient_headers,
    )
    assert response.status == 201

    response = await client.post(
        f'/internal-api/documents/{document_id}/send',
        data=EMPTY_JSON,
        headers=recipient_headers,
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        if is_self_edrpou:
            assert document.status_id == DocumentStatus.signed_and_sent.value
        else:
            assert document.status_id == DocumentStatus.finished.value
            assert document.date_finished is not None

        # Sender
        accounts = await select_company_accounts(conn, sender.company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
        company = await select_company_by_id(conn, sender.company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

        # Recipient
        accounts = await select_company_accounts(conn, recipient.company_id)
        assert accounts[0].units_left == (
            TEST_BILLING_ACCOUNT['units'] - 1 if is_self_edrpou else TEST_BILLING_ACCOUNT['units']
        )
        company = await select_company_by_id(conn, recipient.company_id)
        assert company.upload_documents_left == (
            TEST_UPLOAD_LEFT_COUNTER - 1 if is_self_edrpou else TEST_UPLOAD_LEFT_COUNTER
        )


async def test_owner_billing_overlimit(aiohttp_client, monkeypatch):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    accounts = await get_billing_accounts(company_id=owner.company_id)
    account_id = accounts[0].id

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    async with services.db.acquire() as conn:
        # Default test limit = 30, set available to 0
        await update_account_counter(conn, account_id, units=-30)

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        another_recipients=[recipient],
        vendor='API',
    )
    response = await client.post(
        f'/internal-api/documents/{document.id}/signatures',
        data=prepare_signature_form_data(owner, recipient=recipient),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 400

    data = await response.json()
    assert data['code'] == 'billing_overlimit'


@pytest.mark.parametrize('can_receive_notifications, ', [True, False])
async def test_reject_by_recipient(mailbox, aiohttp_client, can_receive_notifications):
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)
    owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_receive_rejects=can_receive_notifications,
        can_receive_notifications=can_receive_notifications,
    )
    another_owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_receive_rejects=can_receive_notifications,
        can_receive_notifications=can_receive_notifications,
    )
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        user_role=UserRole.user.value,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    another_recipient = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        status_id=DocumentStatus.signed_and_sent.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=2,
        another_owners=[another_owner],
        another_recipients=[recipient, another_recipient],
    )

    document_id = document.id

    try:
        response = await client.post(
            f'/internal-api/documents/{document_id}/reject',
            data=ujson.dumps({'text': TEST_REJECT_COMMENT}),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 200, await response.text()

        async with app['db'].acquire() as conn:
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == DocumentStatus.reject.value
            assert document.date_finished is not None

        if can_receive_notifications:
            assert len(mailbox) == 2
            emails = {mailbox[0]['To'], mailbox[1]['To']}
            assert emails == {owner.email, another_owner.email}

        else:
            assert len(mailbox) == 0
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'edrpou, email',
    [
        ('1234567', '<EMAIL>'),
        (TEST_RECIPIENT_EDRPOU, 'invalid-email@recipient'),
        (TEST_RECIPIENT_EDRPOU, 'кириллицей@vchasno.com.ua'),
    ],
)
async def test_recipient_data(aiohttp_client, edrpou, email):
    app, client, owner = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner)
    document_id = document.id

    try:
        response = await client.post(
            f'/internal-api/documents/{document_id}/change-recipient',
            data=ujson.dumps(
                {
                    'edrpou': edrpou,
                    'emails': email.split(', '),
                    'is_emails_hidden': False,
                }
            ),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 400

        response = await client.post(
            f'/internal-api/documents/{document_id}/signatures',
            data=prepare_signature_form_data(owner, recipient_edrpou=edrpou, recipient_email=email),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 400
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('recipient_p7s, recipient_status', [(b'p7s', 201), (None, 201)])
async def test_internal_signature(aiohttp_client, recipient_p7s, recipient_status):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': [TEST_RECIPIENT_EMAIL]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    try:
        await sign_and_send_document(
            client,
            document.id,
            owner,
            sign_data=prepare_signature_form_data(
                owner,
                p7s=io.BytesIO(b'p7s'),
                archive='zip',
                recipient_edrpou=TEST_RECIPIENT_EDRPOU,
                recipient_email=TEST_RECIPIENT_EMAILS,
            ),
        )

        async with app['db'].acquire() as conn:
            signatures = await select_signatures(conn, [document.id])

        assert len(signatures) == 1
        assert signatures[0].is_internal
        assert signatures[0].internal_file_name == 'p7s.zip'

        response = await client.post(
            f'/internal-api/documents/{document.id}/signatures',
            data=prepare_signature_form_data(
                recipient, p7s=recipient_p7s and io.BytesIO(recipient_p7s)
            ),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == recipient_status
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'exists_recipient_role, role_status, expected_role_status',
    [
        (False, None, None),
        (True, RoleStatus.active, RoleStatus.active),
        (True, RoleStatus.deleted, RoleStatus.deleted),
        (True, RoleStatus.denied, RoleStatus.denied),
        (True, RoleStatus.pending, RoleStatus.pending),
    ],
)
async def test_sign_and_send_to_existing_user(
    mailbox, aiohttp_client, exists_recipient_role, role_status, expected_role_status
):
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient = await prepare_user_data(
        app, company_edrpou=TEST_RECIPIENT_EDRPOU, email=TEST_RECIPIENT_EMAIL
    )

    # New recipient's role is missing or inactive
    new_recipient_edrpou = '********'
    assert new_recipient_edrpou != recipient.company_edrpou

    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=False,
        document_recipients=[{'edrpou': new_recipient_edrpou, 'emails': [recipient.email]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    async with services.db.acquire() as conn:
        if exists_recipient_role:
            company_id = await insert_company(
                conn, {'edrpou': new_recipient_edrpou, 'is_legal': True}
            )
            await prepare_coworker_role(
                conn,
                {
                    'company_id': company_id,
                    'status': role_status,
                    'user_role': UserRole.user.value,
                    'user_id': recipient.id,
                },
            )

    assert len(mailbox) == 0

    # Set recipient to existing user but another inactive/missing role
    await sign_and_send_document(
        client,
        document.id,
        user,
        sign_data=prepare_signature_form_data(
            user,
            p7s=io.BytesIO(b'p7s'),
            archive='zip',
            recipient_edrpou=new_recipient_edrpou,
            recipient_email=recipient.email,
        ),
    )

    # Recipient has existing or new pending role
    async with services.db.acquire() as conn:
        updated_recipient = await select_role_by(conn, new_recipient_edrpou, recipient.id, True)

    if expected_role_status:
        assert updated_recipient.status == expected_role_status
        assert len(mailbox) == (
            0 if expected_role_status in {RoleStatus.denied, RoleStatus.deleted} else 1
        )
    else:
        assert updated_recipient is None
        assert len(mailbox) == 1


async def test_signature_details(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        another_recipients=[recipient],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document_id = document.id

    try:
        # Signed by owner
        await sign_and_send_document(
            client,
            document_id,
            owner,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )

        async with app['db'].acquire() as conn:
            signatures = await select_signatures(conn, document_ids=[document_id])
            assert len(signatures) == 1
            owner_signature = signatures[0]
            assert owner_signature.role_id == owner.role_id

        # Signed by recipient
        response = await client.post(
            f'/internal-api/documents/{document_id}/signatures',
            data=prepare_signature_form_data(recipient),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            signatures = await select_signatures(conn, document_ids=[document_id])
            assert len(signatures) == 2
            recipient_signature = next(
                s for s in signatures if s.key_owner_edrpou == recipient.company_edrpou
            )
            assert recipient_signature.role_id == recipient.role_id
    finally:
        await cleanup_on_teardown(app)


async def test_signature_after_deleted_recipient_role(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        another_recipients=[recipient],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document_id = document.id

    try:
        # Recipient role was deleted
        async with app['db'].acquire() as conn:
            await update_role(
                conn=conn,
                role_id=recipient.role_id,
                data={'status': RoleStatus.deleted},
            )

            # Signed by owner
            response = await client.post(
                f'/internal-api/documents/{document_id}/signatures',
                data=prepare_signature_form_data(owner, recipient=recipient),
                headers=prepare_auth_headers(owner),
            )
            assert response.status == 201

            signatures = await select_signatures(conn, document_ids=[document_id])
            assert len(signatures) == 1
    finally:
        await cleanup_on_teardown(app)


async def test_sign_by_recipient_after_deleted_sender_role(aiohttp_client):
    app, client, sender = await prepare_client(
        aiohttp_client, company_edrpou=UAPROM_EDRPOU, email='<EMAIL>'
    )
    recipient = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(
        app,
        sender,
        create_document_access_for_recipients=True,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    document_id = document.id

    try:
        async with app['db'].acquire() as conn:
            await update_role(
                conn=conn,
                role_id=sender.role_id,
                data={'status': RoleStatus.deleted},
            )
        response = await client.post(
            f'/internal-api/documents/{document_id}/signatures',
            data=prepare_signature_form_data(recipient),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 201
    finally:
        await cleanup_on_teardown(app)


async def test_sign_multiple_signers(telegrambox, mailbox, aiohttp_client):
    signer_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

    parallel_signing = False

    app, client, owner = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
    )
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    signers_map = {
        '<EMAIL>': owner,
        '<EMAIL>': coworker_1,
        '<EMAIL>': coworker_2,
    }
    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=3,
        expected_recipient_signatures=2,
    )

    async with app['db'].acquire() as conn:
        signers_data = [
            {
                'document_id': document.id,
                'company_id': signers_map[email].company_id,
                'role_id': signers_map[email].role_id,
                'order': None if parallel_signing else idx,
            }
            for idx, email in enumerate(signer_emails, start=1)
        ]

        await conn.execute(document_signer_table.insert().values(signers_data))

    document_id = document.id
    url = f'/internal-api/documents/{document_id}/signatures'

    # First signature by owner
    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            owner,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201

    assert len(mailbox) == 1
    assert len(telegrambox) == 1

    async with app['db'].acquire() as conn:
        assert await count(conn, signature_table) == 1
        document = await select_document_by_id(conn, document_id)
        assert document.status_id == DocumentStatus.signed.value

        accounts = await select_company_accounts(conn, owner.company_id)
        assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
        company = await select_company_by_id(conn, owner.company_id)
        assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1

    # Second signature by other owner
    await sign_and_send_document(
        client,
        document_id,
        coworker_1,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
    )


async def test_sign_multiple_signers_with_group_parallel(aiohttp_client):
    """

    Given:
        - document with 4 signers parallel signing
        - 3 signers by roles and 1 signer by group
    When:
       - User from group sign document
    Then:
       - Document signers table should be updated:
         - group as signer should be marked as signed
         - user as signer should be marked as signed
    """
    app, client, owner = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
    )
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=3,
        expected_recipient_signatures=2,
    )

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=owner,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=owner.role_id,
        )

        signers_data: list[Any] = [
            {
                'document_id': document.id,
                'company_id': u.company_id,
                'role_id': u.role_id,
                'group_id': None,
                'order': None,
            }
            for u in [owner, coworker_1, coworker_2]
        ]
        signers_data.append(
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': None,
            }
        )
        await conn.execute(document_signer_table.insert().values(signers_data))

    url = f'/internal-api/documents/{document.id}/signatures'

    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            owner,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        signatures = await select_signatures(
            conn=conn,
            document_ids=[document.id],
        )
        document_signers = await select_document_signers(
            conn=conn,
            document_id=document.id,
        )
        assert len(signatures) == 1
        assert len(document_signers) == 4

        document_signing_map = {(ds.role_id, ds.group_id): ds for ds in document_signers}

        # Signed as a group
        assert document_signing_map[(None, group.id)].date_signed is not None
        # Signed as a role
        assert document_signing_map[(owner.role_id, None)].date_signed is not None
        # Other users
        assert document_signing_map[(coworker_2.role_id, None)].date_signed is None
        assert document_signing_map[(coworker_1.role_id, None)].date_signed is None


async def test_sign_multiple_signers_with_group_ordered_group_after_role(aiohttp_client):
    """

    Given:
        - document with 4 signers ordered signing
        - 3 signers by roles and 1 signer by group
          where role(group nember), role, group, role
        - document is signed by first role
    When:
       - Next user sign document
    Then:
       - Document signers table should be updated:
         - user as signer should be marked as signed
         - group as signer should be marked as signed as well
    """

    app, client, owner = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
    )
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=3,
        expected_recipient_signatures=2,
    )

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=owner,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=owner.role_id,
        )

        signers_data = [
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': owner.role_id,
                'group_id': None,
                'order': 1,
            },
            {
                'document_id': document.id,
                'company_id': coworker_1.company_id,
                'role_id': coworker_1.role_id,
                'group_id': None,
                'order': 2,
            },
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': 3,
            },
            {
                'document_id': document.id,
                'company_id': coworker_2.company_id,
                'role_id': coworker_2.role_id,
                'group_id': None,
                'order': 4,
            },
        ]
        await conn.execute(document_signer_table.insert().values(signers_data))

    url = f'/internal-api/documents/{document.id}/signatures'

    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            owner,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        document_signers = await select_document_signers(
            conn=conn,
            document_id=document.id,
        )

    document_signing_map = {(ds.role_id, ds.group_id): ds for ds in document_signers}

    # Signed not be signed as a group because of order
    assert document_signing_map[(None, group.id)].date_signed is None
    # Signed as a role
    assert document_signing_map[(owner.role_id, None)].date_signed is not None
    # Other users
    assert document_signing_map[(coworker_1.role_id, None)].date_signed is None
    assert document_signing_map[(coworker_2.role_id, None)].date_signed is None

    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            coworker_1,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(coworker_1),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        signatures = await select_signatures(
            conn=conn,
            document_ids=[document.id],
        )
        document_signers = await select_document_signers(
            conn=conn,
            document_id=document.id,
        )
        assert len(signatures) == 2
        assert len(document_signers) == 4

        document_signing_map = {(ds.role_id, ds.group_id): ds for ds in document_signers}

        # Signed as a group
        assert document_signing_map[(None, group.id)].date_signed is not None
        # Signed as a role
        assert document_signing_map[(coworker_1.role_id, None)].date_signed is not None
        assert document_signing_map[(owner.role_id, None)].date_signed is not None
        # Other users
        assert document_signing_map[(coworker_2.role_id, None)].date_signed is None


async def test_sign_multiple_signers_with_group_unordered(aiohttp_client):
    """
    Given:
        - document with 3 signers unordered signing
        - 1 signer by roles and 2 signers by group
          where role(group1 member), group1, group2
    When:
       - Role sign document
    Then:
       - Document signers table should be updated:
         - user as signer should be marked as signed
         - group as signer should be marked as signed as well
    """

    app, client, owner = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
    )
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=3,
        expected_recipient_signatures=2,
    )

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=owner,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=owner.role_id,
        )
        group_2 = await add_group(
            conn=conn,
            name='test2',
            user=owner,
        )
        await insert_group_member(
            conn=conn,
            group_id=group_2.id,
            role_id=coworker_1.role_id,
        )

        signers_data = [
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': owner.role_id,
                'group_id': None,
                'order': None,
            },
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': None,
            },
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': None,
                'group_id': group_2.id,
                'order': None,
            },
        ]
        await conn.execute(document_signer_table.insert().values(signers_data))

    url = f'/internal-api/documents/{document.id}/signatures'

    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            owner,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        document_signers = await select_document_signers(
            conn=conn,
            document_id=document.id,
        )

    document_signing_map = {(ds.role_id, ds.group_id): ds for ds in document_signers}

    # Signed be signed as a group because of being member of group
    assert document_signing_map[(None, group.id)].date_signed is not None
    # Signed as a role
    assert document_signing_map[(owner.role_id, None)].date_signed is not None
    # Not signed as another group
    assert document_signing_map[(None, group_2.id)].date_signed is None


async def test_sign_multiple_signers_with_group_ordered_group(aiohttp_client):
    """

    Given:
        - document with 1 signers ordered signing
        - 1 signer by group
        - document is signed by group member
    When:
       - Next sign document
    Then:
       - Document signers table should be updated:
         - group as signer should be marked as signed
    """

    app, client, owner = await prepare_client(
        aiohttp_client,
        email='<EMAIL>',
        create_billing_account=True,
    )

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=3,
        expected_recipient_signatures=2,
    )

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=owner,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=owner.role_id,
        )

        signers_data = [
            {
                'document_id': document.id,
                'company_id': owner.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': 1,
            },
        ]
        await conn.execute(document_signer_table.insert().values(signers_data))

    url = f'/internal-api/documents/{document.id}/signatures'

    key_serial_number = str(uuid.uuid4())
    stamp_serial_number = str(uuid.uuid4())
    response = await client.post(
        url,
        data=prepare_signature_form_data(
            owner,
            append_stamp_data=True,
            key_serial_number=key_serial_number,
            recipient=recipient,
            stamp_serial_number=stamp_serial_number,
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        document_signers = await select_document_signers(
            conn=conn,
            document_id=document.id,
        )

    document_signing_map = {(ds.role_id, ds.group_id): ds for ds in document_signers}

    # Signed be signed as a group
    assert document_signing_map[(None, group.id)].date_signed is not None
    assert document_signing_map[(None, group.id)].group_signer_id == owner.role_id
