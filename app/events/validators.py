import datetime
import typing

import pydantic
import trafaret as t
from aiohttp import web
from dateutil.relativedelta import relativedelta
from trafaret_validator import TrafaretValidator

from api.errors import (
    Code,
    Error,
    TemporaryUnavailableError,
)
from api.utils import is_public_api_request
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.events import db
from app.events.db import select_actions_report_file, select_actions_report_file_by_report_id
from app.events.enums import ActionsReportType
from app.events.models import ActionsReportFile
from app.events.utils import REPORT_FOR_LAST_DAYS
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.datetime_utils import to_local_datetime, utc_now
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.services import services


class GetEvents(TrafaretValidator):
    date_created_from = validators.LeftDateTime() | t.Null()
    date_created_to = validators.RightDateTime() | t.Null()
    cursor = t.Int(gte=1) | t.Null()


class ExportCtx(typing.NamedTuple):
    date_to: datetime.datetime
    date_from: datetime.datetime
    cursor: int


class DownloadActionsReportFileValidator(pydantic.BaseModel):
    file_id: pv.UUID


class DownloadActionsReportFileValidatorByReportId(pydantic.BaseModel):
    report_id: pv.UUID


class ActionsReportRequestValidator(pydantic.BaseModel):
    date_from: pv.Datetime
    date_to: pv.Datetime
    is_public: bool

    @pydantic.model_validator(mode='after')
    def validate_date_ranges(self) -> typing.Self:
        reason = _(
            f'Період завантаження не повинен перевищувати {30 if self.is_public else 365} діб.'
        )
        assert self.date_from.date() < self.date_to.date(), reason
        assert (
            self.date_from.date()
            >= (utc_now() - relativedelta(days=30 if self.is_public else 365)).date()
        ), reason

        # User passes naive date format, such as 2023-01-01. We assume that the user wants to
        # get the report in Kyiv time zone, so we attach Kyiv time zone to both dates.
        date_from = datetime.datetime.combine(self.date_from, datetime.datetime.min.time())
        date_from = to_local_datetime(date_from)
        self.date_from = date_from

        date_to = datetime.datetime.combine(self.date_to, datetime.datetime.min.time())
        date_to = date_to + datetime.timedelta(hours=23, minutes=59, seconds=59)
        date_to = to_local_datetime(date_to)
        self.date_to = date_to

        return self


async def validate_download_actions_report_file(
    conn: DBConnection,
    request: web.Request,
    user: User,
) -> ActionsReportFile:
    """
    Validate the request for downloading actions report file
    """
    validate_user_permission(user, {'can_download_actions'})

    raw_data = request.match_info

    if 'file_id' in raw_data:
        file_id_data = validators.validate_pydantic(
            DownloadActionsReportFileValidator,
            raw_data,
        )

        file = await select_actions_report_file(
            conn=conn,
            file_id=file_id_data.file_id,
            company_id=user.company_id,
        )

    elif 'report_id' in raw_data:
        report_id_data = validators.validate_pydantic(
            DownloadActionsReportFileValidatorByReportId,
            raw_data,
        )

        file = await select_actions_report_file_by_report_id(
            conn=conn,
            report_id=report_id_data.report_id,
            company_id=user.company_id,
        )
    else:
        raise NotImplementedError('Invalid request')

    if not file:
        raise Error(
            Code.report_not_found,
            reason=_('Звіт недоступний для завантаження'),
        )

    return file


def validate_event_export(data: DataDict) -> ExportCtx:
    valid_data = validators.validate(GetEvents, data)

    date_to = valid_data['date_created_to'] or utc_now()
    date_from = valid_data['date_created_from'] or date_to - datetime.timedelta(
        days=REPORT_FOR_LAST_DAYS
    )

    diff = (date_to - date_from).days

    if diff < 1 or diff > REPORT_FOR_LAST_DAYS:
        raise Error(Code.invalid_time_range, details={'days': REPORT_FOR_LAST_DAYS})

    return ExportCtx(date_to=date_to, date_from=date_from, cursor=valid_data['cursor'] or 0)


async def validate_create_report_request(
    request: web.Request,
    user: User,
    report_type: ActionsReportType,
) -> ActionsReportRequestValidator:
    if get_flag(FeatureFlags.DISABLE_EVENT_REPORT_TASK):
        raise TemporaryUnavailableError(
            reason=_(
                'Зараз ми тимчасово вимкнули завантаження звітів про події. Будь ласка, спробуйте '
                'трохи пізніше'
            )
        )

    validate_user_permission(user, {'can_download_actions'})

    raw_data = await validators.validate_json_request(request)

    # Check if the request from public API
    raw_data['is_public'] = is_public_api_request(request)

    data = validators.validate_pydantic(ActionsReportRequestValidator, raw_data)

    async with redis_lock(name=f'actions_report:{report_type.value}:{user.role_id}'):
        async with services.events_db.acquire() as conn:
            last_report = await db.select_last_actions_report(
                conn=conn,
                role_id=user.role_id,
                report_type=report_type,
            )

        is_last_report_done = (
            # no previous report exists
            not last_report
            # the previous report finished successfully
            or last_report.is_sent
            # the previous report is too old, might have failed or stuck
            or (utc_now() - last_report.date_created) > datetime.timedelta(minutes=10)
        )

        if not is_last_report_done:
            raise TemporaryUnavailableError(
                reason=_('Будь ласка зачекайте, поки сформується попередній звіт'),
            )

    return data
