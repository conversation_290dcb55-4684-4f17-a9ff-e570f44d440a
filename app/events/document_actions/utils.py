import datetime
import json
import logging
from collections.abc import AsyncIterator, Sequence
from dataclasses import dataclass

from app.auth.types import User
from app.events.constants import ACTIONS_REPORT_CHUNK_SIZE, ACTIONS_REPORT_ROWS_PER_FILE
from app.events.db import insert_actions_report, insert_actions_report_file
from app.events.document_actions import db, types
from app.events.document_actions.types import DocumentActionsReportOutput
from app.events.enums import ActionsReportType
from app.events.utils import upload_actions_report_file
from app.i18n import _
from app.lib import xlsx
from app.lib.database import DBConnection
from app.lib.datetime_utils import to_local_datetime
from app.lib.helpers import generate_uuid
from app.services import services
from app.uploads.constants import MB
from worker import topics

logger = logging.getLogger(__name__)


async def add_document_actions(
    document_actions: Sequence[types.DocumentAction],
) -> Sequence[types.DocumentActionDB]:
    """Add document actions to database"""
    if not document_actions:
        return []

    logger.info(
        msg='Add document actions',
        extra={
            'document_actions_total': len(document_actions),
            'document_actions': [
                {
                    'action': document_action.action,
                    'document_id': document_action.document_id,
                    'company_id': document_action.company_id,
                    'role_id': document_action.role_id,
                    'extra': document_action.extra,
                }
                for document_action in document_actions
            ],
        },
    )
    return await db.insert_document_actions(document_actions=document_actions)


async def add_document_action(document_action: types.DocumentAction) -> types.DocumentActionDB:
    """Add document action to database"""

    logger.info(
        msg='Add document action',
        extra={
            'action': document_action.action,
            'document_id': document_action.document_id,
            'document_edrpou_owner': document_action.document_edrpou_owner,
            'document_title': document_action.document_title,
            'company_id': document_action.company_id,
            'company_edrpou': document_action.company_edrpou,
            'email': document_action.email,
            'role_id': document_action.role_id,
            'extra': document_action.extra,
        },
    )
    return await db.insert_document_action(document_action=document_action)


@dataclass(kw_only=True)
class DocumentActionsReportPaginator:
    company_id: str
    date_to: datetime.datetime

    last_date_from: datetime.datetime
    last_record_id: str | None

    rows_count: int = 0
    has_more_rows: bool = False

    max_rows: int = ACTIONS_REPORT_ROWS_PER_FILE
    chunk_size: int = ACTIONS_REPORT_CHUNK_SIZE

    async def stream_actions(self, conn: DBConnection) -> AsyncIterator[types.DocumentActionDB]:
        """
        Get all document actions for the given company and date range using single query that
        is chunked using cursor pagination ("fetchmany").
        """
        actions_stream = db.stream_document_actions_for_report(
            conn=conn,
            company_id=self.company_id,
            last_date_from=self.last_date_from,
            date_to=self.date_to,
            last_record_id=self.last_record_id,
            chunk_size=self.chunk_size,
            limit=self.max_rows + 1,  # one more record for "has_more_rows" check
        )

        iteration: int = 0
        logger.info('Start streaming document actions', extra={'iteration': iteration})
        async for chunk in actions_stream:
            iteration += 1

            # we log only the first 5 iterations to see if there is a difference in performance
            # between first and next iterations
            if iteration < 5:
                logger.info(
                    msg='Streaming document actions',
                    extra={'iteration': iteration, 'chunk_size': len(chunk)},
                )

            for action in chunk:
                # Skip the extra record that we use as marker for "has_more_rows"
                if (self.rows_count + 1) > self.max_rows:
                    self.has_more_rows = True
                    # Do not use "break" here, uvloop in debug mode have bug that causes segfault.
                    # The "continue" statement lets the generator finish gracefully.
                    # https://github.com/MagicStack/uvloop/issues/611
                    continue

                self.rows_count += 1
                self.last_date_from = action.date_created
                self.last_record_id = action.id
                yield action


async def build_report(
    company_id: str,
    company_edrpou: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> DocumentActionsReportOutput:
    """
    Build xlsx file with document actions for company within period
    """

    # Initialize builder and header
    writer = xlsx.XLSXWriter()

    writer.append_row(
        row=[
            _('Дія'),
            _('ID документа'),
            _('Назва документа'),
            _('Email виконавця'),
            _('ID ролі виконавця'),
            _('Дата і час'),
            _('Додаткова інфо про подію'),
        ],
        bold=True,
    )

    paginator = DocumentActionsReportPaginator(
        company_id=company_id,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )
    async with services.events_db.acquire() as conn:
        async for action in paginator.stream_actions(conn=conn):
            writer.append_row(
                row=[
                    action.action.translation,
                    action.document_id,
                    action.document_title or '',
                    action.email or '',
                    action.role_id or '',
                    to_local_datetime(action.date_created).strftime('%Y.%m.%d %H:%M:%S'),
                    json.dumps(action.extra, ensure_ascii=False),
                ],
            )

    content = writer.to_bytes()

    logger.info(
        'Built report for company',
        extra={
            'company_id': company_id,
            'report_size_mb': len(content) / MB,
            'last_date_from': paginator.last_date_from,
            'last_record_id': paginator.last_record_id,
            'has_more_rows': paginator.has_more_rows,
            'rows_count': paginator.rows_count,
        },
    )

    # Example: document_actions_12345678_202310101200000_202310101200000.xlsx
    _date_1: str = last_date_from.strftime('%Y%m%d%H%M%S')
    _date_2: str = paginator.last_date_from.strftime('%Y%m%d%H%M%S')
    filename = f'document_actions_{company_edrpou}_{_date_1}_{_date_2}.xlsx'

    return DocumentActionsReportOutput(
        content=content,
        filename=filename,
        has_more_rows=paginator.has_more_rows,
        last_record_id=paginator.last_record_id,
        last_date_from=paginator.last_date_from,
    )


async def prepare_actions_report_file(
    company_id: str,
    company_edrpou: str,
    report_id: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> DocumentActionsReportOutput:
    output = await build_report(
        company_id=company_id,
        company_edrpou=company_edrpou,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )

    file_id = generate_uuid()

    await upload_actions_report_file(
        company_id=company_id,
        report_id=report_id,
        file_id=file_id,
        file_content=output.content,
    )

    async with services.events_db.acquire() as conn:
        await insert_actions_report_file(
            conn=conn,
            file_id=file_id,
            company_id=company_id,
            report_id=report_id,
            filename=output.filename,
            size=len(output.content),
        )

    return output


async def get_document_upload_action(
    *,
    document_id: str,
) -> types.DocumentActionDB | None:
    """
    Get document upload action for document
    """
    actions = await db.select_document_actions_for(
        document_id=document_id,
        action=types.Action.document_upload,
    )
    if len(actions) > 1:
        logger.warning(
            msg='Multiple document upload actions found',
            extra={'document_id': document_id, 'actions': actions},
        )
    return actions[0] if actions else None


async def start_actions_report(
    *,
    user: User,
    date_from: datetime.datetime,
    date_to: datetime.datetime,
) -> str:
    """
    The main responsibility of this function is to record user request to export actions and
    move to the next step. It is lightweight, so timeouts are not expected.

    This function is the first part of the 3-part process of exporting documents actions.

    return: report_id
    """

    async with services.events_db.acquire() as conn:
        report = await insert_actions_report(
            conn=conn,
            company_id=user.company_id,
            date_from=date_from,
            date_to=date_to,
            type=ActionsReportType.documents,
            created_by=user.role_id,
        )

    await services.kafka.send_record(
        topic=topics.DOCUMENT_ACTIONS_REPORT_PREPARE,
        value={
            'role_id': user.role_id,
            'company_id': user.company_id,
            'company_edrpou': user.company_edrpou,
            'report_id': report.id,
            'date_to': date_to,
            'last_date_from': date_from,
            'last_record_id': None,
        },
    )

    return report.id
