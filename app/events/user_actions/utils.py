import datetime
import json
import logging
from collections.abc import AsyncIterator
from dataclasses import dataclass

from aiohttp import web

from app.auth.types import User
from app.events.constants import ACTIONS_REPORT_CHUNK_SIZE, ACTIONS_REPORT_ROWS_PER_FILE
from app.events.db import (
    insert_actions_report,
    insert_actions_report_file,
)
from app.events.enums import ActionsReportType
from app.events.user_actions import db, types
from app.events.user_actions.types import UserActionsReportOutput
from app.events.utils import upload_actions_report_file
from app.i18n import _
from app.lib import xlsx
from app.lib.database import DBConnection
from app.lib.datetime_utils import to_local_datetime
from app.lib.helpers import generate_uuid
from app.services import services
from app.uploads.constants import MB
from worker import topics

logger = logging.getLogger(__name__)


def get_event_source(request: web.Request) -> types.Source:
    """
    Get user event source from request object.
    """

    if request.rel_url.path[:5] == '/api/':
        return types.Source.public

    if request.rel_url.path[:21] == '/mobile-api/':
        return types.Source.mobile

    return types.Source.internal


@dataclass(kw_only=True)
class UserActionsReportPaginator:
    company_id: str
    date_to: datetime.datetime

    last_date_from: datetime.datetime
    last_record_id: str | None

    rows_count: int = 0
    has_more_rows: bool = False

    max_rows: int = ACTIONS_REPORT_ROWS_PER_FILE
    chunk_size: int = ACTIONS_REPORT_CHUNK_SIZE

    async def get_user_actions(self, conn: DBConnection) -> AsyncIterator[types.UserActionDB]:
        """
        Get user actions for the given company and date range.
        """

        actions_stream = db.stream_user_actions_for_report(
            conn=conn,
            company_id=self.company_id,
            last_date_from=self.last_date_from,
            date_to=self.date_to,
            last_record_id=self.last_record_id,
            chunk_size=self.chunk_size,
            limit=self.max_rows + 1,  # one more record for "has_more_rows" check
        )
        async for action in actions_stream:
            # Skip the extra record that we use as marker for "has_more_rows"
            if (self.rows_count + 1) > self.max_rows:
                self.has_more_rows = True
                # Do not use "break" here, uvloop in debug mode have bug that causes segfault.
                # The "continue" statement lets the generator finish gracefully.
                # https://github.com/MagicStack/uvloop/issues/611
                continue

            self.rows_count += 1
            self.last_date_from = action.date_created
            self.last_record_id = action.id
            yield action


async def build_report(
    company_id: str,
    company_edrpou: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> UserActionsReportOutput:
    """
    Build xlsx file with user actions for company within period
    """

    # Initialize builder and header
    writer = xlsx.XLSXWriter()
    writer.append_row(
        row=[
            _('Дія'),
            _('Джерело'),
            _('Пошта співробітника'),
            _('Дата і час'),
            _('Додаткова інфо про подію'),
        ],
        bold=True,
    )

    paginator = UserActionsReportPaginator(
        company_id=company_id,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )
    async with services.events_db.acquire() as conn:
        async for action in paginator.get_user_actions(conn):
            writer.append_row(
                row=[
                    action.action.translation,
                    action.source.translation,
                    action.email,
                    to_local_datetime(action.date_created).strftime('%d.%m.%Y %H:%M:%S'),
                    json.dumps(action.extra, ensure_ascii=False),
                ],
            )

    content = writer.to_bytes()

    logger.info(
        'Built report for company',
        extra={
            'company_id': company_id,
            'report_size_mb': len(content) / MB,
            'last_date_from': paginator.last_date_from,
            'last_record_id': paginator.last_record_id,
            'has_more_rows': paginator.has_more_rows,
            'rows_count': paginator.rows_count,
        },
    )

    # Example: user_actions_123456789_20231002120000_20231002120000.xlsx
    _date_1 = last_date_from.strftime('%Y%m%d%H%M%S')
    _date_2 = paginator.date_to.strftime('%Y%m%d%H%M%S')
    filename = f'user_actions_{company_edrpou}_{_date_1}_{_date_2}.xlsx'

    return UserActionsReportOutput(
        content=content,
        filename=filename,
        has_more_rows=paginator.has_more_rows,
        last_date_from=paginator.last_date_from,
        last_record_id=paginator.last_record_id,
    )


async def prepare_actions_report_file(
    company_id: str,
    company_edrpou: str,
    report_id: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> UserActionsReportOutput:
    output = await build_report(
        company_id=company_id,
        company_edrpou=company_edrpou,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )

    file_id = generate_uuid()

    await upload_actions_report_file(
        company_id=company_id,
        report_id=report_id,
        file_id=file_id,
        file_content=output.content,
    )

    async with services.events_db.acquire() as conn:
        await insert_actions_report_file(
            conn=conn,
            file_id=file_id,
            company_id=company_id,
            report_id=report_id,
            filename=output.filename,
            size=len(output.content),
        )

    return output


async def start_actions_report(
    *,
    user: User,
    date_from: datetime.datetime,
    date_to: datetime.datetime,
) -> str:
    """
    The main responsibility of this function is to record user request to export actions and
    move to the next step. It is lightweight, so timeouts are not expected.

    This function is the first part of the 3-part process of exporting user actions.

    Returns:
        str: Report ID
    """

    async with services.events_db.acquire() as conn:
        report = await insert_actions_report(
            conn=conn,
            company_id=user.company_id,
            date_from=date_from,
            date_to=date_to,
            type=ActionsReportType.users,
            created_by=user.role_id,
        )

    await services.kafka.send_record(
        topic=topics.USER_ACTIONS_REPORT_PREPARE,
        value={
            'role_id': user.role_id,
            'company_id': user.company_id,
            'company_edrpou': user.company_edrpou,
            'report_id': report.id,
            'date_to': date_to,
            'last_date_from': date_from,
            'last_record_id': None,
        },
    )
    return report.id
