from enum import auto
from typing import Any, Literal, Self, assert_never

import pydantic
from pydantic import BaseModel as _BaseModel
from pydantic import ConfigDict, Field
from yarl import URL

from app.lib.enums import NamedEnum


class ConfigModel(_BaseModel):
    model_config = ConfigDict(extra='ignore', frozen=True)


class FCMConfig(ConfigModel):
    is_enabled: bool
    project_id: str
    config_path: str


class SignTimeoutConfig(ConfigModel):
    default: float
    cesaris: float


class ContentSecurityPolicy(ConfigModel):
    default: list[str]
    script: list[str]
    script_src_elem: list[str]
    style: list[str]
    style_src_elem: list[str]
    img: list[str]
    font: list[str]
    connect: list[str]
    frame: list[str]
    frame_ancestors: list[str]
    object: list[str]
    worker: list[str]


class AppConfig(ConfigModel):
    debug: bool
    demo: bool
    test: bool
    port: int
    # The domain looks like this:
    # - on "local" and "test" environments: "http://localhost:8000"
    # - on other environments "https://{domain}" Ex: "https://edo.vchasno.ua"
    domain: str
    # The static_host looks like this:
    # - http://localhost:8000/static
    # - https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
    static_host: str
    # The static_prefix looks like this:
    #  - dev, test: /static
    #  - prod: https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
    static_prefix: str = Field(..., min_length=0)
    client_max_size: float
    request_timeout: float
    delete_s3_timeout: float
    upload_timeout: float
    upload_s3_timeout: float
    papka24_timeout: float
    sign_timeout: SignTimeoutConfig
    shutdown_timeout: float
    sync_roles_timeout: float
    slow_urls: list[str]
    slow_urls_patterns: list[str]
    trusted_origins: list[str] | None = None
    content_security_policy: ContentSecurityPolicy | None = None
    fernet_key: str
    brand: str
    logo_path: str
    session_max_age: int
    cookie_name: str
    cookie_domain: str
    cookie_secure: bool
    domain_name: str
    info_email: str
    noreply_email: str
    support_email: str
    sales_email: str
    support_phone: str
    support_phone_lifecell: str
    support_phone_vodafone: str
    support_phone_kyivstar: str
    support_viber: str
    support_telegram: str
    support_hours: str
    test_edrpou: str
    landing_url: str | None = None
    sign_widget_iit_url: str
    sign_widget_iit_fozzy_url: str
    default_language: str
    available_languages: list[str]
    proxy_ua: str | None = None
    # The page with rates is built and hosted by WordPress on a separate domain,
    # and it always exists in one instance for all environments.
    rates_url: str = 'https://vchasno.ua/rates/'

    @property
    def is_domain_localhost(self) -> bool:
        return URL(self.domain).host == 'localhost'


class ThemeConfig(ConfigModel):
    primary_color: str


class AnalyticsConfig(ConfigModel):
    ga_tracking_id: str | None = None
    ga_kasa_tracking_id: str | None = None
    gtm_tracking_id: str | None = None
    gtm_common_tracking_id: str | None = None
    gtm_kasa_tracking_id: str | None = None
    fb_domain_content_id: str | None = None
    fb_pixel_id: str | None = None


class AuthConfig(ConfigModel):
    totp_secret_salt: str
    totp_interval: int = Field(..., ge=30, le=600)


class UploadsConfig(ConfigModel):
    allowed_extensions: list[str]
    max_file_size: int


class ContactsConfig(ConfigModel):
    uploads: UploadsConfig


class FirstSignFlowConfig(ConfigModel):
    ignore: list[str]


class ConversionsConfig(ConfigModel):
    first_sign_flow: FirstSignFlowConfig


class EventsDBConfig(ConfigModel):
    url: str
    minsize: int = Field(..., ge=0)
    maxsize: int = Field(..., ge=1)
    timeout: float
    use_aiopg_connection_pool: bool
    ssl_mode: str | None = None
    ssl_root_cert: str | None = None


class DBConfig(ConfigModel):
    url: str
    url_readonly: str
    url_read: str | None = None
    minsize: int = Field(..., ge=0)
    maxsize: int = Field(..., ge=1)
    timeout: float
    use_aiopg_connection_pool: bool
    ssl_mode: str | None = None
    ssl_root_cert: str | None = None


class ESConfig(ConfigModel):
    """
    Config for ES 8 cluster
    """

    hosts: list[str]
    document_index: str = Field(..., min_length=1)
    comment_index: str = Field(..., min_length=1)
    contact_recipient_index: str = Field(..., min_length=1)

    # Should we verify SSL certificates?
    verify_certs: bool = False

    # Basic auth credentials for ES cluster
    username: str | None = None
    password: str | None = None


class RedisHostConfig(ConfigModel):
    host: str
    port: int = Field(..., ge=0)


class RedisSentinelConfig(ConfigModel):
    hosts: list[RedisHostConfig]
    db: int = Field(..., ge=0)
    service_name: str
    sentinel_timeout: float
    username: str | None = None
    password: str | None = None


class RedisConfig(ConfigModel):
    host: str
    port: int = Field(..., ge=0)
    db: int = Field(..., ge=0)
    timeout: float
    use_tls: bool = False
    password: str | None = None


class EsputnikConfig(ConfigModel):
    url: str
    login: str
    password: str
    address_book_id: int
    limit_insert: int


class EvoSenderConfig(ConfigModel):
    project_id: str
    base_url: str


class FeatureFlagsConfig(ConfigModel, frozen=False):
    pass_sign_info_to_backend: bool
    multi_download: bool
    new_worker: bool
    pro_rate_allow_for_free: bool

    def update(self, config: dict[str, Any]) -> None:
        for key, value in config.items():
            setattr(self, key, value)


class GoogleAnalyticsConfig(ConfigModel):
    """
    Config for google analytics measurement protocol.
    """

    token: str


class GoogleAuthConfig(ConfigModel):
    # Web apps.
    # Web projects like kasa, kep, edo, etc. don't have separate pages for login with Google;
    # We redirect users from these projects to "EDO" project for login with Google, so we have
    # only one "web" client ID, only for EDO project.
    client_id_web_edo: str

    # IOS apps.
    # See OAuth 2.0 Client ID with the name "iOS client for ua.vchasno.edo.dev (auto created
    # by Google Service)" in Google Cloud Console
    client_id_ios_edo: str
    client_id_ios_kasa: str
    client_id_ios_kep: str

    # Android apps.
    # OAuth 2.0 Client IDs names in Google Cloud Console:
    # - "Android client for ua.vchasno.edo.dev (auto created by Google Service)"
    # - "Web client for ua.vchasno.edo.dev (auto created by Google Service)"
    #
    # The Android client uses the "web" client ID as the "aud" (audience) claim
    # in JWT tokens, but it also utilizes the "android" client ID for certain internal purposes.
    #
    # Below is a list of "web" client IDs that are auto-generated by Firebase.
    # However, do not confuse these with "client_id_web_edo," which is not associated
    # with Firebase and is manually created in the Google Cloud Console.
    client_id_android_edo: str
    client_id_android_kasa: str
    client_id_android_kep: str

    @property
    def clients_ids(self) -> list[str]:
        """
        Returns list of client ids for all platforms
        """
        return [
            self.client_id_web_edo,
            self.client_id_ios_edo,
            self.client_id_ios_kasa,
            self.client_id_ios_kep,
            self.client_id_android_edo,
            self.client_id_android_kasa,
            self.client_id_android_kep,
        ]


class MicrosoftAuthConfig(ConfigModel):
    client_id: str


class AppleAuthConfig(ConfigModel):
    edo_web_client_id: str
    edo_client_id: str
    kep_client_id: str
    kasa_client_id: str
    team_id: str
    key_id: str
    key: str

    @property
    def client_ids(self) -> list[str]:
        """
        Returns list of client ids for all platforms
        """
        return [
            self.edo_client_id,
            self.edo_web_client_id,
            self.kep_client_id,
            self.kasa_client_id,
        ]


class ProducerConfig(ConfigModel):
    request_timeout_ms: int


class ConsumerConfig(ConfigModel):
    session_timeout_ms: int


class KafkaConfig(ConfigModel):
    bootstrap_servers: list[str] | str
    callback_timeout: int
    client_id: str
    group_id: str
    producer: ProducerConfig
    consumer: ConsumerConfig
    topic_prefix: str
    security_protocol: Literal['PLAINTEXT', 'SSL', 'SASL_PLAINTEXT'] = 'PLAINTEXT'
    sasl_mechanism: str = 'PLAIN'
    sasl_plain_username: str | None = None
    sasl_plain_password: str | None = None


class S3Type(NamedEnum):
    aws = auto()  # dev, prod
    minio = auto()  # local
    test = auto()  # tests


class S3Config(ConfigModel):
    # actually it's scheme + host, e.g., https://minio.localhost
    type: S3Type
    host: str | None = None  # for non-AWS S3
    bucket: str
    region_name: str
    read_timeout: float
    connect_timeout: float
    connections_limit: int
    signature_version: str
    access_key: str
    secret_key: str

    # Currently supports only 1 key in a tuple
    encryption_keys: tuple[str]

    @pydantic.model_validator(mode='after')
    def validate_type(self) -> Self:
        if self.type == S3Type.aws:
            if self.host is not None:
                # For AWS S3, the host must be None, because we expect the botocore package to
                # build the correct host based on the region and bucket name.
                raise ValueError('The host must be None for AWS S3')
        elif self.type == S3Type.minio:
            if self.host is None:
                # For Minio, the host must not be None, because we expect
                # the host to be set to the Minio server address.
                raise ValueError('The host must not be None for Minio')
            if self.host.endswith('amazonaws.com'):
                # Minio does not support AWS S3 virtual-host-style addressing, where the bucket
                # name can be a part of the hostname. So, the host must not end
                # with ".amazonaws.com".
                raise ValueError('The host must not end with ".amazonaws.com" for Minio')
        elif self.type == S3Type.test:
            if self.host and self.host.endswith('amazonaws.com'):
                # For tests, the host must not end with ".amazonaws.com".
                raise ValueError('The host must not end with ".amazonaws.com" for tests')
        else:
            assert_never(self.type)

        return self

    @property
    def addressing_style(self) -> str | None:
        if self.type == S3Type.aws:
            # Bucket name is a part of the domain name:
            #  - https://<bucket>.s3.<region>.amazonaws.com
            # This is recommended for AWS S3
            return 'virtual'
        if self.type == S3Type.minio:
            # Bucket name is part of the path:
            #  - https://other-s3-compatible-service.com/<bucket>
            # This is not recommended to use with AWS S3, but is useful for other
            # S3-compatible services (e.g., MinIO, LeoFS, etc.)
            return 'path'
        if self.type == S3Type.test:
            # for tests, it doesn't matter, because we mock s3 functions
            return None

        assert_never(self.type)

    @property
    def is_aws(self) -> bool:
        return self.type == S3Type.aws

    @property
    def is_minio(self) -> bool:
        return self.type == S3Type.minio

    @property
    def is_test(self) -> bool:
        return self.type == S3Type.test


class BedRockConfig(ConfigModel):
    region_name: str
    access_key: str
    secret_key: str


class SMTPConfig(ConfigModel):
    host: str
    port: int
    timeout: float
    # If True, make the initial connection to the server
    # over plaintext, and then upgrade the connection to TLS/SSL. Not
    # compatible with use_tls.
    start_tls: bool = False
    # If True, make the initial connection to the server
    # over TLS/SSL. Note that if the server supports STARTTLS only, this
    # should be False.
    use_tls: bool = False
    username: str | None = None
    password: str | None = None
    xmailq: str | None = None


class AWSSESConfig(ConfigModel):
    region_name: str
    access_key: str
    secret_key: str


class TelegramConfig(ConfigModel):
    url: str
    token: str


class YouControlConfig(ConfigModel):
    url: str
    login: str
    password: str
    referral_url: str
    youscore_domain: str
    youscore_apikey: str


class SyncContactsEDRPOUConfig(ConfigModel):
    secret_key: str
    url: str
    url_by_invoice: str | None = None
    param: str
    limit: int
    prefix: str = ''


class SyncContactsAPI(ConfigModel):
    e36507036: SyncContactsEDRPOUConfig
    e40283641: SyncContactsEDRPOUConfig

    def get_by_edrpou(self, edrpou: str) -> SyncContactsEDRPOUConfig | None:
        if edrpou == '36507036':
            return self.e36507036

        if edrpou == '40283641':
            return self.e40283641

        return None


class TokensConfig(ConfigModel):
    private_key: str
    public_key: str


class URL2PDFConfig(ConfigModel):
    url: str
    timeout: float
    use_xz: bool


class DOC2PDFConfig(ConfigModel):
    url: str
    timeout: float


class VchasnoSignerConfig(ConfigModel):
    proxy_service_url: str
    use_context: bool


class ZookeeperConfig(ConfigModel):
    host: str
    port: int


class ConstsConfig(ConfigModel):
    gen_archive_ttl: int = 1
    max_files: int = 100
    max_files_size: int = 100  # in MB


class FeatureFlagsV2Config(ConfigModel):
    project: str
    host: str
    port: int
    timeout: int
    is_enabled: bool


class CRMConfig(ConfigModel):
    token: str
    url: str
    creatio_base_url: str | None = None
    landing_id: str | None = None


class ConciergeConfig(ConfigModel):
    service: str
    backend_url: str
    frontend_url: str
    cookie_name: str
    cookie_domain: str
    token: str


class EDIConfig(ConfigModel):
    api_url: str | None = None
    rpc_auth_token: str | None = None
    host: str
    landing_url: str


class TTNConfig(ConfigModel):
    host: str
    landing_url: str

    # Token for sending requests to the private Vchasno TTN API
    auth_token: str


class KasaConfig(ConfigModel):
    host: str
    landing_url: str

    # Token for sending requests to the private Vchasno Kasa API
    auth_token: str


class KEPConfig(ConfigModel):
    host: str
    landing_url: str
    auth_token: str


class ZvitConfig(ConfigModel):
    host: str
    landing_url: str


class PlusConfig(ConfigModel):
    landing_url: str


class CloudSignerConfig(ConfigModel):
    auth_token: str
    host: str


class DiiaConfig(ConfigModel):
    token: str
    offer_id: str
    branch_id: str
    auth_acquirer_token: str
    host: str


IntegrationName = Literal['zakupki', 'kep', 'kasa', 'ttn', 'hrs', 'landing', 'creatio']


class IntegrationConfig(ConfigModel):
    token_hash_b64: str


class IntegrationsConfig(ConfigModel):
    header: str
    zakupki: IntegrationConfig
    kep: IntegrationConfig
    kasa: IntegrationConfig
    ttn: IntegrationConfig
    hrs: IntegrationConfig
    landing: IntegrationConfig | None = None
    creatio: IntegrationConfig | None = None

    @property
    def services_dict(self) -> dict[IntegrationName, IntegrationConfig]:
        config: dict[IntegrationName, IntegrationConfig] = {
            'zakupki': self.zakupki,
            'kep': self.kep,
            'kasa': self.kasa,
            'ttn': self.ttn,
            'hrs': self.hrs,
        }
        if self.landing:
            config['landing'] = self.landing
        if self.creatio:
            config['creatio'] = self.creatio
        return config


class AntivirusConfig(ConfigModel):
    host: str


class SignerWebConfig(ConfigModel):
    host: str
    secret: str
    key_id: str


class EvopayConfig(ConfigModel):
    host: str
    public_key: str
    private_key: str
    evopay_webhook_key: str
    purpose: str
    project_id: str = 'vchasno'

    # This field is useful to set the domain to some tunneling service like ngrok or localtunnel
    # to test webhooks locally.
    # If this field is not set (most of the time), value from "app.domain" will be used.
    evopay_webhook_domain: str | None = None


class SentryConfig(ConfigModel):
    dsn_backend: str
    dsn_frontend: str | None = None
    # By default, send all errors to Sentry (0 = 0%, 1.0 = 100%)
    sample_rate: float = Field(1, ge=0, le=1.0)
    # By default, send no performance traces to Sentry (0 = 0%, 1.0 = 100%)
    traces_sample_rate: float = Field(0, ge=0, le=1.0)


class PrivatbankConfig(ConfigModel):
    host: str
    integration_id: str
    integration_token: str
    account_number: str


class PumbConfig(ConfigModel):
    auth_host: str
    api_host: str
    username: str
    password: str
    client_id: str
    client_secret: str
    # GET it from API {api_host}/account,
    # filter response by IBAN(UA...), get 'accountId' from response
    account_id: int


class CollaboraConfig(ConfigModel):
    url: str


class GotenbergConfig(ConfigModel):
    """
    Service for converting DOCX to PDF
    """

    url: str
    timeout: float = 10  # in seconds


class PosthogConfig(ConfigModel):
    api_key: str
    api_host: str


class HRSConfig(ConfigModel):
    api_url: str | None = None
    token: str | None = None


class Config(ConfigModel):
    analytics: AnalyticsConfig | None = None
    app: AppConfig
    theme: ThemeConfig
    auth: AuthConfig
    # TODO: remove this functionality at all
    colbert: dict[Any, Any] | None = None
    contacts: ContactsConfig
    conversions: ConversionsConfig | None = None
    db: DBConfig
    events_db: EventsDBConfig
    es: ESConfig
    esputnik: EsputnikConfig | None = None
    evo_sender: EvoSenderConfig
    feature_flags: FeatureFlagsConfig
    google_analytics: GoogleAnalyticsConfig | None = None
    google_auth: GoogleAuthConfig | None = None
    microsoft_auth: MicrosoftAuthConfig
    apple_auth: AppleAuthConfig | None = None
    gzip_original: list[str] | None = None
    kafka: KafkaConfig | None = None
    redis_sentinel: RedisSentinelConfig | None = None
    redis: RedisConfig | None = None
    s3: S3Config
    bedrock: BedRockConfig | None = None
    smtp: SMTPConfig
    aws_ses: AWSSESConfig | None = None
    telegram: TelegramConfig | None = None
    youcontrol: YouControlConfig | None = None
    sync_contacts_api: SyncContactsAPI | None = None
    tokens: TokensConfig
    url2pdf: URL2PDFConfig
    doc2pdf: DOC2PDFConfig | None = None
    vchasno_signer: VchasnoSignerConfig
    zookeeper: ZookeeperConfig | None = None
    consts: ConstsConfig
    feature_flags_v2: FeatureFlagsV2Config | None = None
    crm: CRMConfig | None = None
    concierge: ConciergeConfig
    edi: EDIConfig | None = None
    ttn: TTNConfig
    kassa: KasaConfig
    zvit: ZvitConfig
    kep: KEPConfig | None = None
    plus: PlusConfig | None = None
    cloud_signer: CloudSignerConfig | None = None
    diia: DiiaConfig | None = None
    integrations: IntegrationsConfig
    antivirus: AntivirusConfig | None = None
    signer_web: SignerWebConfig | None = None
    evopay: EvopayConfig | None = None
    sentry: SentryConfig | None = None
    privatbank: PrivatbankConfig | None = None
    pumb: PumbConfig | None = None
    collabora: CollaboraConfig | None = None
    fcm: FCMConfig | None = None
    gotenberg: GotenbergConfig | None = None
    posthog: PosthogConfig | None = None
    hrs: HRSConfig | None = None
