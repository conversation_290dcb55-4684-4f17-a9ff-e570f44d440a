from collections import defaultdict
from collections.abc import Iterable

import sqlalchemy as sa
from sqlalchemy import Table
from sqlalchemy.sql import ClauseElement

from app.document_versions.enums import DocumentVersionType
from app.document_versions.tables import document_version_table
from app.document_versions.types import (
    DocumentVersion,
    DocumentVersionIndexatorData,
    InsertDocumentVersionParameters,
)
from app.lib.database import DBConnection
from app.lib.types import (
    DataDict,
)
from app.models import (
    select_all,
    select_one,
)


#########################
# UTILS
#########################
async def _build_select_latest_document_versions_query(
    document_ids: Iterable[str],
    *,
    uploader_edrpou: str | None = None,
    version_type: DocumentVersionType | None = None,
    versions_types: list[DocumentVersionType] | None = None,
    is_sent: bool | None = None,
) -> tuple[Table, ClauseElement]:
    """
    Build query for selecting latest document versions.
    """

    select_from = document_version_table
    filters = [
        document_version_table.c.document_id.in_(document_ids),
    ]

    if version_type:
        filters.append(document_version_table.c.type == version_type)

    if versions_types:
        filters.append(document_version_table.c.type.in_(versions_types))

    if uploader_edrpou:
        filters.append(build_available_versions_filter(uploader_edrpou))

    if is_sent is not None:
        filters.append(document_version_table.c.is_sent.is_(is_sent))

    return select_from, sa.and_(*filters)


def build_available_versions_filter(edrpou: str) -> ClauseElement:
    """
    Generate filter for available versions.
    Get documents that are created by user's company or already sent.
    """

    return sa.and_(
        sa.or_(
            document_version_table.c.is_sent.is_(True),
            document_version_table.c.company_edrpou == edrpou,
        ),
    )


#########################
# SELECT
#########################
async def select_document_version(
    conn: DBConnection,
    *,
    document_id: str | None = None,
    version_id: str | None = None,
    company_edrpou: str | None = None,
) -> DocumentVersion | None:
    """Select optional document version by document ID and version ID"""

    assert document_id or version_id, 'document_id or version_id must be provided'

    filters = []
    if document_id:
        filters.append(document_version_table.c.document_id == document_id)
    if version_id:
        filters.append(document_version_table.c.id == version_id)
    if company_edrpou:
        filters.append(build_available_versions_filter(company_edrpou))

    row = await select_one(
        conn=conn,
        query=(document_version_table.select().where(sa.and_(*filters))),
    )
    return DocumentVersion.from_row(row) if row else None


async def select_document_versions(
    conn: DBConnection,
    *,
    document_ids: Iterable[str],
    uploader_edrpou: str | None = None,
    version_type: DocumentVersionType | None = None,
) -> list[DocumentVersion]:
    select_from, filters = await _build_select_latest_document_versions_query(
        document_ids,
        uploader_edrpou=uploader_edrpou,
        version_type=version_type,
    )

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_version_table])
            .select_from(select_from)
            .where(filters)
            .order_by(document_version_table.c.date_created.desc())
        ),
    )
    return [DocumentVersion.from_row(row) for row in rows]


async def select_document_versions_for_deletion_on_send(
    conn: DBConnection,
    *,
    document_id: str,
    uploader_edrpou: str,
) -> list[DocumentVersion]:
    """
    Get list of versions that should be deleted.
    """

    filters = [
        document_version_table.c.document_id == document_id,
        # do not remove already sent documents (which may be viewed by recipient)
        document_version_table.c.is_sent.is_(False),
        # do not touch recipient version uploading flow.
        # to prevent situation when:
        # document already sent and new versions is uploaded by owner and recipient
        # we shouldn't remove recipient's versions.
        document_version_table.c.company_edrpou == uploader_edrpou,
    ]

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_version_table])
            .select_from(document_version_table)
            .where(sa.and_(*filters))
            .order_by(document_version_table.c.date_created.asc())
        ),
    )
    return [DocumentVersion.from_row(row) for row in rows]


async def count_document_versions(
    conn: DBConnection,
    *,
    document_ids: list[str],
    version_type: DocumentVersionType | None = None,
    is_sent: bool | None = None,
) -> defaultdict[str, int]:
    """
    Check if document has sent versions.
    """
    filters = [
        document_version_table.c.document_id.in_(document_ids),
    ]

    if version_type:
        filters.append(document_version_table.c.type == version_type)
    if is_sent is not None:
        filters.append(document_version_table.c.is_sent.is_(is_sent))

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    sa.func.count(document_version_table.c.document_id).label('count'),
                    document_version_table.c.document_id,
                ]
            )
            .where(sa.and_(*filters))
            .group_by(document_version_table.c.document_id)
        ),
    )
    res = defaultdict(int)

    for row in rows:
        res[row.document_id] = row.count

    return res


async def select_latest_document_version(
    conn: DBConnection,
    document_id: str,
    *,
    uploader_edrpou: str | None = None,
    version_type: DocumentVersionType | None = None,
    versions_types: list[DocumentVersionType] | None = None,
    is_sent: bool | None = None,
) -> DocumentVersion | None:
    """
    Get latest uploaded version.
    """

    select_from, filters = await _build_select_latest_document_versions_query(
        [document_id],
        uploader_edrpou=uploader_edrpou,
        version_type=version_type,
        versions_types=versions_types,
        is_sent=is_sent,
    )

    row = await select_one(
        conn=conn,
        query=(
            sa.select([document_version_table])
            .select_from(select_from)
            .where(filters)
            .order_by(document_version_table.c.date_created.desc())
            .limit(1)
        ),
    )

    return DocumentVersion.from_row(row) if row else None


async def select_first_document_version(
    conn: DBConnection,
    document_id: str,
    *,
    uploader_edrpou: str | None = None,
    version_type: DocumentVersionType | None = None,
    is_sent: bool | None = None,
) -> DocumentVersion | None:
    """
    Get first uploaded version.
    """

    select_from, filters = await _build_select_latest_document_versions_query(
        [document_id],
        uploader_edrpou=uploader_edrpou,
        version_type=version_type,
        is_sent=is_sent,
    )

    row = await select_one(
        conn=conn,
        query=(
            sa.select([document_version_table])
            .select_from(select_from)
            .where(filters)
            .order_by(document_version_table.c.date_created.asc())
            .limit(1)
        ),
    )

    return DocumentVersion.from_row(row) if row else None


async def select_latest_document_versions(
    conn: DBConnection,
    document_ids: list[str],
    *,
    uploader_edrpou: str | None = None,
    version_type: DocumentVersionType | None = None,
    is_sent: bool | None = None,
) -> list[DocumentVersion]:
    """
    Get latest uploaded versions.
    """

    select_from, filters = await _build_select_latest_document_versions_query(
        document_ids,
        uploader_edrpou=uploader_edrpou,
        version_type=version_type,
        is_sent=is_sent,
    )

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_version_table])
            .select_from(select_from)
            .where(filters)
            .order_by(
                document_version_table.c.document_id,
                document_version_table.c.date_created.desc(),
            )
            .distinct(document_version_table.c.document_id)
        ),
    )
    return [DocumentVersion.from_row(row) for row in rows]


async def select_document_versions_for_indexator(
    conn: DBConnection,
    document_ids: list[str],
) -> list[DocumentVersionIndexatorData]:
    """
    Get latest versions for indexator.

    NOTE: versions is sorted in descending order by date_created (latest is first, oldest is last)
    """

    select = [
        document_version_table.c.id,
        document_version_table.c.document_id,
        document_version_table.c.is_sent,
        document_version_table.c.company_edrpou,
    ]
    select_from = document_version_table
    filters = [
        document_version_table.c.document_id.in_(document_ids),
    ]

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(select)
            .select_from(select_from)
            .where(sa.and_(*filters))
            .order_by(
                # WARNING: It is important that the latest version be listed first because
                # further processing relies on this assumption
                document_version_table.c.document_id,
                document_version_table.c.date_created.desc(),
            )
        ),
    )
    return [DocumentVersionIndexatorData.from_row(row) for row in rows]


#########################
# INSERT
#########################
async def insert_document_versions(
    conn: DBConnection,
    params: InsertDocumentVersionParameters,
) -> DocumentVersion:
    """Insert record in database about new document version"""
    row = await select_one(
        conn=conn,
        query=(
            document_version_table.insert()
            .values(params.to_db_dict())
            .returning(document_version_table)
        ),
    )
    return DocumentVersion.from_row(row)


async def insert_document_versions_batch(
    conn: DBConnection,
    params: list[InsertDocumentVersionParameters],
) -> list[DocumentVersion]:
    """Insert record in a database about new document version"""

    rows = await select_all(
        conn=conn,
        query=(
            document_version_table.insert()
            .values([item.to_db_dict() for item in params])
            .returning(document_version_table)
        ),
    )
    return [DocumentVersion.from_row(row) for row in rows]


#########################
# UPDATE
#########################
async def update_document_version(
    conn: DBConnection,
    version_id: str,
    data: DataDict,
) -> None:
    """
    Update document version.
    """

    await conn.execute(
        document_version_table.update()
        .where(document_version_table.c.id == version_id)
        .values(data)
    )


#########################
# DELETE
#########################
async def delete_version(conn: DBConnection, version_id: str) -> None:
    await conn.execute(
        document_version_table.delete().where(document_version_table.c.id == version_id)
    )
