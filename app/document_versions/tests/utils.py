from datetime import datetime

from app.document_versions.enums import DocumentVersionType
from app.document_versions.types import DocumentVersion
from app.document_versions.utils import add_document_content_version
from app.lib.datetime_utils import utc_now
from app.services import services

TEST_DOCUMENT_VERSION_CONTENT = b'test-document-version-content'


async def prepare_document_version(
    *,
    document_id: str,
    role_id: str,
    company_edrpou: str,
    type: DocumentVersionType = DocumentVersionType.new_upload,
    name: str = 'name',
    extension: str = '.docx',
    id: str | None = None,
    is_sent: bool | None = False,
    date_created: datetime | None = None,
    content: bytes = TEST_DOCUMENT_VERSION_CONTENT,
) -> DocumentVersion:
    async with services.db.acquire() as conn:
        return await add_document_content_version(
            conn=conn,
            content=content,
            document_id=document_id,
            company_edrpou=company_edrpou,
            date_created=date_created or utc_now(),
            name=name,
            extension=extension,
            role_id=role_id,
            upload_type=type,
            version_id=id,
            is_sent=is_sent,
        )
