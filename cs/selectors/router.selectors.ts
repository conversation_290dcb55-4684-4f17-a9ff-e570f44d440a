import { getIsArchivePageUtility } from 'lib/ts/helpers';
import { createSelector } from 'reselect';

import { StoreState } from '../types/store';

import { getLocationQuery } from '../lib/url';

export const getLocation = (state: StoreState) => state.router.location;

export const getLocationQueryParams = createSelector(
    getLocation,
    getLocationQuery,
);

export const getDocumentLocationVersionId = (state: StoreState) =>
    getLocation(state).pathname.split('/versions/')[1];

export const getDocumentLocationDraftId = (state: StoreState) =>
    getLocation(state).pathname.split('/drafts/')[1];

export const getIsArchivePage = (state: StoreState) =>
    getIsArchivePageUtility(getLocation(state).pathname);

export const getIsArchivePreviewPage = (state: StoreState) =>
    getLocation(state).pathname.includes('/archive-preview');

export const getDocumentListDirectoryId = (state: StoreState) =>
    getLocation(state).pathname.split('/archive/')[1];
