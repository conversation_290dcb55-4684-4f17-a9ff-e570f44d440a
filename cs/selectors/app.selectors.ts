import { getLinkForPayment } from 'components/ActiveRates/helpers';
import {
    getIsRateDocumentsExpired,
    getIsRateDocumentsTerminate,
} from 'components/RatesPopup/helpers';
import { PermissionCategory } from 'components/proRateInfoPopup/proRateInfoPopupTypes';
import { BillingCompanyConfig, UserOnboarding } from 'gql-types';
import { isFopCompany } from 'lib/company';
import {
    getActiveEmployees,
    getIsMaxActiveEmployees,
    isTovCompany,
    isVchasnoCompany,
} from 'lib/helpers';
import moment from 'moment';
import { hasPermissionFactory } from 'records/user';
import { createSelector } from 'reselect';
import {
    ALL_MAXIMAL_RATES,
    ALL_PRO_RATES,
    ALL_UNLIMITS_RATES,
    DEPRECATED_PRO_RATES,
    getActiveIntegrationRate,
    getActiveRates,
    getFreeRate,
    getIntegrationRates,
    getIsRateExpires,
    getMostCheapRate,
    getMostExpensiveRate,
    getPayedRates,
    getPlannedRates,
    getRatesWithoutSomeRate,
    getTrialIntegrationRate,
    getTrialWebRate,
    getWebRates,
    hasPayedRate,
    hasUnlimitedRate,
} from 'services/billing';
import {
    ALL_FREE_START_PRO_RATES_EXCEPT_BEZLIM,
    ALL_FREE_START_PRO_RATES_EXCEPT_BEZLIM_AND_FREE,
    ALL_PAYED_RATES,
    DEFAULT_BILLING_COMPANY_CONFIG,
    PRO_TRIALS_SET,
    START_RATES_SET,
} from 'services/billing/constants';
import { ApplicationMode, RoleStatuses } from 'services/enums';
import { AccountRate } from 'services/enums';
import { isAvailableFunctionality } from 'services/user';

import { BillingAcount, CompanyRate } from '../types/billing';
import { StoreState } from '../types/store';
import { CompanyConfigSettings, IRole, IUser } from '../types/user';

import {
    getCheckoutRatesUrl,
    getRecommendedWebRate,
} from './checkout.selectors';

import { FREE_RATE_TERMINATE_LIMIT } from './constants';

export const getAppSlice = (state: StoreState) => state.app;

export const getApplicationMode = (state: StoreState): string =>
    getAppSlice(state).applicationMode;

export const getCurrentUser = (state: StoreState): IUser =>
    getAppSlice(state)?.currentUser;

export const getCurrentUserOnboarding = (state: StoreState): UserOnboarding =>
    getCurrentUser(state)?.onboarding;

export const getShowCompanyNamePopup = (state: StoreState): boolean =>
    getAppSlice(state).showCompanyNamePopup;

export const isNeedToVerifyEmailSelector = (state: StoreState): boolean =>
    getAppSlice(state)?.needToVerifyEmail;

export const isNeedToVerifyUserSelector = (state: StoreState): boolean =>
    getAppSlice(state)?.needToVerifyUser;

export const getIsAutogeneratedPassword = (state: StoreState) =>
    getCurrentUser(state).isAutogeneratedPassword;

export const getCurrentUserRole = (state: StoreState): IRole =>
    getCurrentUser(state).currentRole;

export const getCurrentUserSource = (state: StoreState) =>
    getCurrentUser(state).source;

export const getUserTrialAutoEnable = (state: StoreState) =>
    getCurrentUser(state).trialAutoEnable || false;

export const getCurrentCompany = (state: StoreState) =>
    getCurrentUser(state)?.currentCompany;

export const getCurrentCompanyRateExtensions = (state: StoreState) =>
    getCurrentCompany(state)?.rateExtensions;

export const getCurrentCompanyActiveEmployeesTrial = (state: StoreState) =>
    getCurrentCompanyRateExtensions(state)?.findLast(
        (rate) => rate.type === 'employees' && rate.status === 'active_trial',
    );

export const getCurrentCompanyEdrpou = (state: StoreState) =>
    getCurrentCompany(state)?.edrpou;

export const getCurrentCompanyId = (state: StoreState) =>
    getCurrentCompany(state)?.id;

export const getCurrentCompanyName = (state: StoreState) =>
    getCurrentCompany(state)?.name;

export const getCurrentCompanyEmailDomains = (state: StoreState) =>
    getCurrentCompany(state)?.emailDomains;

export const getCurrentCompanyAllowedIps = (state: StoreState) =>
    getCurrentCompany(state)?.allowedIps;

export const getCurrentCompanyInactivityTimeout = (state: StoreState) =>
    getCurrentCompany(state)?.inactivityTimeout;

export const getCurrentCompanyConfig = (
    state: StoreState,
): CompanyConfigSettings =>
    getCurrentCompany(state)?.config || ({} as CompanyConfigSettings);

export const getCurrentCompanyAntivirusConfig = (state: StoreState) =>
    getCurrentCompanyConfig(state).antivirus_settings;

export const getCurrentCompanyArchiveSettings = (state: StoreState) =>
    getCurrentCompanyConfig(state)?.archive_settings || {};

export const getCurrentCompanyBillingCompanyConfig = (
    state: StoreState,
): BillingCompanyConfig =>
    getCurrentCompany(state)?.billingCompanyConfig ??
    DEFAULT_BILLING_COMPANY_CONFIG;

export const getCurrentCompanyUploadConfig = (state: StoreState) =>
    getCurrentCompanyConfig(state)?.uploads || null;

export const getCurrentCompanyRoles = (state: StoreState) =>
    getCurrentCompany(state)?.roles;

export const getIsAnyPopupVisible = (state: StoreState): boolean =>
    getAppSlice(state).anyPopupVisible;

export const getIsSignSessionMode = createSelector(
    getApplicationMode,
    (applicationMode) => applicationMode === ApplicationMode.SIGN_SESSION,
);

export const getCurrentSignSession = (state: StoreState) =>
    getAppSlice(state).currentSignSession;

export const getIsSharedDocumentViewMode = createSelector(
    getApplicationMode,
    (applicationMode) =>
        applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW,
);

export const getActiveEmployeesSelector = createSelector(
    getCurrentCompanyRoles,
    getActiveEmployees,
);

export const getCurrentCompanyMaxEmployees = (state: StoreState) =>
    getCurrentCompanyBillingCompanyConfig(state)?.maxEmployeesCount;

export const getActiveEmployeesCountSelector = (state: StoreState) =>
    getActiveEmployeesSelector(state)?.length;

export const getIsMaxActiveEmployeesSelector = createSelector(
    getActiveEmployeesCountSelector,
    getCurrentCompanyMaxEmployees,
    getIsMaxActiveEmployees,
);

export const getTrialRates = (state: StoreState) =>
    getCurrentCompany(state)?.trialRates;

export const getActiveTrialProRate = (state: StoreState) =>
    getCurrentCompany(state)?.trialRates.findLast(
        (rate) =>
            rate.rate === AccountRate.PRO_PLUS_TRIAL_2022_12 &&
            rate.status === 'active',
    ) || null;

export const getActiveTrialEmployeesProRate = (state: StoreState) =>
    getCurrentCompany(state)?.trialRates.findLast(
        (rate) =>
            rate.rate === AccountRate.PRO_PLUS_TRIAL_2022_12 &&
            rate.status === 'active' &&
            rate.source === 'employees_limit_reach_ab_testing',
    ) || null;

export const getAnyTrialProRateUsedPaidFeature = (state: StoreState) =>
    getCurrentCompany(state)?.trialRates.findLast(
        (rate) =>
            rate.rate === AccountRate.PRO_PLUS_TRIAL_2022_12 &&
            rate.source === 'used_paid_feature',
    ) || null;

export const getActiveAnyTrialProRateUsedPaidFeature = (state: StoreState) =>
    getCurrentCompany(state)?.trialRates.findLast(
        (rate) =>
            rate.rate === AccountRate.PRO_PLUS_TRIAL_2022_12 &&
            rate.source === 'used_paid_feature' &&
            rate.status === 'active',
    ) || null;

export const getActiveRatesNamesSelector = (state: StoreState) =>
    getCurrentCompany(state)?.activeRates;

export const getIsAnyStartRateExists = (state: StoreState) => {
    return getActiveRatesNamesSelector(state).some((rate) =>
        START_RATES_SET.has(rate),
    );
};

export const getIsMaxArchive = (state: StoreState) => {
    return getActiveRatesNamesSelector(state)?.includes(
        AccountRate.ARCHIVE_BIG,
    );
};

export const getIsAnyProRateExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        activeRates.some((item: string) =>
            [...DEPRECATED_PRO_RATES, ...ALL_PRO_RATES].includes(item),
        ),
);

export const getIsUltimateRate = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) => activeRates.includes(AccountRate.PRO),
);

export const getIsAnyUnlimitRateExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        activeRates.some((item: string) => ALL_UNLIMITS_RATES.includes(item)),
);

export const getIsAnyTrialExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) => activeRates.some((item) => PRO_TRIALS_SET.has(item)),
);

export const getAnyActiveMaximalRateName = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        activeRates.find((item) => ALL_MAXIMAL_RATES.includes(item)),
);

export const getIsAnyMaximalRateExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        activeRates.some((item) => ALL_MAXIMAL_RATES.includes(item)),
);

export const getIsActiveProMaximalOrTrialRateExists = createSelector(
    getIsAnyProRateExists,
    getIsAnyMaximalRateExists,
    getIsAnyTrialExists,
    (isAnyProRateExists, isAnyMaximalRateExists, isAnyTrialExists) =>
        isAnyProRateExists || isAnyMaximalRateExists || isAnyTrialExists,
);

export const getIsAnyPayedRateExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        activeRates
            .filter((item: string) => item !== AccountRate.FREE)
            .filter((item: string) => !item.includes('trial')).size > 0,
);

export const getIsCurrentCompanyTov = createSelector(
    getCurrentCompanyEdrpou,
    isTovCompany,
);

export const getBillingAccounts = createSelector(
    getCurrentCompany,
    (currentCompany) => {
        return currentCompany?.billingAccounts.toJS();
    },
);

export const getCurrentCompanyIsFop = createSelector(
    getCurrentCompanyEdrpou,
    (edrpou) => isFopCompany({ edrpou }),
);

export const getIsCurrentCompanyPayedFOP = createSelector(
    [getCurrentCompanyIsFop, getIsAnyPayedRateExists],
    (isFop, isPayed) => isFop && isPayed,
);

export const getIsCurrentCompanyUnPayedFOP = createSelector(
    [getCurrentCompanyIsFop, getIsAnyPayedRateExists],
    (isFop, isPayed) => isFop && !isPayed,
);

// integration billing accounts haven't statuses
export const getIntegrationBillingAccounts = createSelector(
    getBillingAccounts,
    (accounts) =>
        accounts.filter(
            (account: BillingAcount) => account.rate === 'integration',
        ),
);

export const getActiveIntegrationBillingAccounts = createSelector(
    getIntegrationBillingAccounts,
    (accounts) =>
        accounts.filter((account: BillingAcount) => !account.dateDeleted),
);

export const getActiveBillingAccounts = createSelector(
    getBillingAccounts,
    (accounts) =>
        accounts.filter(
            (account: BillingAcount) => account.status === 'active',
        ),
);

export const getPricePerUser = createSelector(
    getActiveBillingAccounts,
    (activeBillingAccounts) => {
        const activeBillingAccount = activeBillingAccounts.findLast(
            (account: BillingAcount) => Boolean(account.pricePerUser),
        );

        return activeBillingAccount ? activeBillingAccount.pricePerUser : null;
    },
);

export const isAdminSelector = (state: StoreState) =>
    getCurrentUserRole(state).isAdmin;

export const isCompanyUsedTrialSelector = createSelector(
    getTrialRates,
    (trialRates) => !!Array.from(trialRates).length,
);

export const isCompanyHasUnlimitedRateSelector = createSelector(
    getCurrentUser,
    (currentUser) => hasUnlimitedRate(currentUser),
);

export const isCompanyHasPayedRateSelector = createSelector(
    getCurrentUser,
    (currentUser) => hasPayedRate(currentUser),
);

export const isCompanyHasPayedWebRateSelector = createSelector(
    getCurrentUser,
    (currentUser) => hasPayedRate(currentUser, true),
);

export const getCompanyRates = createSelector(
    getCurrentCompany,
    (currentCompany) => {
        return currentCompany?.rates.toJS();
    },
);

export const getIsAnyPayRate = createSelector(getCompanyRates, (rates) => {
    return rates.some((rate) => {
        return ALL_PAYED_RATES.includes(rate.rate);
    });
});

export const getCompanyStartProRatesCreatedBefore04092024 = createSelector(
    getCompanyRates,
    (rates: Array<CompanyRate>) => {
        return rates.filter((rate) => {
            return (
                moment(rate.startDate).isBefore('2024-09-04') &&
                ALL_FREE_START_PRO_RATES_EXCEPT_BEZLIM_AND_FREE.includes(
                    rate.rate,
                ) &&
                rate.status === 'active'
            );
        });
    },
);

export const getCompanyWebRates = createSelector(getCompanyRates, getWebRates);

export const getCompanyActiveRates = createSelector(
    getCompanyRates,
    getActiveRates,
);

export const getCompanyActiveWebRates = createSelector(
    getCompanyWebRates,
    getActiveRates,
);

export const getCompanyActiveTrialRates = createSelector(
    getTrialRates,
    getActiveRates,
);

export const getCompanyActiveTrialWebRate = createSelector(
    getCompanyActiveTrialRates,
    getTrialWebRate,
);

export const getCompanyActiveTrialIntegrationRate = createSelector(
    getCompanyActiveTrialRates,
    getTrialIntegrationRate,
);

export const getCompanyActiveFreeRate = createSelector(
    getCompanyActiveWebRates,
    getFreeRate,
);

export const getIsAnyPayRateExpiredAfterCreatedCurrentFreeRate = createSelector(
    getCompanyRates,
    getCompanyActiveFreeRate,
    (rates, activeFreeRate) => {
        if (!activeFreeRate) {
            return false;
        }

        return rates.some((rate) => {
            return (
                ALL_PAYED_RATES.includes(rate.rate) &&
                moment(rate.endDate).isSameOrAfter(activeFreeRate.startDate)
            );
        });
    },
);

export const getCompanyActivePayedWebRates = createSelector(
    getCompanyActiveWebRates,
    getPayedRates,
);

export const getIsCompanyHasActivePayedWebRates = createSelector(
    getCompanyActivePayedWebRates,
    (activePayedWebRates) => !!activePayedWebRates?.length,
);

export const getExpiresActivePayedWebRate = createSelector(
    getCompanyActivePayedWebRates,
    (rates) => getMostExpensiveRate(rates.filter(getIsRateExpires)),
);

export const getCompanyPlannedWebRates = createSelector(
    getCompanyWebRates,
    getPlannedRates,
);

export const getIsCompanyHasPlannedWebRates = (state: StoreState) =>
    getCompanyPlannedWebRates(state).length > 0;

export const getIsCompanyHasOnlyFreeRate = (state: StoreState) =>
    !getIsCompanyHasPlannedWebRates(state) &&
    !getIsCompanyHasActivePayedWebRates(state);

export const getCompanyIntegrationRates = createSelector(
    getCompanyRates,
    getIntegrationRates,
);

export const getCompanyActiveIntegrationRate = createSelector(
    getCompanyIntegrationRates,
    getActiveIntegrationRate,
);

export const getExpiresActiveIntegrationRate = createSelector(
    getCompanyActiveIntegrationRate,
    (rate) => (getIsRateExpires(rate) ? rate : null),
);

export const getCompanyPlannedIntegrationRates = createSelector(
    getCompanyIntegrationRates,
    getPlannedRates,
);

export const getIsCompanyHasPlannedIntegrationRate = (state: StoreState) =>
    getCompanyPlannedIntegrationRates(state).length > 0;

export const getCompanyRatesWithoutIntegration = createSelector(
    getCompanyRates,
    (companyRates) => getRatesWithoutSomeRate(companyRates, 'integration'),
);

export const getCompanyRatesWithoutIntegrations = createSelector(
    getCompanyRates,
    (companyRates) =>
        companyRates.filter(
            (rate: CompanyRate) =>
                rate.rate !== AccountRate.INTEGRATION &&
                rate.rate !== AccountRate.INTEGRATION_TRIAL,
        ),
);

export const getPaidCompanyRatesWithoutIntegration = createSelector(
    getCompanyRatesWithoutIntegrations,
    (companyRatesWithoutIntegration) =>
        getPayedRates(companyRatesWithoutIntegration),
);

export const getMostExpensiveActiveRate = createSelector(
    getCompanyActiveRates,
    getMostExpensiveRate,
);

export const getMostExpensiveWebRate = createSelector(
    getCompanyRatesWithoutIntegration,
    getMostExpensiveRate,
);

export const getMostExpensivePayedWebRate = createSelector(
    getCompanyActivePayedWebRates,
    getMostExpensiveRate,
);

export const getMostCheapPaidWebRate = createSelector(
    getPaidCompanyRatesWithoutIntegration,
    getMostCheapRate,
);

export const getIsFreeRateDocumentsTerminate = createSelector(
    getCompanyActiveFreeRate,
    getActiveBillingAccounts,
    (companyActiveFreeRate, activeBillingAccounts) =>
        getIsRateDocumentsTerminate(
            companyActiveFreeRate,
            activeBillingAccounts,
            FREE_RATE_TERMINATE_LIMIT,
        ),
);

export const getIsFreeRateDocumentsTerminateHasOnlyFreeRateIsAdmin = createSelector(
    getIsFreeRateDocumentsTerminate,
    getIsCompanyHasOnlyFreeRate,
    isAdminSelector,
    (isFreeRateDocumentsTerminate, isCompanyHasOnlyFreeRate, isAdmin) =>
        isFreeRateDocumentsTerminate && isCompanyHasOnlyFreeRate && isAdmin,
);

export const getDocumentsTerminateFreeRate = createSelector(
    getCompanyActiveFreeRate,
    getIsFreeRateDocumentsTerminate,
    (companyActiveFreeRate, isFreeRateTerminate) =>
        isFreeRateTerminate ? companyActiveFreeRate : null,
);

export const getDocumentsExpiredFreeRate = createSelector(
    getCompanyActiveFreeRate,
    getActiveBillingAccounts,
    (companyActiveFreeRate, activeBillingAccounts) =>
        getIsRateDocumentsExpired(companyActiveFreeRate, activeBillingAccounts)
            ? companyActiveFreeRate
            : null,
);

export const getDocumentsTerminatePayedWebRate = createSelector(
    getCompanyActivePayedWebRates,
    getActiveBillingAccounts,
    (companyActiveWebRates, activeBillingAccounts) =>
        companyActiveWebRates?.find((rate: CompanyRate) =>
            getIsRateDocumentsTerminate(rate, activeBillingAccounts),
        ),
);

export const getDocumentsExpiredPayedRate = createSelector(
    getCompanyActivePayedWebRates,
    getActiveBillingAccounts,
    (companyActiveWebRates, activeBillingAccounts) =>
        companyActiveWebRates?.find((rate: CompanyRate) =>
            getIsRateDocumentsExpired(rate, activeBillingAccounts),
        ),
);

export const getDocumentsTerminateIntegrationRate = createSelector(
    getCompanyActiveIntegrationRate,
    getActiveIntegrationBillingAccounts,
    (companyActiveIntegrationRate, integrationBillingAccounts) =>
        getIsRateDocumentsTerminate(
            companyActiveIntegrationRate,
            integrationBillingAccounts,
        )
            ? companyActiveIntegrationRate
            : null,
);

export const getAnotherActivePayedWebRate = createSelector(
    getCompanyActivePayedWebRates,
    getDocumentsTerminatePayedWebRate,
    getDocumentsExpiredPayedRate,
    (companyActiveWebRates, documentsTerminateRate, documentsExpiredRate) =>
        companyActiveWebRates.find(
            (rate: CompanyRate) =>
                (documentsTerminateRate &&
                    rate.id !== documentsTerminateRate.id) ||
                (documentsExpiredRate && rate.id !== documentsExpiredRate.id),
        ),
);

export const getCurrentUserId = (state: StoreState) => getCurrentUser(state).id;

export const getCurrentUserEmail = (state: StoreState) =>
    getCurrentUser(state).email;

export const getCurrentUserRoleId = (state: StoreState) =>
    getCurrentUser(state).roleId;

export const getAppFlags = (state: StoreState) => getAppSlice(state).flags;

type CompanyPermissionMap = Record<PermissionCategory, boolean>;
export const getCurrentCompanyPermissionMap = createSelector(
    getCurrentCompanyBillingCompanyConfig,
    (config): CompanyPermissionMap => {
        return Object.values(PermissionCategory).reduce(
            (acc, permission) => ({
                ...acc,
                [permission]: isAvailableFunctionality(config, permission),
            }),
            {} as CompanyPermissionMap,
        );
    },
);

export const getCompanyConfigSettingsMaxDocumentsForView = createSelector(
    getCurrentCompanyBillingCompanyConfig,
    (config) => {
        return config?.maxDocumentsCount
            ? config?.maxDocumentsCount + (config.maxArchiveDocumentsCount || 0)
            : null;
    },
);

export const getCompanyConfigSettingsMaxArchiveDocumentsCount = createSelector(
    getCurrentCompanyBillingCompanyConfig,
    (config) => {
        return config?.maxArchiveDocumentsCount ?? null;
    },
);

export const getHasPermissionCanDownloadDocument = createSelector(
    getCurrentUser,
    (currentUser) => hasPermissionFactory(currentUser)('canDownloadDocument'),
);

export const getUserRegistrationMethod = createSelector(
    getCurrentUser,
    (currentUser) => currentUser.registrationMethod,
);

export const getIsGoogleAuthMethod = createSelector(
    getUserRegistrationMethod,
    (registrationMethod) => registrationMethod === 'google',
);

export const getCurrentCompanyDefaultRolePermissionKey = createSelector(
    getCurrentCompanyConfig,
    (config) => config.default_role_permissions_key,
);

export const getAllActiveCompanyRoles = createSelector(
    getCurrentUser,
    ({ roles }: IUser) =>
        roles.filter((role) => role.status === RoleStatuses.ACTIVE),
);

export const getCompanyAdminRoles = createSelector(
    getCurrentUser,
    ({ roles }: IUser) => roles.filter((role) => role.isAdmin),
);

export const getActiveCompanyRolesWithoutCurrentRole = createSelector(
    getCurrentUserRoleId,
    getAllActiveCompanyRoles,
    (currentRoleId: string, allActiveRoles: IRole[]) =>
        allActiveRoles.filter((role) => role.id !== currentRoleId),
);

export const getSidebarMenuRatesUrl = createSelector(
    getCurrentUser,
    getCheckoutRatesUrl,
    getIsCompanyHasOnlyFreeRate,
    (currentUser, checkoutRatesUrl, isCompanyHasOnlyFreeRate) => {
        if (isCompanyHasOnlyFreeRate && !checkoutRatesUrl) {
            return '/app/checkout-rates';
        }

        return (
            checkoutRatesUrl ||
            `/app/settings/companies/${currentUser.roleId}/rates`
        );
    },
);

export const getIsMobileDeviceUsage = createSelector(
    getCurrentUser,
    (currentUser) => currentUser.userMeta.mobileUsage,
);

export const getCheckoutLinkForPaymentActiveFreeRate = createSelector(
    getCompanyActiveFreeRate,
    getRecommendedWebRate,
    (activeFreeRate, recommendedWebRateForPayment) =>
        getLinkForPayment(activeFreeRate, recommendedWebRateForPayment, true),
);

export const getIsCurrentRoleFromVchasnoCompany = createSelector(
    getCurrentCompany,
    (currentCompany) => isVchasnoCompany(currentCompany.edrpou),
);

export const getIsCurrentUserHasPassword = createSelector(
    getCurrentUser,
    (currentUser) => currentUser.hasPassword,
);

export const getCurrentUserSurveys = createSelector(
    getCurrentUser,
    (currentUser) => currentUser.activeSurveys,
);

export const getIsShowInviteUserPopup = (state: StoreState) =>
    getAppSlice(state).isShowInviteUserPopup;
export const getAfterBillAddEmployees = (state: StoreState) =>
    getAppSlice(state).afterBillAddEmployees;
export const getIsShowInviteRecipientPopup = (state: StoreState) =>
    getAppSlice(state).isShowInviteRecipientPopup;
export const getIsShowInviteRecipientsPopup = (state: StoreState) =>
    getAppSlice(state).isShowInviteRecipientsPopup;
export const getIsShowInvitationSentPopup = (state: StoreState) =>
    getAppSlice(state).isShowInvitationSentPopup;
export const getIsEmployeeLimitReachedAndTrialAlreadyActivated = (
    state: StoreState,
) => getAppSlice(state).isShowEmployeeLimitReachedTrialAlreadyActivatedPopup;

export const getIsNeedConfirmClosed = (state: StoreState) =>
    state.signPopup.isNeedConfirmClosed;

export const getIsShowConfirmClose = (state: StoreState) =>
    state.signPopup.isShowConfirmClose;

export const isAnyActiveRatesFreeStartProExists = createSelector(
    getActiveRatesNamesSelector,
    (activeRates) =>
        ALL_FREE_START_PRO_RATES_EXCEPT_BEZLIM.filter((rate: AccountRate) =>
            activeRates?.includes(rate),
        ),
);

export const getIsEmployeesLimitTOVReached = createSelector(
    getIsCurrentCompanyTov,
    getIsMaxActiveEmployeesSelector,
    isAnyActiveRatesFreeStartProExists,
    (
        isCurrentCompanyTOV,
        isMaxActiveEmployees,
        isActiveRatesFreeStartProExists,
    ) => {
        return (
            isCurrentCompanyTOV &&
            isMaxActiveEmployees &&
            isActiveRatesFreeStartProExists
        );
    },
);

export const getIsCanEditRoles = createSelector(
    isAdminSelector,
    getCurrentUserRole,
    (isAdmin, currentUserRole) => isAdmin || currentUserRole?.canEditRoles,
);

export const getCompanyConfigSettingsMaxVisibleDocumentsCount = createSelector(
    getCurrentCompanyBillingCompanyConfig,
    (config) => {
        return config?.maxVisibleDocumentsCount ?? null;
    },
);

export const getUsedDocumentCount = (state: StoreState) =>
    getCurrentCompany(state)?.usedDocumentCount;
