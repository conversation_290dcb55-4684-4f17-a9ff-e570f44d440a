import { RefObject, useEffect, useRef, useState } from 'react';

import { Nullable } from '../../types/general';

type UseInternalCallback = () => void;

// https://usehooks.com/useDebounce
export const useDebounce = (value: any, delay: number): any => {
    // State and setters for debounced value
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(
        () => {
            // Update debounced value after delay
            const handler = setTimeout(() => {
                setDebouncedValue(value);
            }, delay);

            // Cancel the timeout if value changes (also on delay change or unmount)
            // This is how we prevent debounced value from updating if value is changed ...
            // .. within the delay period. Timeout gets cleared and restarted.
            return () => {
                clearTimeout(handler);
            };
        },
        [value, delay], // Only re-call effect if value or delay changes
    );

    return debouncedValue;
};

// https://overreacted.io/making-setinterval-declarative-with-react-hooks/
export const useInterval = (
    callback: UseInternalCallback,
    delay: Nullable<number>,
): void => {
    const savedCallback = useRef<UseInternalCallback>();

    // Remember the latest callback.
    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);

    // Set up the interval.
    useEffect(() => {
        const tick = () => savedCallback.current && savedCallback.current();
        if (delay !== null) {
            const id = setInterval(tick, delay);
            return () => clearInterval(id);
        }
        return () => {
            // do nothing
        };
    }, [delay]);
};

type Event = MouseEvent | TouchEvent;

export const useOnClickOutside = <T extends HTMLElement = HTMLElement>(
    ref: RefObject<T>,
    handler: (event: Event) => void,
) => {
    const handlerRef = useRef(handler);
    handlerRef.current = handler;

    useEffect(() => {
        const listener = (event: Event) => {
            const el = ref && ref.current;
            // Do nothing if clicking ref's element or descendent elements
            if (!el || el.contains((event.target as Node) || null)) {
                return;
            }
            handlerRef.current(event);
        };
        document.addEventListener(`mousedown`, listener);
        document.addEventListener(`touchstart`, listener);
        return () => {
            document.removeEventListener(`mousedown`, listener);
            document.removeEventListener(`touchstart`, listener);
        };
    }, [ref]);
};

export const useIsMounted = () => {
    const isMounted = useRef(false);

    useEffect(() => {
        isMounted.current = true;
        return () => {
            isMounted.current = false;
        };
    }, []);

    return () => isMounted.current;
};

// This is workaround for static-site-generator-webpack-plugin (apparently it does not kick off the React lifecycle correctly)
// TODO: remove usage of this when migrated from StaticSiteGeneratorPlugin
export const useDidMount = () => {
    const [mounted, setMount] = useState(false);

    useEffect(() => {
        setMount(true);
    }, []);

    return mounted;
};
