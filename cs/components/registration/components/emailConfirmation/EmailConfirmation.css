.container {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px;
}

.content {
    width: 100%;
    max-width: 400px;
    margin-top: 36%;
}

.partnersLogos {
    display: none;
}

.iconContainer {
    background-color: var(--grey-bg);
}

.iconContainer svg {
    color: var(--slate-grey-color);
}

.title {
    color: var(--content-color);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.description {
    color: var(--content-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.email {
    color: var(--content-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.setNewEmailLink {
    color: var(--content-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

@media all and (max-width: 768px) {
    .container {
        padding: 0;
    }

    .content {
        margin-top: 0;
    }

    .partnersLogos {
        display: flex;
        height: 36vh;
        align-self: center;
        margin-top: 22px;
        object-fit: contain;
    }
}
