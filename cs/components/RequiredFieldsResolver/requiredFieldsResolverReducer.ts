import { AnyAction } from 'redux';

import { Nullable } from '../../types/general';
import { WithoutRequiredFieldsDoc } from '../DocumentsRequiredFieldsError/types';
import { RequiredFieldsCallbacks } from './types';

import actions from './requiredFieldsResolverActions';

export interface RequiredFieldsResolverState {
    withoutRequiredFieldsDocs: WithoutRequiredFieldsDoc[];
    requiredFieldsCallbackName?: RequiredFieldsCallbacks | '';
    requiredFieldsCallbackData?: Nullable<any>;
    docNumber: Nullable<string>;
    docCategory: Nullable<string>;
    docDate?: Nullable<Date>;
    docAmount?: Nullable<string>;
    isGroupRequiredFieldsByCompany?: boolean;
}

const initState: RequiredFieldsResolverState = {
    withoutRequiredFieldsDocs: [],
    requiredFieldsCallbackName: '',
    requiredFieldsCallbackData: null,
    docNumber: '',
    docDate: undefined,
    docCategory: '',
    docAmount: null,
    isGroupRequiredFieldsByCompany: true,
};

const recipientFormReducer = (state = initState, action: AnyAction) => {
    switch (action.type) {
        case actions.REQUIRED_FIELDS_RESOLVER__SET_DATA:
            return {
                ...state,
                withoutRequiredFieldsDocs: action.withoutRequiredFieldsDocs,
                requiredFieldsCallbackName: action.requiredFieldsCallbackName,
                requiredFieldsCallbackData:
                    action.requiredFieldsCallbackData ||
                    initState.requiredFieldsCallbackData,
                isGroupRequiredFieldsByCompany:
                    typeof action.isGroupRequiredFieldsByCompany === 'boolean'
                        ? action.isGroupRequiredFieldsByCompany
                        : initState.isGroupRequiredFieldsByCompany,
            };
        case actions.REQUIRED_FIELDS_RESOLVER__CHANGE_CATEGORY:
            return {
                ...state,
                docCategory: action.docCategory,
                withoutRequiredFieldsDocs: action.withoutRequiredFieldsDocs,
            };
        case actions.REQUIRED_FIELDS_RESOLVER__CHANGE_NUMBER:
            return {
                ...state,
                docNumber: action.docNumber,
            };
        case actions.REQUIRED_FIELDS_RESOLVER__CHANGE_DATE:
            return {
                ...state,
                docDate: action.docDate,
            };
        case actions.REQUIRED_FIELDS_RESOLVER__CHANGE_AMOUNT:
            return {
                ...state,
                docAmount: action.docAmount,
            };
        case actions.REQUIRED_FIELDS_RESOLVER__CLEAR_DATA:
            return {
                ...initState,
            };
        default:
            return state;
    }
};

export default recipientFormReducer;
