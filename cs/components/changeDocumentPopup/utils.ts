import { Document } from '../../services/documents/ts/types';
import { IRole, IUser } from '../../types/user';
import { DocumentListDocumentItem } from '../documentList/types';

import { canAddSigners } from '../../services/documents/utils';
import { getIsCurrentRoleHasPermissionToEditReview } from '../reviews/helpers';

import { getCurrentCompanyRoles } from '../../services/user';

export const canEditSignProcess = (currentUser: IUser, documents: Document[]) =>
    documents.every((doc) => canAddSigners(doc, currentUser));

export const canEditReviewProcessType = (
    documents: Document[],
    currentRole: IRole,
) => {
    // admin can change review proccess type anytime
    if (currentRole.isAdmin) {
        return true;
    }
    // can edit review process type if document doesn't have any review or review requests
    return documents.every((doc) => {
        return getIsCurrentRoleHasPermissionToEditReview(
            doc.reviewRequests,
            doc.reviews,
            currentRole,
        );
    });
};

export const getSuggestion = async (search: string, currentList: IRole[]) => {
    const { currentCompanyRoles } = await getCurrentCompanyRoles({ search });
    const currentIDs = currentList.map((role) => role.id) || [];
    return currentCompanyRoles.filter(
        (role: IRole) => !currentIDs.includes(role.id),
    );
};

export const getIsRoleSignedDocument = (
    role: IRole,
    document: DocumentListDocumentItem,
) => document.signatures.some((signature) => signature.roleId === role.id);
