import React, { Fragment } from 'react';
import { connect, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';
import { Link } from 'react-router-dom';

import { FlexBox } from '@vchasno/ui-kit';

import AppModeDisplay from 'components/AppModeDisplay';
import { FeatureShow } from 'components/FeatureDisplay';
import AccessLevelBlock from 'components/document/toolbar/AccessLevelBlock';
import { useShowProRatePopupHandlerWrapper } from 'hooks/useShowProRatePopupHandlerWrapper';
import { MEDIA_WIDTH } from 'lib/constants';
import { formatDate } from 'lib/date';
import { formatFullName } from 'lib/helpers';
import PropTypes from 'prop-types';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import {
    getDocumentActualDraft,
    getIsDocumentActualDraftPageSelector,
    getIsDocumentActualVersionPageSelector,
    getIsVersionedDocumentSelector,
} from 'selectors/document.selectors';
import { DOCUMENT_FIELDS_HINT } from 'services/documentFields/constants';
import {
    getDateSentToRecipient,
    getIsReviewParallel,
} from 'services/documents/ts/utils';
import {
    canChangeRecipient,
    getIsDocumentHasRejectedDeleteRequest,
    getRemainSideSignaturesCount,
    isAlredySigned3pDocument,
} from 'services/documents/utils';
import { ApplicationMode, DocumentReviewType } from 'services/enums';
import {
    mapStateToApplicationMode,
    mapStatetoHasPermission,
} from 'store/utils';
import { jt, t } from 'ttag';

import { DocumentSidebarGAEvent } from './enums';

import { formatDateTime } from '../utils';

import Alert from '../../ui/Alert/Alert';
import Button from '../../ui/button/button';
import CollapsibleGroup from '../../ui/collapsibleGroup/collapsibleGroup';
import Icon from '../../ui/icon/icon';
import PseudoLink from '../../ui/pseudolink/pseudolink';
import Tags from '../../ui/tags/tags';
import TextShorten from '../../ui/textShorten/textShorten';

import eventTracking from '../../../services/analytics/eventTracking';
import CreateDeleteRequest from '../../createDeleteRequestPopup/createDeleteRequest';
import ProLabel from '../../proLabel/proLabel';
import { PermissionCategory } from '../../proRateInfoPopup/proRateInfoPopupTypes';
import Reviews from '../../reviews/reviews';
import ReviewsCounter from '../../reviewsCounter/reviewsCounter';
import StatusText from '../../statusText/statusText';
import DocumentComments from '../comments/documentComments';
import DocumentReceiver from '../receiver/documentReceiver';
import DocumentRequisitesList from '../requisitesList/documentRequisitesList';
import AntivirusStatus from './AntivirusStatus/AntivirusStatus';
import ReviewPreviewBar from './ReviewPreviewBar';
import Version from './Version/Version';
import LinkedDocumentsSection from './linkedDocumentsSection/linkedDocumentsSection';

import SvgIn from './images/in.svg';
import SvgInternal from './images/internal.svg';
import SvgMultilateral from './images/multi.svg';
// icons
import SvgOut from './images/out.svg';

// styles
import css from './documentToolbar.css';

const inputInfo = { text: t`Вхідний документ`, icon: SvgIn };
const outputInfo = { text: t`Вихідний документ`, icon: SvgOut };
const internalInfo = { text: t`Внутрішній документ`, icon: SvgInternal };

const DocumentToolbar = (props) => {
    const isVersionedDocument = useSelector(getIsVersionedDocumentSelector);
    const documentDraft = useSelector(getDocumentActualDraft);
    const isActualVersionPage = useSelector(
        getIsDocumentActualVersionPageSelector,
    );

    const isActualDraftPage = useSelector(getIsDocumentActualDraftPageSelector);

    const companyPermissionMap = useSelector(getCurrentCompanyPermissionMap);
    const showProRatePopupHandlerWrapper = useShowProRatePopupHandlerWrapper();
    let owner = internalInfo;
    const isSignSessionMode =
        props.applicationMode === ApplicationMode.SIGN_SESSION;
    const isSharedDocumentViewMode =
        props.applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW;
    if (!props.isInternal) {
        owner = props.isInput ? inputInfo : outputInfo;
        if (isAlredySigned3pDocument(props.doc)) {
            owner = props.isInput ? outputInfo : inputInfo;
        }
    }

    const getDateSentToCurrentCompany = () => {
        if (!props.isInput) {
            return null;
        }

        const recipients = props.doc.recipients;
        const currentCompanyRecipient =
            recipients &&
            recipients.find(
                (recipient) => recipient.edrpou === props.companyEdrpou,
            );
        if (currentCompanyRecipient && currentCompanyRecipient.dateSent) {
            return formatDateTime(currentCompanyRecipient.dateSent);
        }
        return formatDateTime(props.dateListing);
    };

    const dateSentToCurrentCompany = getDateSentToCurrentCompany();
    const dateSentToRecipient = getDateSentToRecipient(
        props.doc,
        props.companyEdrpou,
    );
    const dateCreated = formatDateTime(props.dateCreated);
    const dateDocument = formatDateTime(props.dateDocument);
    const dateDelivered = formatDateTime(props.dateDelivered);
    const documentDatesBlock = (
        <div className={css.info}>
            {dateDocument && (
                <div className={css.date} data-qa="qa_doc_created">
                    {t`Сформований`} {dateDocument}
                </div>
            )}
            {dateCreated && (
                <div className={css.date} data-qa="qa_doc_uploaded">
                    {t`Завантажений`} {dateCreated}
                </div>
            )}
            {dateSentToCurrentCompany && (
                <div className={css.date}>
                    {t`Відправлений вам`} {dateSentToCurrentCompany}
                </div>
            )}
            {dateSentToRecipient && (
                <div className={css.date}>
                    {t`Відправлений контрагенту`} {dateSentToRecipient}
                </div>
            )}
            {dateDelivered && (
                <div className={css.date}>
                    {t`Отриманий контрагентом`} {dateDelivered}
                </div>
            )}
        </div>
    );

    const reviews = props.doc.reviews.filter((review) => review.type);
    const reviewsEmails = reviews.length
        ? reviews.map((review) => review.role.user.email)
        : [];

    const reviewsGroupsId = reviews.map((review) => review.groupId);

    const activeReviews = (props.doc?.reviewRequests || []).filter(
        (request) => {
            if (request.toRoleId) {
                return reviewsEmails.indexOf(request.toRole.user.email) === -1;
            }
            return !reviewsGroupsId.includes(request.toGroupId);
        },
    );

    const pendedCount = activeReviews.length || 0;
    const approvedCount = reviews.length
        ? reviews.filter((review) => {
              return review.type === DocumentReviewType.APPROVE;
          }).length
        : 0;
    const rejectedCount = reviews.length
        ? reviews.filter((review) => {
              return review.type === DocumentReviewType.REJECT;
          }).length
        : 0;

    const receiverData = props.doc.isInput
        ? {
              edrpou: props.doc.edrpouOwner,
              email: props.doc.emailOwner,
              name: props.doc.companyNameOwner,
          }
        : {
              edrpou: props.doc.edrpouRecipient,
              email: props.doc.emailRecipient,
              name: props.doc.companyNameRecipient,
          };
    const inboxReceiverData = {
        edrpou: props.doc.edrpouRecipient,
        email: props.doc.emailRecipient,
        name: props.doc.companyNameRecipient,
    };

    const flexibleSignaturesExpectedCount = !props.doc.isInput
        ? props.doc.expectedOwnerSignatures
        : props.doc.expectedRecipientSignatures;
    const remainSignatures = getRemainSideSignaturesCount(props.doc);

    const headSignBlock = (
        <div className={css.status} data-qa="qa_status_text">
            <StatusText isWrapping statusColor={props.statusColor}>
                <b>{props.statusTitle}</b>
            </StatusText>
        </div>
    );

    const isParallel = getIsReviewParallel(props.doc);

    const trackTogglingCollapsibleGroup = (isCollapsed, eventName) => {
        !isCollapsed ? eventTracking.sendToGTMV4({ event: eventName }) : null;
    };

    const handleChangeRecipient = () => {
        props.onOpenChangeRecipientPopup();
        trackTogglingCollapsibleGroup(
            false,
            DocumentSidebarGAEvent.SIDEBAR_RECIPIENT_CHANGE_PSEUDO_LINK_CLICK,
        );
    };

    const handleEditTagsPseudoLinkClick = () => {
        props.onOpenTagsEditPopup();
        eventTracking.sendToGTMV4({
            event: DocumentSidebarGAEvent.SIDEBAR_TAGS_SECTION_EDIT_TAGS_CLICK,
        });
    };

    const tagsHint = t`Позначки для документа, за допомогою яких ви
        можете групувати документи за будь-якою ознакою (напр. назва відділу, тип документу і т.і.).
        Щоб додати ярлик, натисніть
        лінк “Редагувати ярлики”`;

    // Extra fields
    const parameters = props.doc.parameters;
    const renderExtraFields = () => (
        <Fragment>
            <ul>
                {props.documentFields.map((field) => {
                    const data = parameters.find(
                        ({ fieldId }) => fieldId === field.id,
                    );

                    if (!data) return null;

                    const value =
                        field.type === 'date'
                            ? formatDate(data.value)
                            : data.value;

                    return (
                        <li key={field.id} className={css.extraDataBlock}>
                            <div className={css.extraDataTitle}>
                                {field.name}
                            </div>
                            <TextShorten title={value}>{value}</TextShorten>
                        </li>
                    );
                })}
            </ul>
            <div className={css.extraFieldsSettingsLink}>
                <PseudoLink
                    onClick={() => {
                        eventTracking.sendToGTMV4({
                            event:
                                DocumentSidebarGAEvent.SIDEBAR_ADDITIONAL_FIELDS_SECTION_EDIT_FIELD_CLICK,
                        });
                        props.onOpenChangeDocumentPopup({
                            selectedItemIndex: 'extraFields',
                        });
                    }}
                >
                    {t`Редагувати параметри`}
                </PseudoLink>
            </div>
        </Fragment>
    );

    const documentLastVersionLink = (
        <Link to={`/app/documents/${props.doc.id}`}>{t`останню версію`}</Link>
    );

    const documentDraftLink = (
        <Link
            to={{
                pathname: `/app/documents/${props.doc.id}/drafts/${documentDraft?.id}`,
            }}
        >{t`переглянути`}</Link>
    );

    const isShowLinkedDocumentsSection =
        !isSharedDocumentViewMode && !isSignSessionMode;
    const isShowReviewsSection =
        !isSharedDocumentViewMode && !isSignSessionMode;
    const isShowCommentsSection = !isSharedDocumentViewMode;
    const isShowTagsSection = !isSharedDocumentViewMode && !isSignSessionMode;
    const isShowExtraFieldsSection =
        !isSharedDocumentViewMode && !isSignSessionMode;

    return (
        <div className={css.list} data-qa="qa_left_toolbar">
            {!props.doc.isInternal &&
                props.doc.deleteRequest &&
                !getIsDocumentHasRejectedDeleteRequest(props.doc) && (
                    <div className={css.block}>
                        <CollapsibleGroup
                            isOpened
                            isTypeHeaderBig
                            title={t`Запит на видалення`}
                            onHeaderClick={(isCollapsed) => {
                                trackTogglingCollapsibleGroup(
                                    isCollapsed,
                                    t`Запит на видалення`,
                                );
                            }}
                        >
                            <CreateDeleteRequest
                                docPage
                                docs={[props.doc]}
                                onCreateClick={() =>
                                    props.onOpenCreateDeleteRequestPopup([
                                        props.doc,
                                    ])
                                }
                                onResolveRequestClick={() =>
                                    props.onOpenResolveDeleteRequestPopup([
                                        props.doc,
                                    ])
                                }
                                onRejectRequestClick={() =>
                                    props.onRejectDeleteRequest([props.doc])
                                }
                                onCancelDeleteRequestVote={() =>
                                    props.onCancelDeleteRequestVote([props.doc])
                                }
                                onAcceptRequestClick={() =>
                                    props.onAcceptDeleteRequest([props.doc])
                                }
                                onCancelDeleteRequestClick={
                                    props.onCancelDeleteRequestClick
                                }
                            />
                        </CollapsibleGroup>
                    </div>
                )}
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <div className={css.block}>
                    <CollapsibleGroup
                        isOpened
                        isTypeHeaderBig
                        title={t`Підписання`}
                        headBlock={headSignBlock}
                        onHeaderClick={(isCollapsed) => {
                            trackTogglingCollapsibleGroup(
                                isCollapsed,
                                DocumentSidebarGAEvent.SIDEBAR_SIGN_SECTION_OPEN,
                            );
                        }}
                    >
                        {flexibleSignaturesExpectedCount > 1 &&
                            remainSignatures > 0 && (
                                <StatusText
                                    isWrapping
                                    statusColor={props.statusColor}
                                >
                                    <b>
                                        {flexibleSignaturesExpectedCount -
                                            remainSignatures}{' '}
                                        з {flexibleSignaturesExpectedCount}
                                    </b>{' '}
                                    {t`Підписів вашої компанії`}
                                </StatusText>
                            )}
                        {isVersionedDocument && (
                            <FlexBox direction="column">
                                {isActualVersionPage && isActualDraftPage && (
                                    <Alert hideIcon theme="warning">
                                        {t`Чернетка нової версії не опублікована`}
                                    </Alert>
                                )}
                                {isActualVersionPage && !isActualDraftPage && (
                                    <Alert hideIcon theme="success">
                                        {t`Остання версія документа`}
                                    </Alert>
                                )}
                                {!isActualVersionPage && (
                                    <Alert hideIcon theme="warning">
                                        {jt`Переглянути ${documentLastVersionLink} документа`}
                                    </Alert>
                                )}
                                {!isActualDraftPage && documentDraft && (
                                    <Alert hideIcon theme="warning">
                                        {jt`Документ містить неопубліковану чернетку ${documentDraftLink}`}
                                    </Alert>
                                )}
                            </FlexBox>
                        )}
                        {props.doc.isArchived && (
                            <div className={css.actions}>
                                <Alert hideIcon>{t`Документ архівовано`}</Alert>
                            </div>
                        )}
                        {!props.doc.isArchived && (
                            <div className={css.actions}>{props.children}</div>
                        )}
                        <DocumentRequisitesList
                            companyEdrpou={props.companyEdrpou}
                            flows={props.doc.flows}
                            signers={props.doc.signers}
                            signatures={props.doc.signatures}
                        />
                    </CollapsibleGroup>
                </div>
            </MediaQuery>
            <div className={css.block}>
                <CollapsibleGroup
                    isOpened={isVersionedDocument}
                    isTypeHeaderBig
                    title={t`Загальна інформація`}
                    onHeaderClick={(isCollapsed) => {
                        trackTogglingCollapsibleGroup(
                            isCollapsed,
                            DocumentSidebarGAEvent.SIDEBAR_GENERAL_INFO_SECTION_OPEN,
                        );
                    }}
                >
                    <div className={css.antivirusStatus}>
                        <AntivirusStatus
                            antivirusChecks={props.doc.antivirusChecks?.[0]}
                        />
                    </div>
                    <Version doc={props.doc} />
                    <div>
                        <span className={css.owner} data-qa="qa_status_title">
                            {owner.text}
                        </span>
                        <span className={css.icon}>
                            <Icon
                                glyph={
                                    props.doc.isMultilateral
                                        ? SvgMultilateral
                                        : owner.icon
                                }
                            />
                        </span>
                    </div>
                    <div className={css.kind} data-qa="qa_document_status">
                        {props.doc.kindName}
                    </div>
                    {documentDatesBlock}
                    {props.doc.isInput && (
                        <div className={css.info}>
                            <DocumentReceiver
                                receiverData={inboxReceiverData}
                                title={t`Отримувач:`}
                            />
                        </div>
                    )}
                    {props.doc.edrpouRecipient && (
                        <Fragment>
                            <div className={css.info}>
                                <DocumentReceiver
                                    receiverData={receiverData}
                                    title={t`Контрагент:`}
                                />
                            </div>
                            {canChangeRecipient(props.doc) && (
                                <div className={css.pseudolink}>
                                    <PseudoLink
                                        disabled={
                                            !props.hasPermission(
                                                'canSignAndRejectDocument',
                                            )
                                        }
                                        onClick={handleChangeRecipient}
                                    >
                                        {t`Змінити контрагента`}
                                    </PseudoLink>
                                </div>
                            )}
                        </Fragment>
                    )}

                    {!props.doc.isInput && (
                        <div className={css.info} data-qa="qa_employee_upload">
                            <DocumentReceiver
                                receiverData={{
                                    edrpou: '',
                                    email: props.doc.user.email,
                                    name: formatFullName(props.doc.user),
                                }}
                                title={t`Співробітник, що завантажив документ:`}
                            />
                        </div>
                    )}
                </CollapsibleGroup>
            </div>
            <AppModeDisplay hideSignSession hideSharedDocumentView>
                <FeatureShow feature="DOCUMENTS_PRIVATE_ACCESS">
                    <div className={css.block}>
                        <CollapsibleGroup
                            isTypeHeaderBig
                            title={t`Доступ до документа`}
                            onHeaderClick={(isCollapsed) => {
                                trackTogglingCollapsibleGroup(
                                    isCollapsed,
                                    t`Доступ до документа`,
                                );

                                if (!isCollapsed) {
                                    // розкриття блоку доступів на сторінці документу
                                    eventTracking.sendToGTMV4({
                                        event: 'ec_access_settings_open',
                                    });
                                }
                            }}
                        >
                            <AccessLevelBlock />
                        </CollapsibleGroup>
                    </div>
                </FeatureShow>
            </AppModeDisplay>
            {isShowLinkedDocumentsSection && (
                <LinkedDocumentsSection
                    doc={props.doc}
                    companyEdrpou={props.companyEdrpou}
                    onDeleteLink={props.onDeleteLink}
                    onUploadChildDocument={props.onUploadChildDocument}
                    onLinksUpdate={props.onLinksUpdate}
                />
            )}
            {isShowReviewsSection && (
                <div className={css.block}>
                    <CollapsibleGroup
                        isTypeHeaderBig
                        title={t`Внутрішнє погодження`}
                        interactiveTitleIcon={
                            <ProLabel
                                permission={PermissionCategory.REVIEWS}
                                isShowLock
                                lockSize={16}
                                clicked
                                eventGA="ec_approvedoc_docview_trial"
                            />
                        }
                        preview={
                            <ReviewPreviewBar
                                doc={props.doc}
                                onReview={props.onReview}
                                onDeleteReview={props.onDeleteReview}
                            />
                        }
                        onHeaderClick={(isCollapsed) => {
                            trackTogglingCollapsibleGroup(
                                isCollapsed,
                                DocumentSidebarGAEvent.SIDEBAR_REVIEW_SECTION_OPEN,
                            );
                        }}
                    >
                        <ReviewsCounter
                            disableHint
                            pendedCount={pendedCount}
                            approvedCount={approvedCount}
                            rejectedCount={rejectedCount}
                            reviews={reviews}
                            requests={activeReviews}
                            isParallel={isParallel}
                        />
                        <Reviews
                            reviews={reviews}
                            reviewRequests={props.doc.reviewRequests}
                            onReview={props.onReview}
                            onDeleteReview={props.onDeleteReview}
                            onOpenAddReviewersPopup={
                                props.onOpenAddReviewersPopup
                            }
                            onOpenReviewHistoryPopup={
                                props.onOpenReviewHistoryPopup
                            }
                            isParallel={isParallel}
                        />
                    </CollapsibleGroup>
                </div>
            )}
            {isShowCommentsSection && (
                <div className={css.block}>
                    <CollapsibleGroup
                        isOpened={!!props.comments.length}
                        isTypeHeaderBig
                        title={t`Коментарі`}
                        onHeaderClick={(isCollapsed) => {
                            trackTogglingCollapsibleGroup(
                                isCollapsed,
                                DocumentSidebarGAEvent.SIDEBAR_HISTORY_SECTION_OPEN,
                            );
                        }}
                    >
                        <DocumentComments
                            isCommentPopupOpened={props.isCommentPopupOpened}
                            commentAdded={props.commentAdded}
                            doc={props.doc}
                            comments={props.comments}
                            commentLevel={props.commentLevel}
                            userEmail={props.userEmail}
                            onCloseCommentPopup={props.onCloseCommentPopup}
                            onCommentDocument={props.onCommentDocument}
                            openCommentPopup={props.onOpenCommentPopup}
                        />
                    </CollapsibleGroup>
                </div>
            )}
            {isShowTagsSection && (
                <div className={css.block}>
                    <CollapsibleGroup
                        isTypeHeaderBig
                        hint={tagsHint}
                        hintPosition="right"
                        title={t`Ярлики`}
                        interactiveTitleIcon={
                            <ProLabel
                                permission={PermissionCategory.TAGS}
                                isShowLock
                                lockSize={16}
                                clicked
                                eventGA="ec_tags_cdocview_trial"
                            />
                        }
                        onHeaderClick={(isCollapsed) => {
                            trackTogglingCollapsibleGroup(
                                isCollapsed,
                                DocumentSidebarGAEvent.SIDEBAR_TAGS_SECTION_OPEN,
                            );
                        }}
                    >
                        {companyPermissionMap[PermissionCategory.TAGS] && (
                            <Tags
                                isSelectable
                                tags={props.doc.tags}
                                onSelect={props.onTagSelect}
                            />
                        )}
                        <PseudoLink
                            onClick={handleEditTagsPseudoLinkClick}
                        >{t`Редагувати ярлики`}</PseudoLink>
                    </CollapsibleGroup>
                </div>
            )}
            {isShowExtraFieldsSection && (
                <div className={css.block}>
                    <CollapsibleGroup
                        isTypeHeaderBig
                        hint={DOCUMENT_FIELDS_HINT}
                        hintPosition="right"
                        title={t`Додаткові параметри`}
                        interactiveTitleIcon={
                            <ProLabel
                                permission={
                                    PermissionCategory.ADDITIONAL_FIELDS
                                }
                                isShowLock
                                clicked
                                lockSize={16}
                                eventGA="ec_additional_fields_docview_trial"
                            />
                        }
                        onHeaderClick={(isCollapsed) => {
                            trackTogglingCollapsibleGroup(
                                isCollapsed,
                                DocumentSidebarGAEvent.SIDEBAR_ADDITIONAL_FIELDS_SECTION_OPEN,
                            );
                        }}
                    >
                        {props.doc.parameters.length > 0 ? (
                            renderExtraFields()
                        ) : (
                            <Button
                                typeContour
                                theme="blue"
                                width="full"
                                onClick={showProRatePopupHandlerWrapper(
                                    () => {
                                        props.onOpenChangeDocumentPopup({
                                            selectedItemIndex: 'extraFields',
                                        });
                                    },
                                    '',
                                    PermissionCategory.ADDITIONAL_FIELDS,
                                    DocumentSidebarGAEvent.SIDEBAR_ADDITIONAL_FIELDS_SECTION_ADD_FIELD_CLICK,
                                )}
                            >
                                {t`Додати параметри`}
                            </Button>
                        )}
                    </CollapsibleGroup>
                </div>
            )}
        </div>
    );
};

DocumentToolbar.propTypes = {
    hasPermission: PropTypes.func,
    applicationMode: PropTypes.string,
    isInput: PropTypes.bool,
    isInternal: PropTypes.bool,
    isCommentPopupOpened: PropTypes.bool,
    commentAdded: PropTypes.bool,
    doc: PropTypes.object.isRequired,
    comments: PropTypes.array,
    commentLevel: PropTypes.string,
    companyEdrpou: PropTypes.string,
    dateCreated: PropTypes.string,
    dateDelivered: PropTypes.string,
    dateDocument: PropTypes.string,
    dateListing: PropTypes.string,
    dateFinished: PropTypes.string,
    statusColor: PropTypes.string,
    statusTitle: PropTypes.string,
    userEmail: PropTypes.string,
    onCloseCommentPopup: PropTypes.func.isRequired,
    onCommentDocument: PropTypes.func.isRequired,
    onOpenCommentPopup: PropTypes.func.isRequired,
    onOpenTagsEditPopup: PropTypes.func.isRequired,
    onReview: PropTypes.func.isRequired,
    onDeleteReview: PropTypes.func.isRequired,
    onDeleteLink: PropTypes.func.isRequired,
    onLinksUpdate: PropTypes.func.isRequired,
    onOpenAddReviewersPopup: PropTypes.func.isRequired,
    onOpenChangeRecipientPopup: PropTypes.func.isRequired,
    onOpenReviewHistoryPopup: PropTypes.func.isRequired,
    onTagSelect: PropTypes.func.isRequired,
    onUploadChildDocument: PropTypes.func.isRequired,
    isDeleteRequestAllowed: PropTypes.bool,
    isDeleteRequestReceiver: PropTypes.bool,
    isDeleteRequestInitiator: PropTypes.bool,
    onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
    onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
    onRejectDeleteRequest: PropTypes.func.isRequired,
    onAcceptDeleteRequest: PropTypes.func.isRequired,
    onCancelDeleteRequestClick: PropTypes.func.isRequired,
    onCancelDeleteRequestVote: PropTypes.func.isRequired,
    onOpenChangeDocumentPopup: PropTypes.func.isRequired,

    // Extra fields
    documentFields: PropTypes.array.isRequired,
};

const mapStateToProps = (state) => ({
    ...mapStatetoHasPermission(state),
    ...mapStateToApplicationMode(state),
    featureFlags: state.app.flags,
});

export default connect(mapStateToProps)(DocumentToolbar);
