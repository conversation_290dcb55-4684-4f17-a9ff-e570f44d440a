import React from 'react';

import cn from 'classnames';
import { sortByOrder } from 'lib/helpers';
import { getSignerGroupBySignatureRoleId } from 'lib/ts/helpers';
import eventTracking from 'services/analytics/eventTracking';
import { Document, Signature, Signer } from 'services/documents/ts/types';
import { DocumentReviewType } from 'services/enums';
import { t } from 'ttag';

import { Nullable } from '../../../types/general';

import CollapsibleGroup from '../../ui/collapsibleGroup/collapsibleGroup';

import {
    IReview,
    IReviewRequest,
} from '../../reviewHistoryPopup/reviewHistoryPopupTypes';
import ReviewsList from '../../reviews/ReviewsList/ReviewsList';
import ReviewsCounter from '../../reviewsCounter/reviewsCounter';
import SignatureInfo from '../../signatureInfo/signatureInfo';
import StatusText from '../../statusText/statusText';

import css from './ReviewsSignsBlock.css';

type Props = {
    reviewRequests?: IReviewRequest[];
    reviews?: IReview[];
    signatures?: Signature[];
    statusTitle: string;
    statusColor: Document['statusColor'];
    isParallel: boolean;
    reviewStatus: Nullable<string>;
    signers?: Signer[];
};

const ReviewsSignsBlock: React.FC<React.PropsWithChildren<Props>> = ({
    reviewRequests = [],
    reviews = [],
    signatures = [],
    statusColor,
    statusTitle,
    isParallel,
    reviewStatus,
    signers,
}) => {
    const typeReviews = reviews ? reviews.filter((review) => review.type) : [];
    const reviewsEmailsSet = new Set(
        typeReviews.length
            ? typeReviews.map((review) => review.role.user.email)
            : [],
    );
    const reviewsGroupsId = typeReviews.map((review) => review.groupId);

    const activeReviews = (reviewRequests || []).filter((request) => {
        if (request.toRoleId) {
            return !reviewsEmailsSet.has(request.toRole.user.email);
        }
        if (request.toGroupId) {
            return !reviewsGroupsId.includes(request.toGroupId);
        }
    });

    const pendedCount = activeReviews.length || 0;
    const approvedCount = reviews.length
        ? reviews.filter((review) => {
              return review.type === DocumentReviewType.APPROVE;
          }).length
        : 0;
    const rejectedCount = reviews.length
        ? reviews.filter((review) => {
              return review.type === DocumentReviewType.REJECT;
          }).length
        : 0;

    const requests = [...activeReviews].sort(sortByOrder);

    const contentStyles = cn(css.content, {
        [css.red]: statusColor === 'Red',
        [css.orange]: statusColor === 'Orange',
        [css.green]: statusColor === 'Green',
    });

    if (
        requests.length === 0 &&
        typeReviews.length === 0 &&
        signatures.length === 0
    ) {
        return null;
    }

    return (
        <div className={css.root}>
            <div className={contentStyles}>
                <CollapsibleGroup
                    isTypeHeaderBig
                    onHeaderClick={() => {
                        eventTracking.sendToGTM({
                            event: 'click_extend_protocol',
                        });
                    }}
                    title={t`Протокол підписання та погодження документу`}
                    headBlock={
                        <div className={css.header}>
                            <span>{t`Підписання`}:</span>
                            <StatusText isWrapping statusColor={statusColor}>
                                {statusTitle}
                            </StatusText>
                            {reviewStatus !== null && (
                                <>
                                    <span>{t`Погодження:`}</span>
                                    <ReviewsCounter
                                        disableHint
                                        pendedCount={pendedCount}
                                        approvedCount={approvedCount}
                                        rejectedCount={rejectedCount}
                                        reviews={reviews}
                                        requests={activeReviews}
                                        isParallel={isParallel}
                                    />
                                </>
                            )}
                        </div>
                    }
                >
                    <div className={css.body}>
                        <span className={css.label}>{t`Підписання:`}</span>
                        {signatures.length > 0 ? (
                            <div className={css.signaturesList}>
                                {signatures.map((signature) => (
                                    <SignatureInfo
                                        signerGroup={getSignerGroupBySignatureRoleId(
                                            signers,
                                            signature,
                                        )}
                                        signature={signature}
                                        key={signature.id}
                                        noCheck
                                    />
                                ))}
                            </div>
                        ) : (
                            <span
                                data-qa="qa_signature_status"
                                className={css.label}
                            >{t`Підписання відсутнє`}</span>
                        )}
                        <span className={css.label}>{t`Погодження:`}</span>
                        {requests.length > 0 || reviews.length > 0 ? (
                            <div className={css.list}>
                                <ReviewsList
                                    reviewRequests={requests}
                                    reviews={typeReviews}
                                    omitDetails
                                />
                            </div>
                        ) : (
                            <span
                                data-qa="qa_signature_status"
                                className={css.label}
                            >{t`Погодження відсутнє`}</span>
                        )}
                    </div>
                </CollapsibleGroup>
            </div>
        </div>
    );
};

export default ReviewsSignsBlock;
