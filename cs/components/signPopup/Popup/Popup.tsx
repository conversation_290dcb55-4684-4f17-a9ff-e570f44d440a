import React, {
    useCallback,
    useEffect,
    useLayoutEffect,
    useState,
} from 'react';
import Draggable from 'react-draggable';
import { connect, useSelector } from 'react-redux';

import cn from 'classnames';

import { DocumentStatus } from '../../../services/enums';
import { DocumentSource, SignPopupStatus } from '../../../services/enums';
import { StoreState } from '../../../types/store';

import appActionCreators from '../../app/appActionCreators';
import signPopupActionCreators from '../signPopupActionCreators';

import CloseButton from '../../ui/closeButton/closeButton';
import IconButton from '../../ui/iconButton/iconButton';
import Overlay from '../../ui/overlay/overlay';

import FooterPopup from '../FooterPopup/FooterPopup';

import BackSvg from './images/back.svg';

// styles
import css from './Popup.css';

type Props = {
    active?: boolean;
    hiddenCloseButton?: boolean;
    isCloseByButtonOnly?: boolean;
    fullContent?: boolean;
    rounded?: boolean;
    greyColor?: boolean;
    lowered?: boolean;
    centered?: boolean;
    subtitle?: React.ReactNode;
    title?: React.ReactNode;
    onClose: () => void;
    dataQa?: string;
    children: React.ReactNode;
    footer?: boolean;
    isDraggable?: boolean;
    className?: string;
};

type DispatchProps = {
    handleOpen: () => void;
    handleClose: () => void;
    handlePrevStatus: () => void;
};

type StateProps = {
    popupStatus: typeof SignPopupStatus;
};

// TODO refactor this component
const Popup: React.FC<
    React.PropsWithChildren<Props & DispatchProps & StateProps>
> = (props) => {
    const {
        active,
        onClose,
        fullContent,
        greyColor,
        lowered,
        isCloseByButtonOnly,
        dataQa,
        hiddenCloseButton,
        title,
        children,
        rounded,
        centered,
        footer,
        handlePrevStatus,
        popupStatus,
        isDraggable,
    } = props;

    const currentUser = useSelector(
        (state: StoreState) => state.app.currentUser,
    );

    const docs = useSelector((state: StoreState) => state.signPopup.docs);

    const isMultiSign = useSelector(
        (state: StoreState) => state.signPopup.isMultiSign,
    );

    const [activeApplied, setActiveApplied] = useState(false);
    const [mounted, setMounted] = useState(false);

    const doc = docs?.[0];

    const isCurrentCompanyRecipient =
        doc?.edrpouRecipient === currentUser.currentCompany.edrpou;
    const isSignedDocument =
        doc?.statusId === DocumentStatus.FINISHED ||
        doc?.statusId === DocumentStatus.SIGNED_AND_SENT;
    const isSourceEDI = doc?.source === DocumentSource.edi;

    const isNeedPrevBtn =
        (popupStatus === SignPopupStatus.CHOOSE_DOCUMENT_FIELDS ||
            (popupStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY &&
                !isCurrentCompanyRecipient &&
                !isSignedDocument) ||
            popupStatus === SignPopupStatus.CHECK_PRIVATE_KEY ||
            popupStatus === SignPopupStatus.CHECK_USB_KEY) &&
        !isSourceEDI &&
        !doc?.isMultilateral;

    useEffect(() => {
        if (active && mounted) {
            const timer = requestAnimationFrame(() => setActiveApplied(true));

            return () => {
                cancelAnimationFrame(timer);
            };
        }

        return () => {
            // do nothing.
        };
    }, [active, mounted]);

    useEffect(() => {
        if (!active) {
            // todo: this has bad global effect [reset overflow state]
            document.body.style.overflow = '';
            return () => {
                // do nothing.
            };
        } else {
            // when becomes not active, reset activeApplied
            return () => {
                setActiveApplied(false);
            };
        }
    }, [active]);

    useEffect(() => {
        setMounted(true);

        return () => {
            // todo: this has bad global effect [reset overflow state]
            document.body.style.overflow = '';
        };
    }, []);

    const handleClose = useCallback(() => {
        onClose();
        document.body.style.overflow = '';
    }, [onClose]);

    useLayoutEffect(() => {
        // may be unmounted externally (active would never be false)
        if (active) {
            props.handleOpen();
            return props.handleClose;
        } else {
            return () => {
                // do nothing.
            };
        }
    }, [active]);

    if (!active) {
        return null;
    }

    const totalClasses = cn(
        css.root,
        {
            [css.active]: activeApplied,
            [css.fullContent]: fullContent,
            [css.rootGrey]: greyColor,
            [css.rootLowered]: lowered,
            [css.rootCentered]: centered,
            [css.rounded]: rounded,
        },
        props.className,
    );

    const content = () => {
        return (
            <div
                className={totalClasses}
                data-qa={dataQa ? `qa_popup ${dataQa}` : 'qa_popup'}
            >
                {isNeedPrevBtn && (
                    <div
                        className={css.prevStatusBtn}
                        onClick={handlePrevStatus}
                    >
                        <IconButton svg={BackSvg} />
                    </div>
                )}
                {!hiddenCloseButton && (
                    <div className={css.closeButton}>
                        <CloseButton onClose={handleClose} />
                    </div>
                )}
                <div className={css.main}>
                    {title && (
                        <div className={css.header}>
                            {title && <h3 className={css.title}>{title}</h3>}
                        </div>
                    )}
                    {children}
                    {footer && (
                        <div className={css.footer}>
                            <FooterPopup />
                        </div>
                    )}
                </div>
            </div>
        );
    };

    return (
        <Overlay
            active={active}
            isCloseByButtonOnly={isCloseByButtonOnly}
            onClose={handleClose}
        >
            {isDraggable ? (
                <Draggable>
                    <span className={css.draggableWrapper}>{content()}</span>
                </Draggable>
            ) : (
                content()
            )}
        </Overlay>
    );
};

const mapDispatchToProps = {
    handleOpen: appActionCreators.onOpenPopup,
    handleClose: appActionCreators.onClosePopup,
    handlePrevStatus: signPopupActionCreators.showPrevStatus,
};

const mapStateToProps = (state: StoreState) => ({
    popupStatus: state.signPopup.popupStatus,
});

export default connect<StateProps, DispatchProps, Props>(
    mapStateToProps,
    mapDispatchToProps,
)(Popup);
