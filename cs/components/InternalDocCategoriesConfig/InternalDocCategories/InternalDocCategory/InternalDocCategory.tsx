import React from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip, FlexBox, Paragraph } from '@vchasno/ui-kit';

import { INTERNAL_DOC_CATEGORIES_CARD_ID } from 'components/InternalDocCategoriesConfig/constants';
import { useInternalDocCategoriesContext } from 'components/InternalDocCategoriesConfig/context';
import { DocumentCategory } from 'gql-types';
import CloseSvg from 'icons/cross.svg';
import PenSvg from 'icons/pen.svg';
import { getCurrentCompanyBillingCompanyConfig } from 'selectors/app.selectors';
import { t } from 'ttag';
import IconButton from 'ui/iconButton/iconButton';

import css from './InternalDocCategory.css';

interface InternalDocCategoryProps {
    category: DocumentCategory;
}

const InternalDocCategory: React.FC<InternalDocCategoryProps> = ({
    category,
}) => {
    const {
        setDocCategoryForEdit,
        onOpenDeletePopup,
    } = useInternalDocCategoriesContext();
    const billingCompanyConfig = useSelector(
        getCurrentCompanyBillingCompanyConfig,
    );
    const internalDocumentEnabledConfig =
        billingCompanyConfig.internalDocumentsEnabled;

    const onEditButtonClick = () => {
        setDocCategoryForEdit(category);
    };

    const handleOpenDeletePopup = () => {
        onOpenDeletePopup(category);
    };

    return (
        <Paragraph className={css.container}>
            {category.title}
            {internalDocumentEnabledConfig && (
                <div className={css.actionsWrapper}>
                    <FlexBox className={css.actions} align="center" gap={4}>
                        <BlackTooltip title={t`Редагувати`} disableInteractive>
                            <a href={`#${INTERNAL_DOC_CATEGORIES_CARD_ID}`}>
                                <IconButton
                                    svg={PenSvg}
                                    onClick={onEditButtonClick}
                                />
                            </a>
                        </BlackTooltip>
                        <BlackTooltip title={t`Видалити`} disableInteractive>
                            <span>
                                <IconButton
                                    svg={CloseSvg}
                                    onClick={handleOpenDeletePopup}
                                />
                            </span>
                        </BlackTooltip>
                    </FlexBox>
                </div>
            )}
        </Paragraph>
    );
};

export default InternalDocCategory;
