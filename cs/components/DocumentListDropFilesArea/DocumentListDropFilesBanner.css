.root {
    position: relative;
    display: flex;
    height: 160px;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: rgba(219, 229, 234, 0.3);
    border-radius: var(--border-radius);
    transition: background-color 0.3s;
}

.root::after {
    position: absolute;
    border-radius: inherit;
    content: '';
    inset: 0;
    pointer-events: none;
}

@media (max-width: 768px) {
    .root {
        display: none;
    }
}

.title {
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
}

.subTitle {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: center;
}

.icon {
    width: 25px;
    height: 25px;
    transform: translateY(-10px);
}

.icon svg {
    color: var(--slate-grey-color);
}

.closeIcon {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    width: 30px;
    height: 30px;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s;
}

.closeIcon svg {
    width: 20px;
    height: 20px;
    color: var(--content-color);
}

.closeIcon:hover {
    background-color: var(--hover-bg);
}

.dropBoxActive {
    background-color: rgba(175, 201, 224, 0.2);
}

.dropBoxActive::after {
    pointer-events: auto;
}

.dropBoxActive .icon svg {
    animation: dropActive 1s infinite;
    color: var(--link-color);
}

@keyframes dropActive {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(5px);
    }

    100% {
        transform: translateY(0);
    }
}
