.root {
    position: relative;
}

:global(.vchasno-dark-theme) .root {
    --grey-color: var(--content-color);
}

.title {
    margin-bottom: 18px;
    font-weight: bold;
}

.icon {
    display: block;
    width: 20px;
    height: 20px;
    margin-left: auto;
    color: inherit;
    cursor: pointer;
}

.icon svg {
    color: var(--grey-color);
}

.icon:hover {
    color: inherit;
}

.content {
    min-width: 260px;
    box-sizing: border-box;
    padding: 20px;
}

.list {
    max-height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
}

.item + .item {
    margin-top: 15px;
}

.counterBlock {
    padding: 6px 0;
    margin: 18px 0;
    background: var(--corporate-color);
    border-radius: 15px;
    color: #333;
    text-align: center;
}

.count {
    margin-right: 10px;
    font-weight: bold;
}
