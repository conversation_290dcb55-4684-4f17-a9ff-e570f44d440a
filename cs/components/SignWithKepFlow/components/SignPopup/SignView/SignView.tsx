import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import {
    KEP_SIGN_FLOW_ANALYTICS_EVENT,
    MAX_SIGN_CHUNK_SIZE,
} from 'components/SignWithKepFlow/constants';
import {
    CertificatePasswordType,
    CloudCertificateInfo,
    SignResult,
    SignatureType,
} from 'components/SignWithKepFlow/types';
import { formatUserName } from 'components/SignWithKepFlow/utils';
import Icon from 'components/ui/icon/icon';
import Popup from 'components/ui/popup/popup';
import { LayoutGroup, motion } from 'framer-motion';
import {
    getDocumentsCountByFilter,
    getSelectAllDocuments,
} from 'selectors/documentList.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import InlineButton from '../../InlineButton';
import PasswordTab from './PasswordTab';
import PinTab from './PinTab';
import QRTab from './QRTab';

import KepLogo3dGlyph from '../../../assets/kep-logo-3d.svg';

import css from './SignView.css';

type SignWay = CertificatePasswordType | 'qr';

interface SignViewProps {
    onClose: () => void;
    onChooseFallbackSignMethod: () => void;
    onForgotPassword: (passwordType: CertificatePasswordType) => void;
    onSignComplete: (signResults: SignResult[]) => void;
    documents: Document[];
    certificate: CloudCertificateInfo;
}

const SignView: React.FC<SignViewProps> = ({
    documents,
    certificate,
    onClose,
    onForgotPassword,
    onChooseFallbackSignMethod,
    onSignComplete,
}) => {
    const isSelectedAllDocuments = useSelector(getSelectAllDocuments);
    const documentsCountByFilter = useSelector(getDocumentsCountByFilter);
    const documentsCount = isSelectedAllDocuments
        ? documentsCountByFilter
        : documents.length;
    const isSignViaQrEnabled = documentsCount <= MAX_SIGN_CHUNK_SIZE;

    const [signWay, setSignWay] = useState<SignWay>(() => {
        return certificate.password_type || 'pin_code';
    });

    const options = useMemo((): { value: SignWay; label: string }[] => {
        const opts: { value: SignWay; label: string }[] = [
            { value: 'pin_code', label: t`PIN-код` },
            { value: 'password', label: t`Пароль` },
        ];

        if (isSignViaQrEnabled) {
            opts.push({ value: 'qr', label: t`QR-код` });
        }

        return opts;
    }, [isSignViaQrEnabled]);

    const title = useMemo(() => {
        const isMultipleDocuments = documents.length > 1;
        const { name, surname } = certificate;

        if (name) {
            const formattedName = formatUserName(
                { surname: surname || '', name: name || '' },
                { format: 'name-and-surname' },
            );

            return isMultipleDocuments
                ? t`${formattedName}\nпідписує документи`
                : t`${formattedName}\nпідписує документ`;
        } else {
            return isMultipleDocuments
                ? t`Підписання документів`
                : t`Підписання документу`;
        }
    }, [documents.length, certificate]);

    const subtitle = useMemo(() => {
        const { signature_type: signatureType } = certificate;

        if (signWay === 'qr') {
            return t`Зіскануйте QR-код застосунком Вчасно.КЕП`;
        }

        if (signWay === 'pin_code') {
            const map: Record<SignatureType, string> = {
                signature: t`Введіть PIN-код від підпису`,
                stamp: t`Введіть PIN-код від печатки`,
                rro: t`Введіть PIN-код від печатки пРРО`,
            };

            return map[signatureType];
        }

        const map: Record<SignatureType, string> = {
            signature: t`Введіть пароль від підпису`,
            stamp: t`Введіть пароль від печатки`,
            rro: t`Введіть пароль від печатки пРРО`,
        };

        return map[signatureType];
    }, [signWay, certificate]);

    const handleTabChange = (value: SignWay) => {
        setSignWay(value);
    };

    const handleChooseFallbackSignMethod = () => {
        const MAP_VIEW_TO_EVENT: Record<SignWay, string> = {
            pin_code: KEP_SIGN_FLOW_ANALYTICS_EVENT.PIN_TAB_FALLBACK,
            password: KEP_SIGN_FLOW_ANALYTICS_EVENT.PASSWORD_TAB_FALLBACK,
            qr: KEP_SIGN_FLOW_ANALYTICS_EVENT.QR_TAB_FALLBACK,
        };

        eventTracking.sendToGTMV4({
            event: MAP_VIEW_TO_EVENT[signWay],
        });
        onChooseFallbackSignMethod();
    };

    return (
        <Popup
            className={css.root}
            overlayClassName={css.overlay}
            active
            onClose={onClose}
        >
            <div className={css.logo}>
                <Icon glyph={KepLogo3dGlyph} />
            </div>
            <h1 className={css.title}>{title}</h1>
            <p className={css.subtitle}>{subtitle}</p>

            <div className={css.tabsContainer}>
                <div className={css.tabsList}>
                    <LayoutGroup id="sign-view-tabs">
                        {options.map(({ value, label }) => (
                            <button
                                key={value}
                                className={cn(css.tab, {
                                    [css.selectedTab]: signWay === value,
                                })}
                                onClick={() => handleTabChange(value)}
                            >
                                {label}

                                {signWay === value && (
                                    <motion.div
                                        className={css.tabIndicator}
                                        style={{ originY: 'top' }}
                                        layoutId="active-tab-indicator"
                                        transition={{ duration: 0.2 }}
                                    />
                                )}
                            </button>
                        ))}
                    </LayoutGroup>
                </div>
            </div>

            {signWay === 'pin_code' && (
                <PinTab
                    certificate={certificate}
                    onSignComplete={onSignComplete}
                    documents={documents}
                    onForgotPinCode={() => onForgotPassword('pin_code')}
                />
            )}
            {signWay === 'password' && (
                <PasswordTab
                    certificate={certificate}
                    onSignComplete={onSignComplete}
                    documents={documents}
                    onForgotPassword={() => onForgotPassword('password')}
                />
            )}
            {signWay === 'qr' && (
                <QRTab
                    certificate={certificate}
                    documents={documents}
                    onSignComplete={onSignComplete}
                />
            )}

            <FlexBox direction="column" gap={32}>
                <InlineButton onClick={handleChooseFallbackSignMethod}>
                    {t`Підпис не «Вчасно.КЕП»`}
                </InlineButton>
            </FlexBox>
        </Popup>
    );
};

export default SignView;
