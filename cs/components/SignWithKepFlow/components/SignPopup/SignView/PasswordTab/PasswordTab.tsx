import React, { useEffect, useRef, useState } from 'react';

import { Button, PasswordInput, Spinner } from '@vchasno/ui-kit';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { useSignWithPassword } from 'components/SignWithKepFlow/hooks';
import {
    CloudCertificateInfo,
    SignResult,
} from 'components/SignWithKepFlow/types';
import { getErrorMessage } from 'lib/errors';
import { captureException } from 'lib/sentry';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import InlineButton from '../../../InlineButton';

import css from './PasswordTab.css';

interface PasswordTabProps {
    certificate: CloudCertificateInfo;
    documents: Document[];
    onSignComplete: (signResults: SignResult[]) => void;
    onForgotPassword: () => void;
}

const PasswordTab: React.FC<PasswordTabProps> = ({
    certificate,
    documents,
    onSignComplete,
    onForgotPassword,
}) => {
    const [password, setPassword] = useState('');
    const passwordInputRef = useRef<HTMLInputElement>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const sign = useSignWithPassword();

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PASSWORD_TAB_SHOW,
        });
    }, []);

    const handleSubmit = async () => {
        try {
            setIsLoading(true);
            setError(null);

            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PASSWORD_TAB_SUBMIT,
            });

            const signResults = await sign({
                clientId: certificate.acsk_key_id,
                signatureType: certificate.signature_type,
                documents,
                password,
            });

            onSignComplete(signResults);
        } catch (err) {
            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PASSWORD_TAB_ERROR,
            });
            captureException(err);
            setError(getErrorMessage(err));
            setPassword('');
            passwordInputRef.current?.focus();
        } finally {
            setIsLoading(false);
        }
    };

    const handleForgotPassword = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PASSWORD_TAB_FORGOT_PASSWORD,
        });
        onForgotPassword();
    };

    return (
        <div className={css.root}>
            {isLoading && (
                <div className={css.loadingOverlay}>
                    <Spinner width={40} color="#ff5a5f" />
                </div>
            )}
            <div className={css.password}>
                <PasswordInput
                    ref={passwordInputRef}
                    value={password}
                    onChange={(event) => setPassword(event.target.value)}
                    label={t`Пароль`}
                    className={css.passwordInput}
                    error={error || undefined}
                    hideEmptyMeta
                />
                {error && (
                    <InlineButton
                        className={css.forgotPassword}
                        onClick={handleForgotPassword}
                    >
                        {t`Забули пароль?`}
                    </InlineButton>
                )}
            </div>
            <Button
                wide
                size="lg"
                theme="pink"
                disabled={!password}
                onClick={handleSubmit}
            >
                {t`Підписати Вчасно.КЕП`}
            </Button>
        </div>
    );
};

export default PasswordTab;
