import React, { useEffect } from 'react';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { useSignWithQR } from 'components/SignWithKepFlow/hooks';
import {
    CloudCertificateInfo,
    SignResult,
} from 'components/SignWithKepFlow/types';
import QRCode from 'components/ui/QRCode';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';

import css from './QRTab.css';

interface QRTabProps {
    certificate: CloudCertificateInfo;
    documents: Document[];
    onSignComplete: (signResults: SignResult[]) => void;
}

const QRTab: React.FC<QRTabProps> = ({
    documents,
    certificate,
    onSignComplete,
}) => {
    const { qrQuery, statusQuery } = useSignWithQR({
        documents,
        signatureType: certificate.signature_type,
        onSignComplete,
    });

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.QR_TAB_SHOW,
        });
    }, []);

    return (
        <div className={css.root}>
            <QRCode
                isLoading={qrQuery.isLoading || qrQuery.isRefetching}
                isError={qrQuery.isError || statusQuery.isError}
                isStale={qrQuery.isStale}
                onRetry={qrQuery.refetch}
                value={qrQuery.data}
                size={196}
            />
        </div>
    );
};

export default QRTab;
