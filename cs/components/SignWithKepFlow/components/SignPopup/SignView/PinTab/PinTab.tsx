import React, { useEffect, useRef, useState } from 'react';

import { Spinner } from '@vchasno/ui-kit';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { useSignWithPassword } from 'components/SignWithKepFlow/hooks';
import {
    CloudCertificateInfo,
    SignResult,
} from 'components/SignWithKepFlow/types';
import PinInput, { PinInputRef } from 'components/ui/PinInput';
import { getErrorMessage } from 'lib/errors';
import { captureException } from 'lib/sentry';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import InlineButton from '../../../InlineButton';

import css from './PinTab.css';

interface PinTabProps {
    certificate: CloudCertificateInfo;
    documents: Document[];
    onSignComplete: (signResults: SignResult[]) => void;
    onForgotPinCode: () => void;
}

const PinTab: React.FC<PinTabProps> = ({
    certificate,
    documents,
    onSignComplete,
    onForgotPinCode,
}) => {
    const INITIAL_PIN = ['', '', '', ''];
    const [pin, setPin] = useState<string[]>(INITIAL_PIN);
    const pinInputRef = useRef<PinInputRef>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const sign = useSignWithPassword();

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PIN_TAB_SHOW,
        });
    }, []);

    const handlePinChange = (payload: { value: string[] }) => {
        setError(null);
        setPin(payload.value);
    };

    const handlePinComplete = async (payload: {
        value: string[];
        valueAsString: string;
    }) => {
        try {
            setIsLoading(true);
            setError(null);

            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PIN_TAB_SUBMIT,
            });

            const signResults = await sign({
                clientId: certificate.acsk_key_id,
                documents,
                password: payload.valueAsString,
                signatureType: certificate.signature_type,
            });

            onSignComplete(signResults);
        } catch (err) {
            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PIN_TAB_ERROR,
            });
            captureException(err);
            setError(getErrorMessage(err));
            setPin(INITIAL_PIN);
            pinInputRef.current?.focus();
        } finally {
            setIsLoading(false);
        }
    };

    const handleForgotPinCode = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.PIN_TAB_FORGOT_PIN,
        });
        onForgotPinCode();
    };

    return (
        <div className={css.root}>
            {isLoading && (
                <div className={css.loadingOverlay}>
                    <Spinner width={40} color="#ff5a5f" />
                </div>
            )}
            <div className={css.pinInputContainer}>
                <PinInput
                    ref={pinInputRef}
                    length={4}
                    masked
                    value={pin}
                    onChange={handlePinChange}
                    onComplete={handlePinComplete}
                    error={error}
                    autoFocus
                    size="lg"
                />
                {error && (
                    <InlineButton
                        className={css.forgotPinCode}
                        onClick={handleForgotPinCode}
                    >
                        {t`Забули PIN-код?`}
                    </InlineButton>
                )}
            </div>
        </div>
    );
};

export default PinTab;
