import React from 'react';

import AppStoreSvg from 'components/KepMobileAppAd/assets/app-store.svg';
import GooglePlaySvg from 'components/KepMobileAppAd/assets/google-play.svg';
import BackButton from 'components/ui/BackButton/BackButton';
import QRCode from 'components/ui/QRCode';
import Icon from 'components/ui/icon/icon';
import Popup from 'components/ui/popup/popup';
import { t } from 'ttag';

import {
    KEP_APP_APP_STORE_URL,
    KEP_APP_GOOGLE_PLAY_URL,
} from '../../constants';

import kepLogo3dSvgUrl from '../../assets/kep-logo-3d.svg';

import css from './DownloadKepAppPopup.css';

export interface DownloadKepAppPopupProps {
    onClose: () => void;
    onBack?: () => void;
}

const DownloadKepAppPopup: React.FC<DownloadKepAppPopupProps> = ({
    onClose,
    onBack,
}) => {
    return (
        <Popup
            active
            className={css.root}
            overlayClassName={css.overlay}
            inPortal
            onClose={onClose}
        >
            {onBack && <BackButton onClick={onBack} className={css.backBtn} />}

            <div className={css.appOverlay}>
                <div className={css.appExample} />
            </div>

            <div className={css.drawer}>
                <div className={css.drawerContent}>
                    <div className={css.appIcon}>
                        <Icon glyph={kepLogo3dSvgUrl} />
                    </div>
                    <h3 className={css.headerTitle}>
                        {t`Завантажуй застосунок Вчасно.КЕП`}
                    </h3>

                    <div className={css.qrsContainer}>
                        <div className={css.qrContainer}>
                            <div className={css.qr}>
                                <QRCode
                                    value={KEP_APP_APP_STORE_URL}
                                    size={116}
                                    gap={0}
                                />
                            </div>
                            <a
                                className={css.platform}
                                href={KEP_APP_APP_STORE_URL}
                            >
                                <Icon glyph={AppStoreSvg} />
                            </a>
                        </div>
                        <div className={css.qrContainer}>
                            <div className={css.qr}>
                                <QRCode
                                    value={KEP_APP_GOOGLE_PLAY_URL}
                                    size={116}
                                    gap={0}
                                />
                            </div>
                            <a
                                className={css.platform}
                                href={KEP_APP_GOOGLE_PLAY_URL}
                            >
                                <Icon glyph={GooglePlaySvg} />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </Popup>
    );
};

export default DownloadKepAppPopup;
