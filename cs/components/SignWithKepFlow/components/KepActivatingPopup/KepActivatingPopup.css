.root {
    width: 470px;
    max-width: calc(100vw - 20px);
    padding: 52px 40px;
    background: var(--white-bg);
    border-radius: 12px;
}

.content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.hourglass {
    width: 60px;
    height: 60px;
    margin-bottom: 24px;
}

.title {
    margin-bottom: 16px;
    color: var(--content-color);
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
}

.description {
    margin-bottom: 32px;
    color: var(--content-secondary-color);
    font-size: 14px;
    line-height: 20px;
}

.actions {
    display: flex;
    justify-content: center;
}

@media screen and (max-width: 768px) {
    .root {
        width: 100%;
        padding: 32px 20px;
        margin: auto auto 10px auto;
    }

    .overlay>div {
        padding-bottom: 0;
    }
}
