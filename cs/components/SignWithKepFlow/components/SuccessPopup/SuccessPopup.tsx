import React, { useEffect, useState } from 'react';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import LazyLottie from 'components/ui/LazyLottie';
import Popup from 'components/ui/popup/popup';
import { motion } from 'framer-motion';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import css from './SuccessPopup.css';

interface SuccessPopupProps {
    onAnimationComplete: () => void;
    onClose: () => void;
}

const SuccessPopup: React.FC<SuccessPopupProps> = ({
    onAnimationComplete,
    onClose,
}) => {
    const [view, setView] = useState<'signature' | 'checkmark'>('signature');

    const handleComplete = async () => {
        await new Promise((resolve) => setTimeout(resolve, 250));
        onAnimationComplete();
    };

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.SUCCESS_SHOW,
        });
    }, []);

    return (
        <Popup
            active
            onClose={onClose}
            className={css.root}
            overlayClassName={css.overlay}
        >
            <div className={css.content}>
                {view === 'signature' && (
                    <LazyLottie
                        getAnimationData={() =>
                            import('../../assets/signature-lottie.json').then(
                                (module) => module.default,
                            )
                        }
                        loop={false}
                        onComplete={() => setView('checkmark')}
                        style={{ width: 236, height: 194 }}
                        width={236}
                        height={194}
                    />
                )}

                {view === 'checkmark' && (
                    <div className={css.checkmark}>
                        <LazyLottie
                            getAnimationData={() =>
                                import(
                                    '../../assets/checkmark-lottie.json'
                                ).then((module) => module.default)
                            }
                            loop={false}
                            onComplete={handleComplete}
                            style={{ width: 165, height: 136 }}
                            width={165}
                            height={136}
                        />
                    </div>
                )}

                <motion.span
                    className={css.label}
                    variants={{
                        hidden: { opacity: 0 },
                        visible: { opacity: 1, transition: { delay: 0.7 } },
                    }}
                    initial="hidden"
                    animate={view === 'checkmark' ? 'visible' : 'hidden'}
                >
                    {t`Підписано!`}
                </motion.span>
            </div>
        </Popup>
    );
};

export default SuccessPopup;
