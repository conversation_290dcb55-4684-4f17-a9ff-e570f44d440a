import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@vchasno/ui-kit';

import cn from 'classnames';
import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import { composeKepAdPopupLsKey } from 'components/SignWithKepFlow/utils';
import LazyLottie from 'components/ui/LazyLottie';
import Icon from 'components/ui/icon/icon';
import Popup from 'components/ui/popup/popup';
import { setLocalStorageItem } from 'lib/webStorage';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import InlineButton from '../InlineButton';

import KepLogo3dGlyph from '../../assets/kep-logo-3d.svg';

import person1PngUrl from '../../assets/person-1.png';
import person2PngUrl from '../../assets/person-2.png';
import person3PngUrl from '../../assets/person-3.png';
import person4PngUrl from '../../assets/person-4.png';

import css from './GetKepAdPopup.css';

interface KepAdPopupProps {
    onClose: () => void;
    onChooseFallbackSignMethod: () => void;
    onCreateNewKep: () => void;
    onDownloadKepApp: () => void;
}

const GetKepAdPopup: React.FC<KepAdPopupProps> = ({
    onClose,
    onChooseFallbackSignMethod,
    onCreateNewKep,
    onDownloadKepApp,
}) => {
    const currentUserInfo = useSelector(
        signWithKepFlowSelectors.selectCurrentUserInfo,
    );

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.AD_SHOW,
        });
        setLocalStorageItem(
            composeKepAdPopupLsKey(currentUserInfo),
            Date.now(),
        );
    }, []);

    const handleChooseFallbackSignMethod = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.AD_FALLBACK,
        });
        onChooseFallbackSignMethod();
    };

    const handleCreateNewKep = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.AD_CREATE_NEW_KEP,
        });
        onCreateNewKep();
    };

    const handleDownloadKepApp = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.AD_DOWNLOAD_KEP_APP,
        });
        onDownloadKepApp();
    };

    return (
        <Popup
            active
            className={cn(css.root)}
            overlayClassName={css.overlay}
            inPortal
            onClose={onClose}
        >
            <div className={css.left}>
                <div className={css.leftContent}>
                    <div>
                        <div className={css.logo}>
                            <Icon glyph={KepLogo3dGlyph} />
                        </div>

                        <h1 className={css.title}>
                            {t`Забудьте про незручні ключі – отримайте «Вчасно.КЕП» всього за 2 хвилини!`}
                        </h1>

                        <div className={css.statistics}>
                            <div className={css.statisticsPersons}>
                                <img
                                    role="presentation"
                                    alt={t`Клієнт 1`}
                                    src={person1PngUrl}
                                    className={css.statisticsPersonsItem}
                                />
                                <img
                                    role="presentation"
                                    alt={t`Клієнт 2`}
                                    src={person2PngUrl}
                                    className={css.statisticsPersonsItem}
                                />
                                <img
                                    role="presentation"
                                    alt={t`Клієнт 3`}
                                    src={person3PngUrl}
                                    className={css.statisticsPersonsItem}
                                />
                                <img
                                    role="presentation"
                                    alt={t`Клієнт 4`}
                                    src={person4PngUrl}
                                    className={css.statisticsPersonsItem}
                                />
                            </div>
                            <p className={css.statisticsDescription}>
                                {t`Більше 200 тисяч українців\nвже обрали «Вчасно.КЕП»`}
                            </p>
                        </div>

                        <div className={css.buttons}>
                            <Button
                                theme="secondary"
                                wide
                                size="lg"
                                className={css.buttonSecondary}
                                onClick={handleChooseFallbackSignMethod}
                            >
                                {t`Підписи не «Вчасно.КЕП»`}
                            </Button>
                            <Button
                                theme="pink"
                                size="lg"
                                className={css.buttonPrimary}
                                onClick={handleCreateNewKep}
                            >
                                {t`Отримати`}
                            </Button>
                        </div>
                    </div>
                </div>
                <div className={css.leftFooter}>
                    {t`Не маєте застосунка?`}{' '}
                    <InlineButton onClick={handleDownloadKepApp}>
                        {t`Завантажити «Вчасно.КЕП»`}
                    </InlineButton>
                </div>
            </div>
            <div className={css.right}>
                <div className={css.frame}>
                    <LazyLottie
                        className={css.lottie}
                        getAnimationData={() =>
                            import('../../assets/kep-sign-ad-lottie.json').then(
                                (module) => module.default,
                            )
                        }
                        width={506}
                        height={285}
                    />
                </div>
            </div>
        </Popup>
    );
};

export default GetKepAdPopup;
