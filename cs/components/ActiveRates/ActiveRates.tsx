import React from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import {
    getActiveBillingAccounts,
    getCompanyActiveIntegrationRate,
    getCompanyActiveRates,
    getIsCompanyHasOnlyFreeRate,
    getIsFreeRateDocumentsTerminate,
    getMostExpensivePayedWebRate,
    isAdminSelector,
    isCompanyHasPayedRateSelector,
} from 'selectors/app.selectors';
import {
    isRateDateTerminate,
    isRateDocumentsTerminate,
    isTrialRatesInActiveRates,
} from 'services/billing';

import ExpiresFreeRateBlock from './ExpiresFreeRateBlock/ExpiresFreeRateBlock';
import ExpiresRateBlock from './ExpiresRateBlock/ExpiresRateBlock';
import FreeRateButton from './FreeRateButton/FreeRateButton';
import TrialRateBlock from './TrialRateBlock/TrialRateBlock';

const hideRateBlockByLocation = (location: ReturnType<typeof useLocation>) => {
    return location.pathname.startsWith('/app/checkout');
};
// тільки для користувачів на Базовому тарифі - Коли користувач знаходиться на сторінці тарифів, приховувати кнопку “Оплатити тариф“ в хедері.
// Чому? Є клієнти, після того, як обрали тариф, не натискають кнопку “Перейти на тариф“ на сторінці, а натискають кнопку “Оплатити тариф“ в хедері.
// Конверсія в оплату у таких клієнтів невисока. Для того, щоб не плутати клієнтів, треба приховати кнопку в хедері.
const hideOnlyFreeRateBlock = (location: ReturnType<typeof useLocation>) => {
    return location.pathname.startsWith('/app/checkout-rates/web');
};

const ActiveRates: React.FC<React.PropsWithChildren<unknown>> = () => {
    const location = useLocation();
    const billingAccounts = useSelector(getActiveBillingAccounts);
    const integrationRate = useSelector(getCompanyActiveIntegrationRate);
    const mostExpensivePayedWebRate = useSelector(getMostExpensivePayedWebRate);
    const isFreeRateDocumentsTerminate = useSelector(
        getIsFreeRateDocumentsTerminate,
    );
    const isCompanyHasPayedRate = useSelector(isCompanyHasPayedRateSelector);
    const isCompanyHasOnlyFreeRate = useSelector(getIsCompanyHasOnlyFreeRate);
    const isAdmin = useSelector(isAdminSelector);
    const activeRates = useSelector(getCompanyActiveRates);
    const isActiveTrialRates = isTrialRatesInActiveRates(activeRates);

    const isMostExpensivePayedRateTerminate =
        isRateDateTerminate(mostExpensivePayedWebRate) ||
        isRateDocumentsTerminate(billingAccounts, mostExpensivePayedWebRate);

    const isIntegrationRateTerminate =
        isRateDateTerminate(integrationRate) ||
        isRateDocumentsTerminate(billingAccounts, integrationRate);

    const isShowFreeRateButton =
        !isIntegrationRateTerminate &&
        !isMostExpensivePayedRateTerminate &&
        !isCompanyHasPayedRate &&
        !isActiveTrialRates &&
        isCompanyHasOnlyFreeRate &&
        !isFreeRateDocumentsTerminate;

    const isShowTrialRatesBlock =
        isAdmin && !isShowFreeRateButton && isActiveTrialRates;

    const isShowExpiresRateBlock =
        isAdmin &&
        !isShowTrialRatesBlock &&
        !isShowFreeRateButton &&
        (isMostExpensivePayedRateTerminate || isIntegrationRateTerminate);

    const isShowExpiresFreerRateBlock =
        isAdmin &&
        !isShowTrialRatesBlock &&
        !isShowFreeRateButton &&
        !isShowExpiresRateBlock &&
        isCompanyHasOnlyFreeRate &&
        isFreeRateDocumentsTerminate;

    if (hideRateBlockByLocation(location)) {
        return null;
    }

    if (isCompanyHasOnlyFreeRate && hideOnlyFreeRateBlock(location)) {
        return null;
    }

    return (
        <>
            {isShowTrialRatesBlock && <TrialRateBlock />}
            {isShowExpiresRateBlock && <ExpiresRateBlock />}
            {isShowFreeRateButton && <FreeRateButton />}
            {isShowExpiresFreerRateBlock && <ExpiresFreeRateBlock />}
        </>
    );
};

export default ActiveRates;
