import { UserRole } from 'services/enums';
import { t } from 'ttag';

import {
    CommonRolePermissions,
    MasterAdminPermissions,
    RolePermissionsPayload,
} from '../../types/user';

export const permissionsToApiPayloadKey: Record<
    CommonRolePermissions,
    keyof RolePermissionsPayload
> = {
    canViewDocument: 'can_view_document',
    canViewPrivateDocument: 'can_view_private_document',
    canCommentDocument: 'can_comment_document',
    canUploadDocument: 'can_upload_document',
    canDownloadDocument: 'can_download_document',
    canPrintDocument: 'can_print_document',
    canDeleteDocument: 'can_delete_document',
    canSignAndRejectDocument: 'can_sign_and_reject_document',
    canRemoveItselfFromApproval: 'can_remove_itself_from_approval',
    canEditCompany: 'can_edit_company',
    canEditRoles: 'can_edit_roles',
    canEditDocumentTemplates: 'can_edit_document_automation',
    canInviteCoworkers: 'can_invite_coworkers',
    canEditDocumentFields: 'can_edit_document_fields',
    canEditTemplates: 'can_edit_templates',
    canArchiveDocuments: 'can_archive_documents',
    canDeleteArchivedDocuments: 'can_delete_archived_documents',
    canCreateTags: 'can_create_tags',
    canEditDirectories: 'can_edit_directories',
    canDownloadActions: 'can_download_actions',
    canEditCompanyContact: 'can_edit_company_contact',
    canEditSecurity: 'can_edit_security',
    canChangeDocumentSignersAndReviewers:
        'can_change_document_signers_and_reviewers',
    canDeleteDocumentExtended: 'can_delete_document_extended',
    canEditRequiredFields: 'can_edit_required_fields',
    canEditDocumentCategory: 'can_edit_document_category',
    canViewCoworkers: 'can_view_coworkers',
};

export const permissionsToLabel: Record<CommonRolePermissions, string> = {
    canViewDocument: t`Перегляд всіх документів із спільним доступом`,
    canViewPrivateDocument: t`Перегляд всіх документів із приватним доступом`,
    canCommentDocument: t`Коментування документів`,
    canUploadDocument: t`Завантаження документів у сервіс`,
    canDownloadDocument: t`Вивантаження документів на локальний комп’ютер`,
    canPrintDocument: t`Друк документів`,
    canDeleteDocument: t`Видалення документів`,
    canSignAndRejectDocument: t`Підписання та відхилення документів`,
    canRemoveItselfFromApproval: t`Видалення себе зі списку погоджувачів`,
    canEditCompany: t`Керування загальними налаштуваннями компанії`,
    canEditRoles: t`Редагування та видалення співробітників`,
    canInviteCoworkers: t`Запрошення співробітників`,
    canEditDocumentTemplates: t`Налаштування сценаріїв документів`,
    canEditDocumentFields: t`Налаштування додаткових параметрів документів`,
    canEditTemplates: t`Створення та редагування шаблонів`,
    canArchiveDocuments: t`Переміщення документів у архів`,
    canDeleteArchivedDocuments: t`Видалення архівних документів`,
    canCreateTags: t`Створення нових ярликів`,
    canEditDirectories: t`Керування папками`,
    canEditRequiredFields: t`Налаштування обовʼязкових полів для вхідних документів`,
    canDownloadActions: t`Збереження історії документів/дій користувачів`,
    canEditCompanyContact: t`Керування контактами контрагентів`,
    canEditSecurity: t`Зміна налаштувань безпеки`,
    canChangeDocumentSignersAndReviewers: t`Зміна розпочатого процесу підписання/погодження`,
    canDeleteDocumentExtended: t`Видалення будь-якого документу компанії`,
    canEditDocumentCategory: t`Налаштування типів внутрішніх документів`,
    canViewCoworkers: t`Доступ до перегляду співробітників`,
};

export const permissionsToHint: Partial<
    Record<CommonRolePermissions, string>
> = {
    canViewDocument: t`Можливість переглядати всі документи по компанії, окрім документів із приватним доступом.`,
    canViewPrivateDocument: t`Можливість переглядати всі документи в компанії, що помічені як приватні. Дане налаштування може змінювати лише співробітник із роллю Адміністратор.`,
    canCommentDocument: t`Можливість додавати внутрішні та зовнішні коментарі до документів.`,
    canUploadDocument: t`Можливість завантажувати документи у сервіс.`,
    canDownloadDocument: t`Вивантаження оригіналів документів із Вчасно на ваш пристрій.`,
    canPrintDocument: t`Друк документів та вивантаження pdf візуалізацій із графічними позначками підписання/погодження.`,
    canDeleteDocument: t`Можливість ініціювати та підтверджувати запити на видалення документів, в котрих співробітник приймає участь або до котрих має доступ. Не розповсюджується на документи у розділі Архів.`,
    canSignAndRejectDocument: t`Можливість підписувати документи за допомогою КЕП та/або відхилення підписання.`,
    canRemoveItselfFromApproval: t`Можливість прибрати себе зі списку погоджувачів документа або замінити на іншого співробітника в погодженні.`,
    canEditDocumentTemplates: t`Можливість створювати/редагувати та видаляти сценарії для документів.`,
    canEditDocumentFields: t`Можливість створення/редагування функціональності додаткових параметрів до документів`,
    canEditRequiredFields: t`Можливість активації та налаштування функціональності обовʼязкових полів для вхідних документів.`,
    canEditTemplates: t`Дозвіл на створення, редагування та видалення шаблонів компанії для створення документів всередині сервісу.`,
    canArchiveDocuments: t`Дозвіл на переміщення документів із зовнішніх/внутрішніх документів у розділ Архів`,
    canEditDirectories: t`Можливість створення, редагування та переміщення папок, а також документів у папках всередині розділу Архів`,
    canDeleteArchivedDocuments: t`Можливість створювати та підтверджувати запити на видалення документів у розділі Архів`,
    canCreateTags: t`Дозвіл на створення ярликів в компанії. Створені ярлики будуть доступні всім співробітникам компанії.`,
    canInviteCoworkers: t`Дозвіл на запрошення співробітників у компанію. Співробітник зможе встановити запрошеному колезі такі ж або ж менші права, як і має сам.`,
    canEditRoles: t`Можливість змінювати налаштування іншим співробітникам компанії. Якщо співробітник має такий дозвіл, але не є адміністратором, то він не зможе змінювати налаштування іншим співробітникам із роллю Адміністратор.`,
    canEditCompany: t`Можливість змінювати загальні налаштування компанії, окрім вивантаження історії дій`,
    canEditCompanyContact: t`Дозвіл на видалення, масове запрошення контрагентів та додавання ярликів у розділі "Контакти контрагентів"`,
    canDownloadActions: t`Можливість вивантаження історії дій з документами та історії дій співробітників. Якщо у співробітника немає дозволу на перегляд всіх документів, то і вивантаження історії дій недоступне.`,
    canEditSecurity: t`Можливість встановлювати безпекові налаштування для реєстрації та авторизації користувачів у сервіс Вчасно.`,
    canChangeDocumentSignersAndReviewers: t`Можливість корегувати учасників розпочатого процесу підписання або погодження. Змінити можна лише учасників, від котрих ще очікується дія.`,
    canDeleteDocumentExtended: t`Можливість відправити/підтвердити запит на видалення будь-якого документу, що доступний співробітнику. Не розповсюджується на документи в Архіві.`,
    canEditDocumentCategory: t`Створення, редагування та видалення індивідуальних типів внутрішніх документів`,
    canViewCoworkers: t`Право перегляду переліку співробітників та їх контактів в розділі налаштувань`,
};

export const roleToLabel = {
    [UserRole.ADMIN]: t`Адміністратор компанії`,
};

export const roleToHint = {
    [UserRole.ADMIN]: t`Роль із повними правами доступів у сервісі Вчасно.`,
};

export const masterPermissionsToLabel: Record<
    keyof MasterAdminPermissions,
    string
> = {
    canViewClientData: t`Пошук інформації про клієнтів`,
    canEditClientData: t`Редагування налаштувань клієнтів`,
    canEditSpecialFeatures: t`Доступ до спеціальних можливостей`,
};
