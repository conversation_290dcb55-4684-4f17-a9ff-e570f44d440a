import {
    AdministrationRolePermissions,
    DocumentRolePermissions,
    FunctionalityRolePermissions,
} from '../../types/user';

export const roleDocumentPermissionList: ReadonlyArray<
    keyof DocumentRolePermissions
> = [
    'canViewDocument',
    'canViewPrivateDocument',
    'canCommentDocument',
    'canUploadDocument',
    'canDownloadDocument',
    'canPrintDocument',
    'canDeleteDocument',
    'canSignAndRejectDocument',
    'canRemoveItselfFromApproval',
];

export const roleFunctionalityPermissionList: ReadonlyArray<
    keyof FunctionalityRolePermissions
> = [
    'canEditDocumentTemplates',
    'canEditDocumentFields',
    'canEditRequiredFields',
    'canEditTemplates',
    'canArchiveDocuments',
    'canEditDirectories',
    'canDeleteArchivedDocuments',
    'canCreateTags',
    'canEditDocumentCategory',
    'canViewCoworkers',
];

export const roleAdminPermissionList: ReadonlyArray<
    keyof AdministrationRolePermissions
> = [
    'canInviteCoworkers',
    'canEditRoles',
    'canEditCompany',
    'canEditCompanyContact',
    'canDownloadActions',
    'canEditSecurity',
    'canChangeDocumentSignersAndReviewers',
    'canDeleteDocumentExtended',
];
