import React from 'react';
import { useSelector } from 'react-redux';

import { Checkbox, snackbarToast } from '@vchasno/ui-kit';

import { isVchasnoCompany } from 'lib/helpers';
import {
    getCurrentUserRole,
    getIsCurrentRoleFromVchasnoCompany,
} from 'selectors/app.selectors';
import { UserRole } from 'services/enums';
import { t } from 'ttag';

import { CommonRolePermissions, IRole } from '../../types/user';
import { EmployeeSectionsWithCheckboxes } from '../employee/types';

import { getRoleAccessPermissionWarning } from './utils';

import CheckboxHintWrapper from './CheckboxHintWrapper';
import GridLayout from './GridLayout';
import PermissionGroup from './PermissionGroup';
import {
    roleAdminPermissionList,
    roleDocumentPermissionList,
    roleFunctionalityPermissionList,
} from './lists';
import {
    masterPermissionsToLabel,
    permissionsToApiPayloadKey,
    permissionsToHint,
    permissionsToLabel,
    roleToHint,
    roleToLabel,
} from './mappers';
import { usePermissionDisplayFilter } from './usePermissionDisplayFilter';
import { useUpdateGlobalAdminPermissions } from './useUpdateGlobalAdminPermissions';

interface RoleAccessListProps {
    currentEmployeeRole: IRole;
    currentEmployeeEdrpou: string;
    isEditable: boolean;
    onUserRoleUpdate: (
        id: string,
        accessData:
            | Record<string, boolean>
            | {
                  user_role: typeof UserRole.ADMIN | typeof UserRole.USER;
              },
        newEditedSection: string,
    ) => void;
    isAdminSettingsAccessible: boolean;
    isShowGlobalAdminPermissionsSettings?: boolean;
}

const RoleAccessList: React.FC<RoleAccessListProps> = ({
    currentEmployeeRole,
    currentEmployeeEdrpou,
    isEditable,
    onUserRoleUpdate,
    isAdminSettingsAccessible,
    isShowGlobalAdminPermissionsSettings = false,
}) => {
    const permissionDisplayFilter = usePermissionDisplayFilter();
    const currentRole = useSelector(getCurrentUserRole);
    const isCurrentRoleFromVchasnoCompany = useSelector(
        getIsCurrentRoleFromVchasnoCompany,
    );
    const isEmployeeFromVchasnoCompany = isVchasnoCompany(
        currentEmployeeEdrpou,
    );
    const isShowGlobalAdminPermissionsBlock =
        isShowGlobalAdminPermissionsSettings &&
        isCurrentRoleFromVchasnoCompany &&
        isEmployeeFromVchasnoCompany &&
        (currentRole.canViewClientData || isAdminSettingsAccessible);
    const isCanUserEditGlobalAdminPermissions =
        currentRole.canEditClientData || isAdminSettingsAccessible;

    const [
        { canViewClientData, canEditClientData, canEditSpecialFeatures },
        onUpdateGlobalAdminPermission,
    ] = useUpdateGlobalAdminPermissions({
        canViewClientData: currentEmployeeRole.canViewClientData,
        canEditClientData: currentEmployeeRole.canEditClientData,
        canEditSpecialFeatures: currentEmployeeRole.canEditSpecialFeatures,
    });

    const isAdmin = String(currentEmployeeRole.userRole) === UserRole.ADMIN;

    const onChangeCanViewClientData = () => {
        const nextViewState = !canViewClientData;

        // Turn off canEditClientData if canViewClientData is false
        const nextEditState = nextViewState && canEditClientData;

        // Update canEditClientData only if there's a change
        if (nextEditState !== canEditClientData) {
            onUpdateGlobalAdminPermission(
                currentEmployeeRole.id,
                'canEditClientData',
                nextEditState,
            );
        }

        onUpdateGlobalAdminPermission(
            currentEmployeeRole.id,
            'canViewClientData',
            nextViewState,
        );
    };

    const onChangeCanEditClientData = () => {
        const nextEditState = !canEditClientData;

        // Turn on canViewClientData if canEditClientData is true
        const nextViewState = nextEditState || canViewClientData;

        // Update canViewClientData only if there's a change
        if (nextViewState !== canViewClientData) {
            onUpdateGlobalAdminPermission(
                currentEmployeeRole.id,
                'canViewClientData',
                nextViewState,
            );
        }

        onUpdateGlobalAdminPermission(
            currentEmployeeRole.id,
            'canEditClientData',
            nextEditState,
        );
    };

    const makeHandleChangeCommonRolePermission = (
        permission: CommonRolePermissions,
    ) => () => {
        if (getRoleAccessPermissionWarning(permission, currentEmployeeRole)) {
            snackbarToast.info(
                t`Для вивантаження історії дій з документами необхідний доступ до перегляду всіх документів із приватним і спільним доступом.`,
            );
        }
        return onUserRoleUpdate(
            currentEmployeeRole.id,
            {
                [permissionsToApiPayloadKey[permission]]: !currentEmployeeRole[
                    permission
                ],
            },
            EmployeeSectionsWithCheckboxes.ROLE_ACCESS_LIST,
        );
    };

    const renderPermissionCheckbox = (permission: CommonRolePermissions) => {
        return (
            <CheckboxHintWrapper
                key={permission}
                hint={permissionsToHint[permission]}
            >
                <Checkbox
                    label={permissionsToLabel[permission]}
                    checked={
                        isAdmin || Boolean(currentEmployeeRole[permission])
                    }
                    readOnly={isAdmin}
                    onChange={makeHandleChangeCommonRolePermission(permission)}
                    disabled={isAdmin || !isEditable}
                />
            </CheckboxHintWrapper>
        );
    };

    return (
        <GridLayout>
            <PermissionGroup title={t`Дії з документами`}>
                {roleDocumentPermissionList
                    .filter(permissionDisplayFilter)
                    .map(renderPermissionCheckbox)}
            </PermissionGroup>
            <PermissionGroup title={t`Керування функціональністю`}>
                {roleFunctionalityPermissionList
                    .filter(permissionDisplayFilter)
                    .map(renderPermissionCheckbox)}
            </PermissionGroup>
            <PermissionGroup title={t`Адміністративні права`}>
                {roleAdminPermissionList
                    .filter(permissionDisplayFilter)
                    .map(renderPermissionCheckbox)}
                {isAdminSettingsAccessible && (
                    <CheckboxHintWrapper
                        key="admin"
                        hint={roleToHint[UserRole.ADMIN]}
                    >
                        <Checkbox
                            checked={isAdmin}
                            disabled={!isEditable}
                            label={roleToLabel[UserRole.ADMIN]}
                            onChange={() => {
                                onUserRoleUpdate(
                                    currentEmployeeRole.id,
                                    {
                                        user_role: isAdmin
                                            ? UserRole.USER
                                            : UserRole.ADMIN,
                                    },
                                    EmployeeSectionsWithCheckboxes.ROLE_ACCESS_LIST,
                                );
                            }}
                        />
                    </CheckboxHintWrapper>
                )}
            </PermissionGroup>
            <PermissionGroup
                hide={!isShowGlobalAdminPermissionsBlock}
                title={t`Спеціальні права Вчасно`}
            >
                <Checkbox
                    key="canViewClientData"
                    checked={canViewClientData}
                    label={masterPermissionsToLabel['canViewClientData']}
                    onChange={onChangeCanViewClientData}
                    disabled={!isCanUserEditGlobalAdminPermissions}
                />
                <Checkbox
                    key="canEditClientData"
                    checked={canEditClientData}
                    label={masterPermissionsToLabel['canEditClientData']}
                    onChange={onChangeCanEditClientData}
                />
                <Checkbox
                    key="canEditSpecialFeatures"
                    checked={canEditSpecialFeatures}
                    label={masterPermissionsToLabel['canEditSpecialFeatures']}
                    onChange={() => {
                        onUpdateGlobalAdminPermission(
                            currentEmployeeRole.id,
                            'canEditSpecialFeatures',
                            !canEditSpecialFeatures,
                        );
                    }}
                />
            </PermissionGroup>
        </GridLayout>
    );
};

export default RoleAccessList;
