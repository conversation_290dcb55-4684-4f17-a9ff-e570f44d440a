import React from 'react';
import { useSelector } from 'react-redux';

import { Checkbox, snackbarToast } from '@vchasno/ui-kit';

import CheckboxHintWrapper from 'components/RoleAccessList/CheckboxHintWrapper';
import GridLayout from 'components/RoleAccessList/GridLayout';
import PermissionGroup from 'components/RoleAccessList/PermissionGroup';
import {
    roleAdminPermissionList,
    roleDocumentPermissionList,
    roleFunctionalityPermissionList,
} from 'components/RoleAccessList/lists';
import {
    permissionsToHint,
    permissionsToLabel,
    roleToHint,
    roleToLabel,
} from 'components/RoleAccessList/mappers';
import { usePermissionDisplayFilter } from 'components/RoleAccessList/usePermissionDisplayFilter';
import { usePermissionHasCurrentUserRoleFilter } from 'components/RoleAccessList/usePermissionHasCurrentUserRoleFilter';
import { isAdminSelector } from 'selectors/app.selectors';
import { UserRole } from 'services/enums';
import { t } from 'ttag';

import {
    CommonRolePermissions,
    DefaultRolePermissionsConfig,
} from '../../types/user';

import {
    getPermissionWarning,
    getSnakeCasePermission,
    hasCheckedPermission,
} from './utils';

// styles
import css from './companyRolePermission.css';

interface CompanyRolePermissionProps {
    disabled: boolean;
    permissions: DefaultRolePermissionsConfig;
    onChangePermissions?: (
        permissions: Partial<DefaultRolePermissionsConfig>,
    ) => void;
}

const CompanyRolePermission = ({
    disabled,
    permissions,
    onChangePermissions,
}: CompanyRolePermissionProps) => {
    const isAdmin = useSelector(isAdminSelector);
    const permissionDisplayFilter = usePermissionDisplayFilter();
    const permissionHasCurrentRoleFilter = usePermissionHasCurrentUserRoleFilter();

    const onChangePermissionHandler = (
        permission: CommonRolePermissions,
    ): void => {
        if (getPermissionWarning(permission, permissions)) {
            snackbarToast.info(
                t`Для вивантаження історії дій з документами необхідний доступ до перегляду всіх документів із приватним і спільним доступом.`,
            );
        }

        return onChangePermissions?.({
            user_role: permissions.user_role,
            [getSnakeCasePermission(permission)]: !hasCheckedPermission(
                permission,
                permissions,
            ),
        });
    };

    const renderPermissionCheckbox = (permission: CommonRolePermissions) => {
        // у компанії ключі прав ролі приходять з бекенду без мапінгу у снек-кейсі

        const checked = hasCheckedPermission(permission, permissions);

        const canCurrenRoleSetPermission = permissionHasCurrentRoleFilter(
            // можна додавати чи прибирати права якщо користувач має таке право, принцип не можна назначити більше прав ніж роль
            permission,
        );

        return (
            <CheckboxHintWrapper
                key={permission}
                hint={permissionsToHint[permission]}
            >
                <Checkbox
                    label={permissionsToLabel[permission]}
                    checked={Boolean(checked)}
                    onChange={() => onChangePermissionHandler(permission)}
                    disabled={disabled || !canCurrenRoleSetPermission}
                />
            </CheckboxHintWrapper>
        );
    };

    return (
        <>
            <div className={css.paragraph}>
                {t`Нові співробітники, що зареєструвалися з КЕП, будуть мати такі права доступу:`}
            </div>
            <GridLayout>
                <PermissionGroup title={t`Дії з документами`}>
                    {roleDocumentPermissionList
                        .filter(permissionDisplayFilter)
                        .map(renderPermissionCheckbox)}
                </PermissionGroup>
                <PermissionGroup title={t`Керування функціональністю`}>
                    {roleFunctionalityPermissionList
                        .filter(permissionDisplayFilter)
                        .map(renderPermissionCheckbox)}
                </PermissionGroup>
                <PermissionGroup title={t`Адміністративні права`}>
                    {roleAdminPermissionList
                        .filter(permissionDisplayFilter)
                        .map(renderPermissionCheckbox)}
                    <CheckboxHintWrapper
                        key="admin"
                        hint={roleToHint[UserRole.ADMIN]}
                    >
                        <Checkbox
                            disabled={disabled || !isAdmin}
                            checked={
                                String(permissions.user_role) === UserRole.ADMIN
                            }
                            label={roleToLabel[UserRole.ADMIN]}
                            onChange={(event) =>
                                onChangePermissions?.({
                                    can_view_document:
                                        permissions.can_view_document,
                                    user_role: event.target.checked
                                        ? Number(UserRole.ADMIN)
                                        : Number(UserRole.USER),
                                })
                            }
                        />
                    </CheckboxHintWrapper>
                </PermissionGroup>
            </GridLayout>
        </>
    );
};

export default CompanyRolePermission;
