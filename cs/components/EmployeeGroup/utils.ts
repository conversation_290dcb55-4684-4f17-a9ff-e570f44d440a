import { t } from 'ttag';

import { IRole } from '../../types/user';
import {
    EmployeeGroup,
    GroupMember,
    MembersListsForUpdate,
    UpdatedGroupMember,
} from './types';

export const getFormattedGroupsList = (groupsList = []): EmployeeGroup[] =>
    groupsList.map(({ id, name, members = [] }) => ({
        id,
        name,
        membersCount: members.length,
    }));

export const getGroupMembersRoleIDsList = (groupMembers: GroupMember[]) =>
    groupMembers.map(({ roleId }) => roleId);

export const removeDuplicatesFromArray = <T, U>(
    targetArray: T[],
    referenceArray: U[],
    targetArrayComparisonField = 'id',
    referenceArrayComparisonField = 'id',
): T[] => {
    const referenceArrayIds = new Set(
        referenceArray.map((obj) => obj[referenceArrayComparisonField]),
    );
    return targetArray.filter(
        (obj) => !referenceArrayIds.has(obj[targetArrayComparisonField]),
    );
};

export const formatGroupMembersDataFromDB = (members = []): GroupMember[] =>
    members.map((member: { id: string; role: IRole }) => {
        const { id: roleId, isAdmin, user } = member.role;
        return {
            memberId: member.id,
            roleId,
            isAdmin,
            user,
        };
    });

export const formatGroupMemberDataFromSuggestions = (
    member: IRole,
): GroupMember => {
    const { id: roleId, isAdmin, user } = member;
    return {
        roleId,
        isAdmin,
        user,
    };
};

export const formatUpdatedGroupMembersData = (
    members: GroupMember[],
): Record<GroupMember['roleId'], UpdatedGroupMember> =>
    members.reduce((result, member) => {
        result[member.roleId] = {
            memberId: member.memberId,
            updateAction: null,
        };

        return result;
    }, {});

export const formatGroupMembersCount = (count) => {
    const lastDigitOfCount = count % 10;
    if (lastDigitOfCount === 1) {
        return t`${count} співробітник`;
    } else if (
        lastDigitOfCount === 2 ||
        lastDigitOfCount === 3 ||
        lastDigitOfCount === 4
    ) {
        return t`${count} співробітника`;
    } else {
        return t`${count} співробітників`;
    }
};

export const getMembersListsForUpdate = (
    updatedGroupMembers: Record<GroupMember['roleId'], UpdatedGroupMember>,
) => {
    return Object.entries(updatedGroupMembers).reduce(
        (result, [roleId, memberInfo]): MembersListsForUpdate => {
            if (memberInfo?.memberId && memberInfo.updateAction === 'needAdd') {
                return result;
            } else if (memberInfo.updateAction === 'needAdd') {
                result.membersToAdd.push(roleId);
                return result;
            } else if (
                memberInfo?.memberId &&
                memberInfo.updateAction === 'needDelete'
            ) {
                result.membersToDelete.push(memberInfo.memberId);
                return result;
            }
            return result;
        },
        { membersToAdd: [], membersToDelete: [] } as MembersListsForUpdate,
    );
};
