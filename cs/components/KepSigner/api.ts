import Cookies from 'js-cookie';
import {
    decorateRequestInitWithSignSessionCredentials,
    getSignSessionId,
} from 'services/sign-session';

import { Nullable } from '../../types/general';
import {
    AcquireSignResponsePayload,
    CloudCertificateAPIResponse,
    CloudCertificateResponsePayload,
    GetSignatureSerialNumberResponse,
    MultiSignStatusResponsePayload,
    SignStatusResponsePayload,
    Signatures,
    StatusCodeResponsePayload,
} from './types';

import io from '../../services/io';
import { CSIGNER_URL } from './constants';
import { encryptedFetch } from './encryption';

const UNHANDLED_ERROR_MESSAGE = `
    Невідома помилка серверу. Спробуйте ще раз, чи зверніться до служби
    підтримки, якщо помилка буде повторюватися`;

// "OLD" suffix to differentiate from new sign flow
const ORIGINATOR_DESCRIPTION = 'Вчасно.ЕДО.OLD';

export type ApiErrorData = {
    reason: string;
    errors: Record<string, string>;
};

function prepareErrorMessage(data: ApiErrorData) {
    const messages: string[] = [];
    if (!data) {
        return UNHANDLED_ERROR_MESSAGE;
    }
    if (data.reason) {
        messages.push(data.reason);
    }
    if (data.errors) {
        for (const [key, value] of Object.entries(
            data.errors as Record<string, string>,
        )) {
            messages.push(`\n${key}: ${value}`);
        }
    }
    return messages.join('; ');
}

async function prepareError(response: Response): Promise<Error> {
    let data = null;
    try {
        data = await response.json();
    } catch (err) {
        // pass
    }
    return new Error(prepareErrorMessage(data));
}

export const encryptedPost = async <ReturnType = Record<string, unknown>>(
    url: string,
    data: Record<string, unknown>,
    corsMode = false,
): Promise<ReturnType> => {
    const body = JSON.stringify(data);
    const init: RequestInit = {
        method: 'POST',
        body,
        ...(corsMode
            ? {
                  mode: 'cors',
                  credentials: 'include',
              }
            : {
                  headers: {
                      'X-XSRF-Token': Cookies.get('xsrf') ?? '',
                  },
                  credentials: 'same-origin',
              }),
    };

    decorateRequestInitWithSignSessionCredentials(init);

    let response: Response;
    try {
        response = await encryptedFetch(url, init);
    } catch (err) {
        throw navigator.onLine
            ? err
            : new Error('Проблема з підключенням до Інтернету');
    }

    if (!response.ok) {
        throw await prepareError(response);
    }

    return await response.json();
};

const fetchCloudCertificatesData = async <
    ReturnType = Record<string, unknown>
>(): Promise<ReturnType> =>
    await io.getAsJson(`/internal-api/kep/certificates`);

export const getCloudCertificatesInternalApi = async () => {
    try {
        const data = await fetchCloudCertificatesData<CloudCertificateAPIResponse>();

        return {
            certs: data.certificates,
            isMobileKepLogged: data.is_mobile_logged,
        };
    } catch (_error) {}
};

const KEP_API_PROXY = `/internal-api/proxy/kep?address=${config.KEP_HOST}`;

export const getCloudCertificates = async (
    email: string,
    edrpou: Nullable<string> = null,
) => {
    // session not contain vchasno_auth cookie, send request with edo proxy
    const url = getSignSessionId() ? KEP_API_PROXY : config.KEP_HOST;

    const data = await encryptedPost<CloudCertificateResponsePayload>(
        `${url}/api/private-encrypted/user/cloud-certificates`,
        {
            email,
            ...(edrpou ? { edrpou } : {}),
        },
        true,
    );
    return {
        certs: data.certificates,
        isMobileKepLogged: data.is_mobile_logged,
    };
};

export const releaseOperationId = async (
    clientId: string,
    operationId: string,
) =>
    encryptedPost<StatusCodeResponsePayload>(
        `${CSIGNER_URL}/ss/release-operation-id`,
        {
            clientId,
            operationId,
        },
    );

export const startSign = (
    clientId: string,
    clientId2: Nullable<string>,
    email: string,
    description: string,
    hash: string,
) => {
    const ids = clientId2 === null ? { clientId } : { clientId, clientId2 };
    return encryptedPost<AcquireSignResponsePayload>(
        `${CSIGNER_URL}/ss/acquire-sign`,
        {
            ...ids,
            email,
            originatorDescription: ORIGINATOR_DESCRIPTION,
            operationDescription: description,
            hash,
        },
    );
};

interface SignDocumentsPayload {
    clientId: string;
    clientId2?: string;
    email: string;
    hashes: string[];
    operationDescription?: string;
    operationDescriptions: string[];
    originatorDescription?: string;
}

export const startMultiSign = async ({
    clientId,
    clientId2,
    email,
    operationDescriptions,
    hashes,
    operationDescription,
    originatorDescription = ORIGINATOR_DESCRIPTION,
}: SignDocumentsPayload) =>
    encryptedPost<AcquireSignResponsePayload>(
        `${CSIGNER_URL}/ss/acquire-sign`,
        {
            ...(clientId2 === null ? { clientId } : { clientId, clientId2 }),
            email,
            originatorDescription,
            hashes,
            operationDescription,
            operationDescriptions,
        },
    );

export const getSignResult = (clientId: string, operationId: string) => {
    return encryptedPost<SignStatusResponsePayload>(
        `${CSIGNER_URL}/ss/sign-status`,
        {
            clientId,
            operationId,
        },
    );
};

// remove after remove ENABLE_KEP_SUPER_MASS_SIGN flag
export const getMultiSignResultsOld = (clientId: string, operationId: string) =>
    encryptedPost<MultiSignStatusResponsePayload>(
        `${CSIGNER_URL}/ss/sign-status`,
        {
            clientId,
            operationId,
        },
    );

export const getMultiSignResults = (
    clientId: string,
    operationId: string,
    pageNum?: number,
    pageSize?: number,
) =>
    encryptedPost<MultiSignStatusResponsePayload>(
        `${CSIGNER_URL}/ss/sign-status`,
        {
            clientId,
            operationId,
            pageNum,
            pageSize,
        },
    );

export const resendSms = (
    clientId: string,
    operationId: string,
): Promise<Record<string, unknown>> => {
    return encryptedPost(`${CSIGNER_URL}/ss/resend-sign-sms`, {
        clientId,
        operationId,
    });
};

type GetSignatureSerialNumberType = (
    payload: Signatures,
) => Promise<GetSignatureSerialNumberResponse>;

export const getSignatureSerialNumber: GetSignatureSerialNumberType = (
    payload,
) => {
    return io.post('/internal-api/signatures/get-serial-number', payload, true);
};

export const updateKepAppPopupDate = () => {
    return io.post('/internal-api/notifications/popups', {
        update_kep_popup_date: true,
    });
};
