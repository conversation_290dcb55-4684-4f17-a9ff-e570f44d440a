import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { openInNewTab } from 'lib/navigation';
import { Rate } from 'records/types';
import { getMostExpensiveWebRate } from 'selectors/app.selectors';
import { getRecommendedWebRate } from 'selectors/checkout.selectors';
import { getIsShowTerminateEmployeesPopup } from 'selectors/companyEmployees.selectors';
import { ACCOUNT_RATE_TITLE_MAP } from 'services/billing';
import { AccountRate } from 'services/enums';
import { jt, t } from 'ttag';

import { getRecommendedWebRateActionCreator } from '../Checkout/checkoutActionCreators';
import actions from '../companyEmployees/companyEmployeesActions';

import { getLinkForPayment } from '../ActiveRates/helpers';
import { sendPopupButtonClickAnalytics } from '../RatesPopup/helpers';

import Button from '../ui/button/button';
import Icon from '../ui/icon/icon';

import RatesPopup from '../RatesPopup/RatesPopup';

import CardsSvg from './images/cards.svg';

import css from '../RatesPopup/RatesPopup.css';

const TerminateEmployeesPopup: React.FC = () => {
    const dispatch = useDispatch();
    const mostExpensiveActiveWebRate: Rate = useSelector(
        getMostExpensiveWebRate,
    );
    const recommendedWebRateForPayment = useSelector(getRecommendedWebRate);
    const isShowTerminateEmployeesPopup = useSelector(
        getIsShowTerminateEmployeesPopup,
    );

    useEffect(() => {
        if (!isShowTerminateEmployeesPopup) {
            return;
        }
        dispatch(getRecommendedWebRateActionCreator());
    }, [isShowTerminateEmployeesPopup]);

    const url = useMemo(
        () =>
            recommendedWebRateForPayment && mostExpensiveActiveWebRate
                ? getLinkForPayment(
                      mostExpensiveActiveWebRate.rate,
                      recommendedWebRateForPayment,
                  )
                : '/app/checkout-rates',
        [mostExpensiveActiveWebRate, recommendedWebRateForPayment],
    );

    if (!isShowTerminateEmployeesPopup) {
        return null;
    }

    const onClose = () => {
        dispatch({
            type:
                actions.COMPANY_EMPLOYEES__SET_IS_SHOW_TERMINATE_EMPLOYEES_POPUP,
            isShowTerminateEmployeesPopup: false,
        });
    };

    const onClick = () => {
        sendPopupButtonClickAnalytics();
        openInNewTab(url);
    };

    const rate =
        ACCOUNT_RATE_TITLE_MAP[
            mostExpensiveActiveWebRate?.rate as AccountRate.OLD
        ];
    const rateName = <b>{rate}</b>;

    return (
        <RatesPopup
            onClose={onClose}
            active={isShowTerminateEmployeesPopup}
            popupAnalyticsLabel={t`Кількість співробітників`}
        >
            <div className={css.icon}>
                <Icon glyph={CardsSvg} />
            </div>
            <h5 className={css.title}>
                {t`Ви досягли максимальної кількості співробітників на тарифі.`}
            </h5>
            <p className={css.text}>
                {jt`Вичерпалася кількість співробітників, яких можна додавати у сервіс, на тарифі ${rateName}.`}
            </p>
            <div className={css.buttons}>
                <Button width="full" theme="cta" onClick={onClick}>
                    {t`Купити тариф`}
                </Button>
            </div>
        </RatesPopup>
    );
};

export default TerminateEmployeesPopup;
