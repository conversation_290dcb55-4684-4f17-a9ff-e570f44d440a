import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
    getCanCloseSuccessVerifiedUser,
    getDisplayStatus,
} from '../../../selectors/successVerifiedUser.selectors';
import successVerifiedUserActionCreators from '../successVerifiedUserActionCreators';

export const useDisplaySuccessVerifiedUser = () => {
    const dispatch = useDispatch();

    const displayStatusFromStore = useSelector(getDisplayStatus);
    const canCloseSuccessVerifiedUser = useSelector(
        getCanCloseSuccessVerifiedUser,
    );

    React.useEffect(() => {
        dispatch(successVerifiedUserActionCreators.onMount());
    }, []);

    return !canCloseSuccessVerifiedUser || displayStatusFromStore === 'visible';
};
