import React from 'react';
import { useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';
import { Redirect, Route, Switch, useRouteMatch } from 'react-router-dom';

import { getLocation } from 'connected-react-router';
import { MEDIA_WIDTH } from 'lib/constants';
import {
    getCurrentCompany,
    getCurrentUserRoleId,
} from 'selectors/app.selectors';
import { getCompanyEmployeesCountByStatus } from 'selectors/companyEmployees.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { RoleStatuses } from 'services/enums';
import { mapStatetoHasPermission } from 'store/utils';
import { c, t } from 'ttag';

import { StoreState } from '../../types/store';

import Breadcrumbs from '../ui/breadcrumbs/breadcrumbs';
import Title from '../ui/title/title';

import NavTabs from '../Tabs/Tabs';
import PageTitle from '../pageTitle/pageTitle';
import CompanyEmployeesSettings from './components/CompanyEmployeesSettings/CompanyEmployeesSettings';
import CompanyMainSettings from './components/CompanyMainSettings/CompanyMainSettings';
import CompanyRatesSettings from './components/CompanyRatesSettings/CompanyRatesSettings';
import CompanySecuritySettings from './components/CompanySecuritySettings';
import CompanySettingsMenu from './components/CompanySettingsMenu/CompanySettingsMenu';
import { TABS_URLS } from './constants';

import css from './companyCard.css';

const CompanyCard: React.FC<React.PropsWithChildren<unknown>> = ({
    children,
}) => {
    const { hasPermission } = useSelector(mapStatetoHasPermission);
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.mobile });
    const activeEmployeesCount = useSelector((state: StoreState) =>
        getCompanyEmployeesCountByStatus(state, RoleStatuses.ACTIVE),
    );

    const currentCompany = useSelector(getCurrentCompany);
    const currentRoleId = useSelector(getCurrentUserRoleId);

    const TABS = [
        {
            title: t`Тарифи`,
            link: TABS_URLS.rates,
            analytic: () => {
                eventTracking.sendToGTM({
                    event: 'tariffs_click',
                    category: 'company_settings',
                });
            },
        },
        { title: t`Налаштування`, link: TABS_URLS.main },
        {
            title: t`Співробітники`,
            link: TABS_URLS.employees,
            additionalData: (
                <span className={css.employeesCount}>
                    {activeEmployeesCount}
                </span>
            ),
            isHidden: !hasPermission('canViewCoworkers') || isMobile,
        },
        {
            title: t`Налаштування безпеки`,
            link: TABS_URLS.security,
            isHidden: !hasPermission('canEditSecurity'),
        },
    ];

    const companyName = currentCompany?.name || '';

    const breadcrumbs = [
        {
            link: '/app/settings',
            name: t`Налаштування`,
        },
        {
            link: '/app/settings/companies',
            name: t`Мої компанії`,
        },
        {
            link: `/app/settings/companies/${currentRoleId}`,
            name: c('company card').t`Компанія ${companyName}`,
        },
    ];

    const location = useSelector(getLocation);
    const { path, url } = useRouteMatch();

    return (
        <div>
            {!children && (
                <div>
                    <PageTitle>{c('company card title')
                        .t`Компанія ${companyName}`}</PageTitle>
                    <Breadcrumbs items={breadcrumbs} />
                    <Title>{t`Налаштування компанії`}</Title>
                    <CompanySettingsMenu company={currentCompany} />
                    <NavTabs
                        tabs={TABS}
                        baseLink={url}
                        containerClassName={css.tabs}
                    />
                    <div className={css.cardsItems}>
                        <Switch>
                            <Route
                                exact
                                path={`${path}/${TABS_URLS.rates}`}
                                component={CompanyRatesSettings}
                            />
                            <Route
                                exact
                                path={`${path}/${TABS_URLS.main}`}
                                component={CompanyMainSettings}
                            />
                            <Route
                                exact
                                path={`${path}/${TABS_URLS.employees}`}
                            >
                                <CompanyEmployeesSettings
                                    company={currentCompany}
                                />
                            </Route>
                            <Route
                                exact
                                path={`${path}/${TABS_URLS.security}`}
                                component={() => <CompanySecuritySettings />}
                            />
                            <Redirect
                                to={`${path}/${TABS_URLS.main}${location?.search}`}
                            />
                        </Switch>
                    </div>
                </div>
            )}
            <div>{children}</div>
        </div>
    );
};

export default CompanyCard;
