import React from 'react';
import { useDispatch } from 'react-redux';

import ChangeDocumentButton from 'components/changeDocumentButton/changeDocumentButton';
import documentActionCreator from 'components/document/documentActionCreators';
import HideForEDI from 'components/hideForEDI/hideForEDI';
import { WithDocumentProcessesRenderer } from 'contexts/documentProcesses';
import eventTracking from 'services/analytics/eventTracking';

const DocumentEditSettingsIcon = () => {
    const dispatch = useDispatch();

    return (
        <HideForEDI>
            <WithDocumentProcessesRenderer>
                {({ isNewUpload }) => (
                    <ChangeDocumentButton
                        onOpenChangeDocumentPopup={() =>
                            dispatch(
                                documentActionCreator.onOpenChangeDocumentPopup(
                                    { selectedItemIndex: [] },
                                ),
                            )
                        }
                        onClick={
                            isNewUpload
                                ? () => {
                                      eventTracking.sendToGTM({
                                          event: 'new_settings_click',
                                          category: 'doc_page',
                                      });
                                  }
                                : undefined
                        }
                    />
                )}
            </WithDocumentProcessesRenderer>
        </HideForEDI>
    );
};

export default DocumentEditSettingsIcon;
