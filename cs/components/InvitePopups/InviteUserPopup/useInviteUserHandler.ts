import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

import {
    InviteUserFormValues,
    inviteUserFormResolver,
} from 'components/InvitePopups/InviteUserPopup/validation';
import appActionCreators from 'components/app/appActionCreators';
import actions from 'components/companyEmployees/companyEmployeesActionCreators';
import notificationCenterActionCreators from 'components/notificationCenter/notificationCenterActionCreators';
import omit from 'lodash/omit';
import {
    getAfterBillAddEmployees,
    getCurrentCompany,
    getCurrentCompanyId,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import auth from 'services/auth';
import { UserRole } from 'services/enums';
import { getCompany } from 'services/user';
import { t } from 'ttag';

import appActions from '../../app/appActions';

import { getNameActiveRateHasFreeStartProRates } from './helper';

export const useInviteUserHandler = () => {
    const dispatch = useDispatch();
    const currentCompany = useSelector(getCurrentCompany);
    const currentCompanyEdrpou = currentCompany?.edrpou;
    const currentCompanyId = useSelector(getCurrentCompanyId);
    const isAfterBillAddEmployees = useSelector(getAfterBillAddEmployees);
    const {
        handleSubmit,
        control,
        setValue,
        watch,
        reset,
    } = useForm<InviteUserFormValues>({
        resolver: inviteUserFormResolver,
        defaultValues: {
            email: '',
            edrpou: '',
            isPermissionChecked: false,
            permissions: {
                ...omit(
                    currentCompany?.config?.default_role_permissions_key,
                    'user_role',
                ),
                is_admin:
                    String(
                        currentCompany?.config?.default_role_permissions_key
                            ?.user_role,
                    ) === UserRole.ADMIN,
            },
        },
    });

    const rateName = getNameActiveRateHasFreeStartProRates();

    const onClose = () => {
        dispatch({ type: appActions.APP__HIDE_INVITE_USER_POPUP });
    };

    const onSubmit = async (data: InviteUserFormValues) => {
        try {
            await auth.inviteUser({
                email: data.email,
                edrpou: currentCompanyEdrpou,

                ...(data.isPermissionChecked && {
                    permissions: data.permissions,
                }),

                ...(data.position && { position: data.position }),
            });
            eventTracking.sendToGTM({
                category: rateName,
                action: 'send_invite_colleague',
            });

            dispatch(
                notificationCenterActionCreators.addNotification({
                    type: 'text',
                    textType: 'success',
                    title: t`Запрошення надіслане`,
                    text: `Email: ${
                        data.email
                    }, ${t`ЄДРПОУ/ІПН`}: ${currentCompanyEdrpou}${
                        data.position ? `, ${t`Позиція`}: ${data.position}` : ''
                    }`,
                    showCloseButton: true,
                    autoClose: 10000,
                }),
            );

            eventTracking.sendToGTM({
                event: 'invite_coworker_success',
            });

            if (isAfterBillAddEmployees) {
                eventTracking.sendToGTM({
                    event: 'invite_coworkers_ultimate_popup_success',
                });
            }

            const company = await getCompany(currentCompanyId);
            reset();
            const roles = company.roles.toJS();

            dispatch(
                appActionCreators.updateCompanyState({
                    payload: {
                        roles: [...roles],
                    },
                }),
            );
            dispatch(actions.setEmployees(company.roles));
        } catch (error) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    title: t`Виникла помилка`,
                    type: 'text',
                    textType: 'error',
                    text: error.message,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
        }

        onClose();
    };

    return {
        onSubmit,
        onClose,
        handleSubmit,
        control,
        setValue,
        watch,
    };
};
