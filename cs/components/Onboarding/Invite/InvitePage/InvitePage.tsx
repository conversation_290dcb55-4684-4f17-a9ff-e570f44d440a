import React from 'react';
import { useDispatch } from 'react-redux';

import TemplateBlock from '../../TemplateBlock/TemplateBlock';
import { indicateVisibleOnMount } from '../../onboardingReducer';
import Invite from '../Invite';

const InvitePage: React.FC<React.PropsWithChildren<unknown>> = () => {
    const dispatch = useDispatch();

    const onMount = () => dispatch(indicateVisibleOnMount('INVITE_BANNER'));

    return (
        <TemplateBlock tag="INVITE_BANNER">
            <Invite onMount={onMount} />
        </TemplateBlock>
    );
};

export default InvitePage;
