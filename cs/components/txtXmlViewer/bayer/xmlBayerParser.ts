import { xmlQueries } from 'components/txtXmlViewer/bayer/constants';
import { textContent } from 'components/txtXmlViewer/utils';
import { parseXml } from 'services/xml';

const parseTextFloat = (floatString: string) => {
    return parseFloat(floatString.replace(/[\.\n]/gm, '').replace(/,/, '.'));
};

const numberToStyled = (number: number, fractionDigits: number) => {
    return number.toLocaleString('de-DE', {
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits,
    });
};

const numberStringToStyled = (number: string, fractionDigits: number) => {
    return parseTextFloat(number).toLocaleString('de-DE', {
        minimumFractionDigits: fractionDigits,
        maximumFractionDigits: fractionDigits,
    });
};

const buildTablesObjects = (
    table: Element,
    soldToName: string,
    licenseSoldTo: string,
    documentNumber: string,
    documentDate: string,
) => {
    const tableFirst = [];
    const tableSecond = [];
    const tableThird = [];

    const rowsTableCount = table.children.length / 16;
    let isAllRowsDiscountZero = true;

    for (let row = 0; row < rowsTableCount; row++) {
        const rowFirstTable = [];
        const rowSecondTable = [];
        const rowThirdTable = [];

        rowFirstTable.push(row + 1);
        rowSecondTable.push(row + 1);
        rowThirdTable.push(row + 1);

        const countMedicaments = numberStringToStyled(
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A9"] > VALUE`)
                ?.textContent || '',
            3,
        );
        const medicamentNumber =
            table.querySelector(
                `ROW[LINE = '${row}'][NAME = 'TAB1_A2'] > VALUE`,
            )?.textContent || '';
        const medicamentName =
            table.querySelector(
                `ROW[LINE = '${row}'][NAME = 'TAB1_A3_1'] > VALUE`,
            )?.textContent || '';
        const medicamentCompany =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A3_2"] > VALUE`)
                ?.textContent || '';
        const medicamentCountry =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A3_3"] > VALUE`)
                ?.textContent || '';
        const medicamentSerial =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A4"] > VALUE`)
                ?.textContent || '';
        const medicamentDate =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A7"] > VALUE`)
                ?.textContent || '';
        const medicamentTare =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A8"] > VALUE`)
                ?.textContent || '';
        const medicamentPriceWitdoutPDV = numberStringToStyled(
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A12"] > VALUE`)
                ?.textContent || '',
            2,
        );
        const medicamentSumPriceWitdoutPDV = numberStringToStyled(
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A13"] > VALUE`)
                ?.textContent || '',
            2,
        );
        const numberAndDate =
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A6"] > VALUE`)
                ?.textContent || '';

        const discountTable = parseTextFloat(
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A11"] > VALUE`)
                ?.textContent || '',
        );

        if (discountTable) {
            isAllRowsDiscountZero = false;
        }

        rowFirstTable.push(medicamentNumber);
        rowFirstTable.push(medicamentName);
        rowFirstTable.push(medicamentSerial);
        rowFirstTable.push(
            numberStringToStyled(
                table.querySelector(
                    `ROW[LINE="${row}"][NAME="TAB1_A5"] > VALUE`,
                )?.textContent || '',
                2,
            ),
        );
        rowFirstTable.push(countMedicaments);
        rowFirstTable.push(medicamentDate);
        rowFirstTable.push(medicamentTare);
        rowFirstTable.push(countMedicaments);
        rowFirstTable.push(
            numberStringToStyled(
                table.querySelector(
                    `ROW[LINE="${row}"][NAME="TAB1_A10"] > VALUE`,
                )?.textContent || '',
                2,
            ),
        );
        rowFirstTable.push(numberToStyled(discountTable, 2));
        rowFirstTable.push(medicamentPriceWitdoutPDV);
        rowFirstTable.push(medicamentSumPriceWitdoutPDV);
        rowFirstTable.push(medicamentCompany);
        rowFirstTable.push(medicamentCountry);
        rowFirstTable.push(
            table.querySelector(`ROW[LINE="${row}"][NAME="TAB1_A3_4"] > VALUE`)
                ?.textContent || '',
        );
        rowFirstTable.push(numberAndDate);

        rowSecondTable.push(soldToName);
        rowSecondTable.push(licenseSoldTo);
        rowSecondTable.push(documentNumber);
        rowSecondTable.push(documentDate);
        rowSecondTable.push(medicamentNumber);
        rowSecondTable.push(medicamentName);
        rowSecondTable.push(medicamentCompany);
        rowSecondTable.push(medicamentCountry);
        rowSecondTable.push(medicamentSerial);
        rowSecondTable.push(numberAndDate);
        rowSecondTable.push(countMedicaments);
        rowSecondTable.push(medicamentDate);

        rowThirdTable.push(medicamentName);
        rowThirdTable.push(medicamentSerial);
        rowThirdTable.push(medicamentDate);
        rowThirdTable.push(countMedicaments);
        rowThirdTable.push(medicamentTare);
        rowThirdTable.push(medicamentPriceWitdoutPDV);
        rowThirdTable.push(medicamentSumPriceWitdoutPDV);

        tableFirst.push(rowFirstTable);
        tableSecond.push(rowSecondTable);
        tableThird.push(rowThirdTable);
    }

    return { tableFirst, tableSecond, tableThird, isAllRowsDiscountZero };
};

export const xmlBayerParser = (content: string) => {
    const xml = parseXml(content);
    const getText = (query: string) => textContent(xml, query) as string;

    const soldToName = getText(xmlQueries.soldToName);
    const licenseSoldTo = getText(xmlQueries.licenseSoldTo);
    const documentNumber = getText(xmlQueries.documentNumber);
    const documentDate = getText(xmlQueries.documentDate);

    const {
        tableFirst,
        tableSecond,
        tableThird,
        isAllRowsDiscountZero,
    } = buildTablesObjects(
        xml.querySelector(xmlQueries.table)!,
        soldToName,
        licenseSoldTo,
        documentNumber,
        documentDate,
    );

    const totalWithoutDiscountFloat = parseTextFloat(
        getText(xmlQueries.totalWithoutDiscount),
    );
    const discountFloat = parseTextFloat(getText(xmlQueries.discount));

    const totalWithoutDiscount = isAllRowsDiscountZero
        ? numberToStyled(totalWithoutDiscountFloat - discountFloat, 2)
        : numberToStyled(totalWithoutDiscountFloat, 2);

    return {
        documentNumber: documentNumber,
        supplyOrder: getText(xmlQueries.supplyOrder) || '',
        documentDate: documentDate,
        contractNumber: getText(xmlQueries.contractNumber),
        contractDate: getText(xmlQueries.contractDate),
        paymentDate: getText(xmlQueries.paymentDate),
        salerOrderNumber: getText(xmlQueries.salerOrderNumber),
        deliveryNumber: getText(xmlQueries.deliveryNumber),
        plant: getText(xmlQueries.plant),
        licenseSupplier: getText(xmlQueries.licenseSupplier),
        licenseDate: getText(xmlQueries.licenseDate),
        licenseSoldTo: licenseSoldTo,
        documentCorrection: getText(xmlQueries.documentCorrection),
        supplierName: getText(xmlQueries.supplierName),
        supplierEDRPOU: getText(xmlQueries.supplierEDRPOU),
        supplierAddress: getText(xmlQueries.supplierAddress),
        supplierIBAN: getText(xmlQueries.supplierIBAN),
        supplierBankAddress: getText(xmlQueries.supplierBankAddress),
        supplierBankPhone: getText(xmlQueries.supplierBankPhone),
        soldToCode: getText(xmlQueries.soldToCode),
        soldToName: soldToName,
        soldToAddress: getText(xmlQueries.soldToAddress),
        soldToIPN: getText(xmlQueries.soldToIPN),
        soldToEDRPOU: getText(xmlQueries.soldToEDRPOU),
        shipToCode: getText(xmlQueries.shipToCode),
        shipToName: getText(xmlQueries.shipToName),
        shipToAddress: getText(xmlQueries.shipToAddress),
        shipToPhone: getText(xmlQueries.shipToPhone),
        totalWithoutDiscount: totalWithoutDiscount,
        discount: isAllRowsDiscountZero
            ? numberToStyled(0, 2)
            : numberStringToStyled(getText(xmlQueries.discount), 2),
        total: numberStringToStyled(getText(xmlQueries.total), 2),
        vatRate: getText(xmlQueries.vatRate),
        vat: numberStringToStyled(getText(xmlQueries.vat), 2),
        totalAmount: numberStringToStyled(getText(xmlQueries.totalAmount), 2),
        tableFirst: tableFirst,
        tableSecond: tableSecond,
        tableThird: tableThird,
    } as const;
};

export default (data: { content: string }) => xmlBayerParser(data.content);
