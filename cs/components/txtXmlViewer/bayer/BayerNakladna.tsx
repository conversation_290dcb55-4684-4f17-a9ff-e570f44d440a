import React, { FC } from 'react';
import Helmet from 'react-helmet';

import cn from 'classnames';
import { xmlBayerParser } from 'components/txtXmlViewer/bayer/xmlBayerParser';
import { XMLDocumentViewOrientation } from 'components/txtXmlViewer/types';
import { generateUATextMoneyRepresentation } from 'components/txtXmlViewer/utils';
import Icon from 'ui/icon';

import logo from './images/bayer_logo_color.svg';

import css from './BayerNakladna.css';

interface BayerNakladnaProps {
    data: ReturnType<typeof xmlBayerParser>;
}

const BayerNakladnaComponent: FC<BayerNakladnaProps> & {
    orientation: XMLDocumentViewOrientation;
} = ({ data }) => {
    return (
        <div className={css.root}>
            <Helmet>
                <style>{`@page {
                margin: 0;
                size: a4 landscape;

                @bottom-left {
                    content: "";
                }
            }`}</style>
            </Helmet>
            <section className={css.bayerSection}>
                <h1 className={css.mainTitle}>
                    НАКЛАДНА №: {data.documentNumber}
                </h1>
                <table className={css.headTable}>
                    <tbody>
                        <tr>
                            <td className={css.tableLeft}>
                                <b>Постачальник:</b>
                                <br />
                                {data.supplierName}, {data.supplierAddress}
                                <br />
                                код ЄДРПОУ {data.supplierEDRPOU}
                                <br />
                                <br />
                                Банківські реквізити постачальника:
                                <br />
                                IBAN: {data.supplierIBAN}
                                <br />
                                {data.supplierBankAddress}
                                <br />
                                {data.supplierBankPhone}
                                <br />
                                <br />
                                <b>Покупець: {data.soldToCode}</b>
                                <br />
                                {data.soldToName}, {data.soldToAddress}
                                <br />
                                ІПН {data.soldToIPN} № ЄДРПОУ{' '}
                                {data.soldToEDRPOU}
                            </td>
                            <td className={css.dateCell}>
                                Дата: {data.documentDate}
                            </td>
                            <td className={css.tableRight}>
                                <Icon glyph={logo} className={css.logo} />
                                <br />
                                {data.licenseSupplier}
                                <br />
                                Строк діі з {data.licenseDate}
                                <br />
                                <br />
                                <div className={css.licenseSold}>
                                    Ліцензія покупця № {data.licenseSoldTo}{' '}
                                    серія {data.licenseSoldTo.substring(0, 2)}{' '}
                                    на оптову торгівлю лікарськими засобами
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table className={css.headTable}>
                    <tbody>
                        <tr>
                            <td className={css.tableLeft}>
                                <b>Адреса доставки: {data.soldToCode}</b>
                                <br />
                                {data.shipToName}, {data.shipToAddress}
                                <table className={css.tableNoPadding}>
                                    <tbody>
                                        <tr>
                                            <td>Телефон</td>
                                            <td>{data.shipToPhone}</td>
                                        </tr>
                                        <tr>
                                            <td>№ договору</td>
                                            <td>
                                                {data.contractNumber} від{' '}
                                                {data.contractDate}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>Сплатити до:</td>
                                            <td>{data.paymentDate}</td>
                                        </tr>
                                        <tr>
                                            <td>Замовлення номер:</td>
                                            <td>{data.salerOrderNumber}</td>
                                        </tr>
                                        {data.supplyOrder && (
                                            <tr>
                                                <td colSpan={2}>
                                                    {data.supplyOrder}
                                                </td>
                                            </tr>
                                        )}
                                    </tbody>
                                </table>
                            </td>
                            <td>Склад: {data.plant}</td>
                        </tr>
                    </tbody>
                </table>

                <table className={css.tableOne}>
                    <thead>
                        <tr>
                            <th>№</th>
                            <th>Номенклатурний номер</th>
                            <th>
                                Номенклатура
                                <br />
                                Назва виробника, країна
                            </th>
                            <th>№ серіі (партіі)</th>
                            <th>Номер і дата сертифіката якості виробника</th>
                            <th>Кількість серії(партії)</th>
                            <th>Термін придатності</th>
                            <th>Од. вим</th>
                            <th>Кількість</th>
                            <th>Ціна за одиницю виміру товару без ПДВ, грн.</th>
                            <th>Знижка %</th>
                            <th>
                                Ціна за одиницю виміру товару з врахуванням
                                знижки, грн. *
                            </th>
                            <th>Вартість з урахуванням знижки без ПДВ, грн.</th>
                        </tr>
                    </thead>
                    <tbody>
                        {data.tableFirst.map((row) => {
                            return (
                                <tr
                                    key={row[0]}
                                    style={{
                                        pageBreakInside: 'avoid',
                                    }}
                                >
                                    <td>{row[0]}</td>
                                    <td className={css.alignСenter}>
                                        {row[1]}
                                    </td>
                                    <td>
                                        {row[2]}
                                        <br />
                                        {row[13] ? `${row[13]}, ` : ''}
                                        {row[14]}
                                        <br />
                                        Код УКТЗЕД {row[15]}
                                    </td>
                                    <td>{row[3]}</td>
                                    <td>{row[16]}</td>
                                    <td className={css.alignRight}>{row[5]}</td>
                                    <td className={css.alignRight}>{row[6]}</td>
                                    <td className={css.alignRight}>{row[7]}</td>
                                    <td className={css.alignRight}>{row[8]}</td>
                                    <td className={css.alignRight}>{row[9]}</td>
                                    <td className={css.alignRight}>
                                        {row[10]}
                                    </td>
                                    <td className={css.alignRight}>
                                        {row[11]}
                                    </td>
                                    <td className={css.alignRight}>
                                        {row[12]}
                                    </td>
                                </tr>
                            );
                        })}

                        <tr
                            className={css.rowNoBorder}
                            style={{
                                pageBreakInside: 'avoid',
                            }}
                        >
                            <td colSpan={13}>
                                <table className={css.total}>
                                    <tbody>
                                        <tr className={css.rowNoBorder}>
                                            <td colSpan={9}>
                                                * Ціна за одиницю виміру товару
                                                з врахуванням знижки, грн є для
                                                лікарських засобів
                                                Оптово-відпускною ціною без ПДВ,
                                                грн. для суб’єктів
                                                господарювання, що здійснюють
                                                оптову торгівлю лікарськими
                                                засобами
                                            </td>
                                            <td
                                                colSpan={2}
                                                className={css.alignRight}
                                                style={{
                                                    paddingRight: '15mm',
                                                    whiteSpace: 'nowrap',
                                                }}
                                            >
                                                <ul className={css.list}>
                                                    <li>РАЗОМ БЕЗ ЗНИЖКИ</li>
                                                    <li>ЗНИЖКА</li>
                                                    <li>ВСЬОГО</li>
                                                    <li>{data.vatRate}% ПДВ</li>
                                                    <li>РАЗОМ</li>
                                                </ul>
                                            </td>
                                            <td className={css.alignRight}>
                                                <ul className={css.list}>
                                                    <li>
                                                        {
                                                            data.totalWithoutDiscount
                                                        }
                                                    </li>
                                                    <li>{data.discount}</li>
                                                    <li>{data.total}</li>
                                                    <li>{data.vat}</li>
                                                    <li>{data.totalAmount}</li>
                                                </ul>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table className={css.addressees}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div
                                                    className={
                                                        css.addresseesCell
                                                    }
                                                >
                                                    Відпустив:*
                                                    <br />
                                                    {data.supplierName}
                                                    <hr />
                                                    <br />
                                                    <br />
                                                    <hr />
                                                    <div className={css.pib}>
                                                        (ПІБ, посада, підпис)
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div
                                                    className={cn(
                                                        css.addresseesCell,
                                                        css.addresseesCellTO,
                                                    )}
                                                >
                                                    Отримав:
                                                    <br />
                                                    {data.soldToName}
                                                    <hr />
                                                    <br />
                                                    <br />
                                                    <hr />
                                                    <div className={css.pib}>
                                                        (ПІБ, посада, підпис)
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <br />
                                * відповідальний за здійснення господарської
                                операції і правильність її оформлення
                                <br />
                                <br />
                                Місце складання: м.Київ
                                <br />
                                Шановні панове! Прохання при оплаті в тексті
                                призначення платежу вказувати Ваш № Покупця
                                першими цифрами. Також будемо вдячні, якщо Ви
                                вкажете № рахунку або накладної. Дякуємо за
                                співпрацю!
                                <div className={css.numbers}>
                                    {data.documentNumber}
                                    <br />
                                    {data.salerOrderNumber}
                                    <br />
                                    {data.deliveryNumber}
                                    <br />
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section className={css.bayerSection}>
                <h1 className={css.secondTitle}>
                    Реєстр
                    <br />
                    лікарських засобів, які реалізуються суб'єктом
                    господарювання
                </h1>

                <table className={css.secondHead}>
                    <tbody>
                        <tr>
                            <td>Постачальник:</td>
                            <td>
                                {data.supplierName}
                                <br />
                                {data.supplierAddress}
                                <br />
                                код ЄДРПОУ {data.supplierEDRPOU}
                                <br />
                                {data.licenseSupplier}
                            </td>
                            <td rowSpan={2}>
                                <Icon glyph={logo} className={css.logo} />
                            </td>
                        </tr>
                        <tr>
                            <td>Одержувач:</td>
                            <td>
                                {data.shipToName}
                                <br />
                                {data.shipToAddress}
                                <br />
                                ІПН {data.soldToIPN} № ЄДРПОУ{' '}
                                {data.soldToEDRPOU}
                                <br />
                                Ліцензія: {data.licenseSoldTo}
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table className={css.tableOne}>
                    <thead>
                        <tr>
                            <td>№ з/п</td>
                            <td>Найменування покупця та номер ліцензії</td>
                            <td>Номер і дата накладної</td>
                            <td>
                                Назва лікарського зособу та його лікарська форма
                            </td>
                            <td>Назва виробника</td>
                            <td>Номер серії</td>
                            <td>Номер і дата сертифіката якості виробника</td>
                            <td>Кількість відправлених упаковок</td>
                            <td>Термін придатності лікарського засобу</td>
                            <td>Результат контролю уповноваженою особою</td>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>2</td>
                            <td>3</td>
                            <td>4</td>
                            <td>5</td>
                            <td>6</td>
                            <td>7</td>
                            <td>8</td>
                            <td>9</td>
                            <td>10</td>
                        </tr>
                    </thead>

                    <tbody>
                        {data.tableSecond.map((row) => {
                            return (
                                <tr key={row[0]}>
                                    <td>{row[0]}</td>
                                    <td>
                                        {row[1]}
                                        <br />
                                        {row[2]}
                                    </td>
                                    <td>
                                        {row[3]} від {row[4]}
                                    </td>
                                    <td>
                                        {row[5]}
                                        <br />
                                        {row[6]}
                                    </td>
                                    <td>
                                        {row[7] ? `${row[7]}, ` : ''}
                                        {row[8]}
                                    </td>
                                    <td>{row[9]}</td>
                                    <td>{row[10]}</td>
                                    <td className={css.alignRight}>
                                        {row[11]}
                                    </td>
                                    <td className={css.alignRight}>
                                        {row[12]}
                                    </td>
                                    <td></td>
                                </tr>
                            );
                        })}

                        <tr
                            className={css.rowNoBorder}
                            style={{
                                pageBreakInside: 'avoid',
                            }}
                        >
                            <td colSpan={10}>
                                <div className={css.signSecond}>
                                    <div>Підпис:</div>
                                    <div>
                                        <hr />
                                    </div>
                                </div>
                                <div className={css.signSecond}>
                                    <div>Дата:</div>
                                    <div>
                                        <hr />
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section className={css.bayerSection}>
                <table>
                    <tbody>
                        <tr>
                            <td>Специфікація № {data.documentNumber}</td>
                            <td>від {data.documentDate}</td>
                            <td>
                                до Договору № {data.contractNumber} /{' '}
                                {data.contractDate}
                            </td>
                            <td></td>
                        </tr>
                        <tr>
                            <td colSpan={4}>
                                Між {data.supplierName} та {data.soldToName}
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table className={css.tableOne}>
                    <thead>
                        <tr>
                            <td>№</td>
                            <td>Номенклатура</td>
                            <td>№ серіі (партіі)</td>
                            <td>Термін придатності</td>
                            <td>Кількість</td>
                            <td>Од. вим</td>
                            <td>Ціна без ПДВ, грн.</td>
                            <td>Сума без ПДВ, грн.</td>
                        </tr>
                    </thead>

                    <tbody>
                        {data.tableThird.map((row) => {
                            return (
                                <tr key={row[0]}>
                                    <td>{row[0]}</td>
                                    <td>{row[1]}</td>
                                    <td>{row[2]}</td>
                                    <td>{row[3]}</td>
                                    <td>{row[4]}</td>
                                    <td>{row[5]}</td>
                                    <td>{row[6]}</td>
                                    <td>{row[7]}</td>
                                </tr>
                            );
                        })}

                        <tr
                            className={css.rowNoBorder}
                            style={{
                                pageBreakInside: 'avoid',
                            }}
                        >
                            <td colSpan={8}>
                                <table
                                    style={{
                                        tableLayout: 'fixed',
                                    }}
                                >
                                    <tbody>
                                        <tr>
                                            <td
                                                colSpan={2}
                                                className={css.alignRight}
                                                style={{
                                                    width: '100%',
                                                }}
                                            >
                                                ВСЬОГО
                                                <br />
                                                {data.vatRate} % ПДВ
                                                <br />
                                                Всього до сплати з ПДВ:
                                            </td>
                                            <td
                                                className={css.alignRight}
                                                style={{
                                                    width: '35mm',
                                                }}
                                            >
                                                {data.total}
                                                <br />
                                                {data.vat}
                                                <br />
                                                {data.totalAmount}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <br />
                                <div style={{ fontSize: '14px' }}>
                                    Загальна сума Специфікації складає:
                                    <br />
                                    {generateUATextMoneyRepresentation(
                                        parseFloat(
                                            data.totalAmount.replace(',', '.'),
                                        ),
                                    )}
                                    <br />в тому числі ПДВ{' '}
                                    <span
                                        style={{
                                            paddingLeft: '5mm',
                                        }}
                                    />{' '}
                                    {data.vatRate} %
                                    <br />
                                    {generateUATextMoneyRepresentation(
                                        parseFloat(data.vat.replace(',', '.')),
                                    )}
                                    <br />
                                    <br />
                                    Підписи сторін:
                                    <br />
                                    <br />
                                    <div className={css.signsLast}>
                                        <div>
                                            <div className={css.lastSignTitle}>
                                                Від імені та за дорученням
                                                Продавця
                                            </div>
                                            <div className={css.signLast}>
                                                <div>Підпис:</div>
                                                <div>
                                                    <hr />
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <div className={css.lastSignTitle}>
                                                Від імені та за дорученням
                                                Покупця
                                            </div>
                                            <div className={css.signLast}>
                                                <div>Підпис:</div>
                                                <div>
                                                    <hr />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </div>
    );
};

BayerNakladnaComponent.orientation = XMLDocumentViewOrientation.landscape;

export const BayerNakladna = BayerNakladnaComponent;
