export const xmlQueries = {
    documentNumber: 'NAKLADNA > DOCUMENTHEADER > DOCUMENTNUMBER',
    documentDate: 'NAKLADNA > DOCUMENTHEADER > DOCUMENTDATE',
    contractNumber: 'NAKLADNA > DOCUMENTHEADER > CONTRACTNUMBER',
    contractDate: 'NAKLADNA > DOCUMENTHEADER > CONTRACTDATE',
    paymentDate: 'NAKLADNA > DOCUMENTHEADER > PAYMENTDATE',
    salerOrderNumber: 'NAKLADNA > DOCUMENTHEADER > SALERORDERNUMBER',
    deliveryNumber: 'NAKLADNA > DOCUMENTHEADER > DELIVERYNUMBER',
    plant: 'NAKLADNA > DOCUMENTHEADER > PLANT',
    licenseSupplier: 'NAKLADNA > DOCUMENTHEADER > LICENSESUPPLIER',
    licenseDate: 'NAKLADNA > DOCUMENTHEADER > LICENSEDATE',
    licenseSoldTo: 'NAKLADNA > DOCUMENTHEADER > LICENSESOLD<PERSON>',
    documentCorrection: 'NAKLADNA > DOCUMENTHEADER > DOCUMENTCORRECTION',
    supplyOrder: 'NAKLADNA > DOCUMENTHEADER > SOLDTOORDERNUMBER',
    supplierName: 'NAKLADNA > SUPPLIER > NAME',
    supplierAddress: 'NAKLADNA > SUPPLIER > ADDRESS',
    supplierEDRPOU: 'NAKLADNA > SUPPLIER > EDRPOU',
    supplierIBAN: 'NAKLADNA > SUPPLIER > BANKDATA > IBAN',
    supplierBankAddress: 'NAKLADNA > SUPPLIER > BANKDATA > BANKADDRESS',
    supplierBankPhone: 'NAKLADNA > SUPPLIER > BANKDATA > BANKPHONE',
    soldToCode: 'NAKLADNA > SOLDTO > SOLDTOCODE',
    soldToName: 'NAKLADNA > SOLDTO > SOLDTONAME',
    soldToAddress: 'NAKLADNA > SOLDTO > SOLDTOADDRESS',
    soldToIPN: 'NAKLADNA > SOLDTO > SOLDTOIPN',
    soldToEDRPOU: 'NAKLADNA > SOLDTO > SOLDTOEDRPOU',
    shipToCode: 'NAKLADNA > SHIPTO > SHIPTOCODE',
    shipToName: 'NAKLADNA > SHIPTO > SHIPTONAME',
    shipToAddress: 'NAKLADNA > SHIPTO > SHIPTOADDRESS',
    shipToPhone: 'NAKLADNA > SHIPTO > SHIPTOPHONE',
    totalWithoutDiscount: 'NAKLADNA > TOTALWITHOUTDISCOUNT',
    discount: 'NAKLADNA > DISCOUNT',
    total: 'NAKLADNA > TOTAL',
    vatRate: 'NAKLADNA > VATRATE',
    vat: 'NAKLADNA > VAT',
    totalAmount: 'NAKLADNA > TOTALAMOUNT',
    table: 'NAKLADNA > MAINTABLE',
} as const;
