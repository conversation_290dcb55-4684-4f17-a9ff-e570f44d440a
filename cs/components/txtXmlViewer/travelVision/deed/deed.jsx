import React from 'react';

import PropTypes from 'prop-types';

import { detectDeedKind } from '../utils';

import { XmlData } from '../../../../records/xml';
import { DeedKind } from '../constants';
import DeedAvr from '../deedAvr/deedAvr';
import DeedRtu from '../deedRtu/deedRtu';

const Deed = ({ data }) => {
    const deedKind = detectDeedKind(data.documentType, data.services);
    return deedKind === DeedKind.RTU ? (
        <DeedRtu data={data} />
    ) : (
        <DeedAvr data={data} />
    );
};

Deed.propTypes = {
    data: PropTypes.instanceOf(XmlData).isRequired,
};

export default Deed;
