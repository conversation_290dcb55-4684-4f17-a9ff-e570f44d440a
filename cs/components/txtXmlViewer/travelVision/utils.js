import { DeedKind, ProductType } from './constants';

export function detectDeedKind(documentType, services) {
    const isOnlyGoods = services.every(
        (service) => service.type === ProductType.GOOD,
    );
    const isOnlyServices = services.every(
        (service) => service.type === ProductType.SERVICE,
    );

    // Single type services
    if (isOnlyGoods) return DeedKind.RTU;
    if (isOnlyServices) return DeedKind.AVR;

    // Multiple types services, detect by document type
    if (documentType) {
        return documentType === DeedKind.RTU ? DeedKind.RTU : DeedKind.AVR;
    }

    // Multiple types services, but no document type provided, return default type
    return DeedKind.AVR;
}

export default {
    detectDeedKind,
};
