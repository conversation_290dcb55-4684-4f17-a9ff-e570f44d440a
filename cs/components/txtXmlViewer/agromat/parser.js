import I, { Record } from 'immutable';
import moment from 'moment';

import { boolContent, parseTableData, textContent } from '../utils';

import { sum } from '../../../lib/numbers';
import {
    XmlBank,
    XmlCompany,
    XmlProduct,
    XmlProducts,
    xmlDataJs,
} from '../../../records/xml';
import { parseXml as domParseXml } from '../../../services/xml';
import * as constants from './constants';

export const AgromatXmlData = new Record({
    ...xmlDataJs,
    delivery: null,
    deliveryPlace: null,
    warning: null,
    letterCode: null,
    letterDate: null,
});

const AgromatXmlProduct = new I.Record({
    number: null,
    quantity: null,
    taxes: null,
    group: null,
    trademark: null,
    productSize: null,
    nomQuantity: null,
    productCode: null,
    name: null,
    price: null,
    sum: null,
    item: null,
});

export const parseXmlOwnerBank = (xml) => {
    let iban = textContent(xml, constants.OWNER_BANK_IBAN_QUERY, false);
    let account = textContent(xml, constants.OWNER_BANK_ACCOUNT_QUERY, false);
    let mfo = textContent(xml, constants.OWNER_BANK_MFO_QUERY, false);
    let name = textContent(xml, constants.OWNER_BANK_NAME_QUERY, false);

    if (!iban) {
        iban = textContent(xml, constants.OWNER_BANK_IBAN_FALLBACK_QUERY);
    }

    if (!account) {
        account = textContent(xml, constants.OWNER_BANK_ACCOUNT_FALLBACK_QUERY);
    }

    if (!mfo) {
        mfo = textContent(xml, constants.OWNER_BANK_MFO_FALLBACK_QUERY);
    }

    if (!name) {
        name = textContent(xml, constants.OWNER_BANK_NAME_FALLBACK_QUERY);
    }

    return new XmlBank({ iban, account, mfo, name });
};

export const parseXmlPartnerBank = (xml) => {
    let iban = textContent(xml, constants.PARTNER_BANK_IBAN_QUERY, false);
    let account = textContent(xml, constants.PARTNER_BANK_ACCOUNT_QUERY, false);
    let mfo = textContent(xml, constants.PARTNER_BANK_MFO_QUERY, false);
    let name = textContent(xml, constants.PARTNER_BANK_NAME_QUERY, false);

    if (!iban) {
        iban = textContent(xml, constants.PARTNER_BANK_IBAN_FALLBACK_QUERY);
    }

    if (!account) {
        account = textContent(
            xml,
            constants.PARTNER_BANK_ACCOUNT_FALLBACK_QUERY,
        );
    }

    if (!mfo) {
        mfo = textContent(xml, constants.PARTNER_BANK_MFO_FALLBACK_QUERY);
    }

    if (!name) {
        name = textContent(xml, constants.PARTNER_BANK_NAME_FALLBACK_QUERY);
    }

    return new XmlBank({ iban, account, mfo, name });
};

export const parseXmlOwner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.OWNER_ADDRESS_QUERY),
        bank: parseXmlOwnerBank(xml),
        edrpou: textContent(xml, constants.OWNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.OWNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.OWNER_IPN_QUERY),
        phone: textContent(xml, constants.OWNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.OWNER_REPRESENTATIVE_QUERY,
            false,
        ),
    });
};

export const parseXmlPartner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.PARTNER_ADDRESS_QUERY),
        edrpou: textContent(xml, constants.PARTNER_EDRPOU_QUERY),
        bank: parseXmlPartnerBank(xml),
        fullName: textContent(xml, constants.PARTNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.PARTNER_IPN_QUERY),
        phone: textContent(xml, constants.PARTNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.PARTNER_REPRESENTATIVE_QUERY,
            false,
        ),
    });
};

export const parseXmlService = (serviceXml) => {
    return new XmlProduct({
        name: textContent(serviceXml, constants.SERVICE_NAME_QUERY),
        number: textContent(serviceXml, constants.SERVICE_NUMBER_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.SERVICE_PRICE_QUERY),
        ),
        quantity: textContent(serviceXml, constants.SERVICE_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.SERVICE_SUM_QUERY)),
        taxes: parseFloat(
            textContent(serviceXml, constants.SERVICE_TAXES_QUERY),
        ),
        type: constants.ProductType.SERVICE,
    });
};

export const parseXmlGoods = (serviceXml) => {
    return new AgromatXmlProduct({
        name: textContent(serviceXml, constants.GOODS_NAME_QUERY),
        number: textContent(serviceXml, constants.GOODS_NUMBER_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.GOODS_PRICE_NO_TAXES_QUERY),
        ),
        quantity: textContent(serviceXml, constants.GOODS_QUANTITY_QUERY),
        sum: parseFloat(
            textContent(serviceXml, constants.GOODS_SUM_NO_TAXES_QUERY),
        ),
        group: textContent(serviceXml, constants.GOODS_GROUP_QUERY),
        trademark: textContent(serviceXml, constants.GOODS_TRADEMARK_QUERY),
        productSize: textContent(serviceXml, constants.GOODS_SIZE_QUERY),
        nomQuantity: textContent(
            serviceXml,
            constants.GOODS_NOM_QUANTITY_QUERY,
        ),
        productCode: textContent(serviceXml, constants.GOODS_CODE_QUERY),
        taxes: parseFloat(textContent(serviceXml, constants.GOODS_TAXES_QUERY)),
        item: textContent(serviceXml, constants.GOODS_ITEM_QUERY),
    });
};

export const parseXmlServices = (xml) => {
    const serviceXmls = xml.querySelector(constants.SERVICES_QUERY)
        ? parseTableData(
              xml,
              constants.SERVICES_QUERY,
              constants.SERVICE_NUMBER_QUERY,
          )
        : [];
    const goodsXmls = xml.querySelector(constants.GOODS_QUERY)
        ? parseTableData(
              xml,
              constants.GOODS_QUERY,
              constants.GOODS_NUMBER_QUERY,
          )
        : [];
    const items = new I.List([
        ...goodsXmls.map(parseXmlGoods),
        ...serviceXmls.map(parseXmlService),
    ]);

    return new XmlProducts({
        currency: textContent(xml, constants.CURRENCY_QUERY),
        isTaxesIncluded: boolContent(xml, constants.IS_TAXES_INCLUDED_QUERY),
        items,
        totalDocumentSum: parseFloat(
            textContent(xml, constants.TOTAL_DOCUMENT_SUM_QUERY),
        ),
        totalPrice: sum(items, 'price'),
        totalPriceStr: textContent(xml, constants.TOTAL_PRICE_STR_QUERY),
        totalSum: sum(items, 'sum'),
        totalSumStr:
            textContent(xml, constants.TOTAL_SUM_STR_QUERY, false) ||
            textContent(xml, constants.TOTAL_SUM_STR_FALLBACK_QUERY),
        totalTaxes: sum(items, 'taxes'),
        totalTaxesStr: textContent(xml, constants.TOTAL_TAXES_STR_QUERY),
    });
};

export const parseXml = (content) => {
    const xml = domParseXml(content);
    const date = textContent(xml, constants.DATE_QUERY).trim();
    return new AgromatXmlData({
        date: date && moment(date),
        documentType: textContent(xml, constants.DOCUMENT_TYPE_QUERY).trim(),
        number: textContent(xml, constants.NUMBER_QUERY).trim(),

        agreementName: textContent(xml, constants.AGREEMENT_NAME_QUERY),
        agreementPlace: textContent(xml, constants.AGREEMENT_PLACE_QUERY),
        dealName: textContent(xml, constants.DEAL_NAME_QUERY, false),
        delivery: textContent(xml, constants.DELIVERY_QUERY, false),
        deliveryPlace: textContent(xml, constants.DELIVERY_PLACE_QUERY, false),
        owner: parseXmlOwner(xml),
        partner: parseXmlPartner(xml),
        services: parseXmlServices(xml),

        letterCode: textContent(xml, constants.LETTER_CODE),
        letterDate: textContent(xml, constants.LETTER_DATE),
        warning: textContent(xml, constants.WARNING_QUERY),
    });
};

export default (data) => parseXml(data.content);
