.headerTable {
    width: 100%;
    margin-bottom: 1.5cm;
}

.headerTable td {
    width: 50%;
    padding-right: 0.25cm;
    font-size: 80%;
    vertical-align: top;
}

@media (min-width: 720px) {
    .headerTable td {
        padding-right: 1.25cm;
    }
}

.documentTitle {
    font-size: 110%;
    font-weight: bold;
}

.documentNumber {
    font-size: 110%;
    font-weight: bold;
}

.documentNumberTable {
    padding-bottom: 0.05cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
}

.documentNumberDetailsTable {
    padding-left: 0;
}

.documentTotalText {
    font-weight: bold;
}

.documentTrivia,
.documentTotalText,
.documentTriviaBorder,
.documentTriviaIndent,
.documentTriviaMargin {
    width: 85%;
    font-size: 85%;
    line-height: 110%;
}

.compilationPlace {
    margin-bottom: 0.1cm;
    font-size: 85%;
    line-height: 110%;
}

.documentTriviaBorder {
    width: 100%;
    padding-bottom: 0.2cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-top: 0.4cm;
}

.documentTriviaMargin {
    margin: 0.4cm 0;
}

.documentDetailsTable {
    width: 100%;
}

.documentDetailsTable td {
    padding-top: 0.1cm;
    padding-bottom: 0.2cm;
    padding-left: 0;
    font-size: 80%;
}

.documentDetailsTable .detailName {
    width: 5%;
    padding-right: 0.2cm;
}

.footerTable {
    width: 100%;
    margin-top: 0.4cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.footerDate {
    padding-top: 0.6cm;
    font-size: 110%;
}

.representativeRow {
    font-size: 120%;
}

.digitalSignaturesWrapper {
    position: relative;
}

.positions td {
    padding-top: 6px;
}
