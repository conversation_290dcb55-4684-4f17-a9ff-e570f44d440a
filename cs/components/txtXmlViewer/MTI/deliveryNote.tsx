import React, { FC } from 'react';

import PropTypes from 'prop-types';

import { XmlData, XmlDocument } from '../../../types/xmlDocuments';

import ExpandButtonRow from '../ui/expandButtonRow/expandButtonRow';
import SignLine from '../ui/signLine/signLine';

import { formatDate } from '../../../lib/date';
import { formatPrice } from '../../../lib/numbers';
import Company from '../company/company';
import { DATE_FORMAT_FULL } from '../rozetka/constants';

// styles
import css from './deliveryNote.css';

interface Props {
    doc: XmlDocument;
    data: XmlData;
}

const DeliveryNote: FC<React.PropsWithChildren<Props>> = ({ data }) => {
    const {
        number,
        agreementName,
        agreementDate,
        date,
        place,
        partner,
        owner,
        services = {},
        extra = {},
    } = data;
    const {
        items,
        totalSum,
        totalDocumentSum,
        totalDocumentSumStr,
        totalTaxes,
    } = services;
    const { addDoc, addDocSigner } = extra;

    const prepareDocSignerBlock = () =>
        addDocSigner ? `${addDoc ? ', через ' : ''}${addDocSigner}` : '';

    return (
        <div>
            <h1 className={css.documentTitle}>
                Видаткова накладна № {number} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>

            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>Постачальник:</td>
                        <td>
                            <Company boldFullName data={owner} />
                        </td>
                    </tr>
                    <tr>
                        <td>Покупець:</td>
                        <td>
                            <Company boldFullName data={partner} />
                        </td>
                    </tr>
                    {(addDoc || addDocSigner) && (
                        <tr>
                            <td>Документ:</td>
                            <td>
                                <span>
                                    {addDoc}
                                    {prepareDocSignerBlock()}
                                </span>
                            </td>
                        </tr>
                    )}
                    <tr>
                        <td>Договір:</td>
                        <td>
                            № {agreementName}
                            {agreementDate &&
                                ` від ${formatDate(
                                    agreementDate,
                                    DATE_FORMAT_FULL,
                                )}`}
                        </td>
                    </tr>
                </tbody>
            </table>

            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <th className={css.number}>№</th>
                        <th className={css.code}>
                            Виробник, Артикул, парт номер
                        </th>
                        <th className={css.name}>Товар</th>
                        <th className={css.code}>Код УКТЗЕД</th>
                        <th className={css.quantity}>Кіл-сть</th>
                        <th className={css.item}>Од.</th>
                        <th className={css.price}>Ціна без ПДВ</th>
                        <th className={css.sum}>Сума без ПДВ</th>
                    </tr>
                </thead>
                <tbody>
                    {services?.items?.map((service, index) => (
                        <tr key={`product-${service.number}`}>
                            <td className={css.textCenter}>{index + 1}</td>
                            <td className={css.textRight}>
                                {service.supplierCode}
                            </td>
                            <td>{service.name}</td>
                            <td className={css.textRight}>
                                {service.externalCode}
                            </td>
                            <td className={css.textRight}>
                                {service.quantity}
                            </td>
                            <td className={css.textCenter}>{service.unit}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.price)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.sum)}
                            </td>
                        </tr>
                    ))}
                    <ExpandButtonRow
                        colSpan={6}
                        last={items.last && items.last.number}
                    />
                </tbody>
            </table>

            <table className={css.totalTable}>
                <tbody>
                    <tr>
                        <td className={css.totalName}>Всього:</td>
                        <td>{formatPrice(totalSum)}</td>
                    </tr>
                    <tr key="tax-key-2">
                        <td className={css.totalName}>Сума ПДВ:</td>
                        <td>{formatPrice(totalTaxes)}</td>
                    </tr>
                    <tr key="tax-key-3">
                        <td className={css.totalName}>Всього із ПДВ:</td>
                        <td>{formatPrice(totalDocumentSum)}</td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTrivia}>
                Всього найменувань {items.size}, на суму{' '}
                {formatPrice(services?.totalDocumentSum)} грн.
            </div>

            {totalDocumentSumStr && (
                <div className={css.documentTriviaBold}>
                    {totalDocumentSumStr}
                </div>
            )}

            {place && (
                <div className={css.documentTrivia}>
                    <div>Місце складання:</div>
                    <div>{place}</div>
                </div>
            )}

            <div className={css.documentTriviaBorder} />

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Постачальника*
                            <br />
                        </th>
                        <th>
                            Від Покупця
                            <br />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTriviaMargin}>
                * Підписання цієї накладної підтверджує, що товар отримано в
                зазначеній у ній кількості та без пошкоджень, які можливо
                виявити під час прийому-передачі, якщо інші умови не предбачені
                правилами прийому-передачі, які діють між Покупцем та Продавцем.
            </div>
        </div>
    );
};

DeliveryNote.propTypes = {
    data: PropTypes.object,
};

export default DeliveryNote;
