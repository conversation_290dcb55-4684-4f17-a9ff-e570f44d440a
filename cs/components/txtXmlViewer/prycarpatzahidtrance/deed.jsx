import React from 'react';

import PropTypes from 'prop-types';

import { detectDeedKind } from './utils';

import { XmlData } from '../../../records/xml';
import { DeedKind } from './constants';
import DeedAvr from './deedAvr';
import DeedRtu from './deedRtu';

const Deed = (props) => {
    const deedKind = detectDeedKind(
        props.data.documentType,
        props.data.services,
    );
    return deedKind === DeedKind.RTU ? (
        <DeedRtu {...props} />
    ) : (
        <DeedAvr {...props} />
    );
};

Deed.propTypes = {
    data: PropTypes.instanceOf(XmlData).isRequired,
};

export default Deed;
