import React from 'react';

import PropTypes from 'prop-types';

import { formatTotalPrice } from '../utils';

import SignLine from '../ui/signLine/signLine';

import { formatDate } from '../../../lib/date';
import { XmlDocument } from '../../../records/document';
import { XmlData } from '../../../records/xml';
import FooterRenderer from '../../footerRenderer/footerRenderer';
import Company from '../company/company';
import { DATE_FORMAT } from '../constants';
import ProductsTable from '../products/productsTable';
import { DATE_FORMAT_FULL, DeedKind, NUMBER_REGEXP } from './constants';

import css from './deed.css';

const Deed = (props) => {
    const { data, doc, renderSignatures, renderReviews } = props;
    const { date, number, owner, partner, services } = data;
    const fullDate = formatDate(date, DATE_FORMAT_FULL);
    const shortDate = formatDate(date, DATE_FORMAT);
    const shortNumber = number.replace(NUMBER_REGEXP, '');
    const isInsuranceKind = data.kind === DeedKind.INSURANCE;

    return (
        <div className={css.root}>
            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>ЗАТВЕРДЖУЮ</td>
                        <td>ЗАТВЕРДЖУЮ</td>
                    </tr>
                    <tr>
                        <td>{owner.representativePosition}</td>
                        <td>{partner.representativePosition}</td>
                    </tr>
                    <tr>
                        <td>{owner.fullName}</td>
                        <td>{partner.fullName}</td>
                    </tr>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                    <tr>
                        <td>{owner.representative}</td>
                        <td>{partner.representative}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td>Місце складання: м.Київ</td>
                        <td className={css.headerDate}>{fullDate}</td>
                    </tr>
                </tfoot>
            </table>
            {renderSignatures && (
                <div className={css.headerDigitalSignatures}>
                    <div className={css.digitalSignaturesWrapper}>
                        <FooterRenderer
                            renderSignatures={renderSignatures}
                            renderReviews={false} // reviews are rendered below
                            doc={doc}
                        />
                    </div>
                </div>
            )}

            <h1 className={css.documentNumber}>АКТ № {shortNumber}</h1>
            <h2 className={css.documentTitle}>
                здачі-приймання робіт (надання послуг)
            </h2>

            <div className={css.documentTriviaIndent}>
                Ми, що нижче підписалися, представник Замовника{' '}
                {partner.fullName} {partner.representativePosition}{' '}
                {partner.representative}, з одного боку, і представник Виконавця{' '}
                {owner.fullName} {owner.representativePosition}{' '}
                {owner.representative}, з іншого боку, склали цей акт про те, що
                на підставі наведених документів:
            </div>
            <table className={css.documentDetailsTable}>
                <tbody>
                    <tr>
                        <td className={css.detailName}>Договір:</td>
                        <td>
                            {data.agreementName ||
                                (isInsuranceKind && 'страхування')}
                        </td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTrivia}>
                Виконавцем були виконані наступні роботи (надані таки послуги):
            </div>
            <ProductsTable
                withoutTaxes={isInsuranceKind}
                data={services}
                serviceNameLabel="Найменування робіт, послуг"
            />

            {isInsuranceKind ? (
                <div>
                    <div className={css.documentTrivia}>
                        Загальна вартість робіт (послуг) склала{' '}
                        {formatTotalPrice(services, 'totalSum')}.
                    </div>
                    <div className={css.documentTrivia}>
                        ПДВ не передбачено згідно ст. 196.1.3 ПКУ
                    </div>
                </div>
            ) : (
                <div className={css.documentTrivia}>
                    Загальна вартість робіт (послуг) склала без ПДВ{' '}
                    {formatTotalPrice(services, 'totalPrice')}, ПДВ{' '}
                    {formatTotalPrice(services, 'totalTaxes')}, загальна
                    вартість робіт (послуг) із ПДВ{' '}
                    {formatTotalPrice(services, 'totalSum')}.
                </div>
            )}
            <div className={css.documentTrivia}>
                Сторони претензій одна до одної не мають.
            </div>
            <div className={css.documentTriviaBold}>
                Якщо протягом 3-х рабочих днів не надійшло претензій з боку
                замовника, то акт вважається погодженим.
            </div>

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Виконавця*
                            <br />
                        </th>
                        <th>
                            Від Замовника
                            <br />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                    <tr className={css.representativeRow}>
                        <td>
                            <b>
                                {owner.representativePosition}{' '}
                                {owner.representative}
                            </b>
                        </td>
                        <td>
                            <b>
                                {partner.representativePosition}{' '}
                                {partner.representative}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    <tr>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <Company data={owner} />
                        </td>
                        <td>
                            <Company data={partner} />
                        </td>
                    </tr>
                </tbody>
            </table>
            {(renderSignatures || renderReviews) && (
                <div className={css.digitalSignaturesWrapper}>
                    <FooterRenderer
                        renderSignatures={renderSignatures}
                        renderReviews={renderReviews}
                        doc={doc}
                    />
                </div>
            )}
        </div>
    );
};

Deed.propTypes = {
    printMode: PropTypes.bool,
    renderSignatures: PropTypes.bool,
    renderReviews: PropTypes.bool,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
    data: PropTypes.instanceOf(XmlData).isRequired,
};

Deed.defaultProps = {
    printMode: false,
};

export default (props) => <Deed {...props} />;
