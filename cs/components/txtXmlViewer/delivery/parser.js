import I from 'immutable';
import moment from 'moment';

import {
    boolContent,
    formatAgreementName,
    parseTableData,
    textContent,
} from '../utils';

import { sum } from '../../../lib/numbers';
import {
    XmlBank,
    XmlCompany,
    XmlData,
    XmlPayment,
    XmlPayments,
    XmlProduct,
    XmlProducts,
} from '../../../records/xml';
import { parseXml as domParseXml } from '../../../services/xml';
import constants from './constants';

export const parseXmlOwnerBank = (xml) => {
    return new XmlBank({
        account: textContent(xml, constants.OWNER_BANK_ACCOUNT_QUERY, false),
        mfo: textContent(xml, constants.OWNER_BANK_MFO_QUERY, false),
        name: textContent(xml, constants.OWNER_BANK_NAME_QUERY, false),
    });
};

export const parseXmlOwner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.OWNER_ADDRESS_QUERY),
        bank: parseXmlOwnerBank(xml),
        certificate: textContent(xml, constants.OWNER_CERTIFICATE_QUERY, false),
        edrpou: textContent(xml, constants.OWNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.OWNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.OWNER_IPN_QUERY),
        phone: textContent(xml, constants.OWNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.OWNER_REPRESENTATIVE_QUERY,
            false,
        ),
        representativePosition: textContent(
            xml,
            constants.OWNER_REPRESENTATIVE_POSITION_QUERY,
            false,
        ),
    });
};

export const parseXmlPartner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.PARTNER_ADDRESS_QUERY),
        certificate: textContent(
            xml,
            constants.PARTNER_CERTIFICATE_QUERY,
            false,
        ),
        edrpou: textContent(xml, constants.PARTNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.PARTNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.PARTNER_IPN_QUERY),
        phone: textContent(xml, constants.PARTNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.PARTNER_REPRESENTATIVE_QUERY,
            false,
        ),
        representativePosition: textContent(
            xml,
            constants.PARTNER_REPRESENTATIVE_POSITION_QUERY,
            false,
        ),
    });
};

export const parseXmlService = (serviceXml) => {
    return new XmlProduct({
        item: textContent(serviceXml, constants.SERVICE_ITEM_QUERY),
        name: textContent(serviceXml, constants.SERVICE_NAME_QUERY),
        number: textContent(serviceXml, constants.SERVICE_NUMBER_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.SERVICE_PRICE_QUERY),
        ),
        quantity: textContent(serviceXml, constants.SERVICE_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.SERVICE_SUM_QUERY)),
        taxes: parseFloat(
            textContent(serviceXml, constants.SERVICE_TAXES_QUERY),
        ),
    });
};

export const parseXmlGoods = (serviceXml) => {
    return new XmlProduct({
        item: textContent(serviceXml, constants.GOODS_ITEM_QUERY),
        name: textContent(serviceXml, constants.GOODS_NAME_QUERY),
        number: textContent(serviceXml, constants.GOODS_NUMBER_QUERY),
        price: parseFloat(textContent(serviceXml, constants.GOODS_PRICE_QUERY)),
        quantity: textContent(serviceXml, constants.GOODS_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.GOODS_SUM_QUERY)),
        taxes: parseFloat(textContent(serviceXml, constants.GOODS_TAXES_QUERY)),
    });
};

export const parseXmlServices = (xml) => {
    const serviceXmls = xml.querySelector(constants.SERVICES_TABLE_QUERY)
        ? parseTableData(
              xml,
              constants.SERVICES_TABLE_QUERY,
              constants.TABLE_ROW_QUERY,
          )
        : [];
    const goodsXmls = xml.querySelector(constants.GOODS_TABLE_QUERY)
        ? parseTableData(
              xml,
              constants.GOODS_TABLE_QUERY,
              constants.GOODS_NUMBER_QUERY,
          )
        : [];
    const items = new I.List([
        ...goodsXmls.map(parseXmlGoods),
        ...serviceXmls.map(parseXmlService),
    ]);

    return new XmlProducts({
        currency: textContent(xml, constants.CURRENCY_QUERY),
        isTaxesIncluded: boolContent(xml, constants.IS_TAXES_INCLUDED_QUERY),
        items,
        totalDocumentSum: parseFloat(
            textContent(xml, constants.TOTAL_DOCUMENT_SUM_QUERY),
        ),
        totalPrice: sum(items, 'price'),
        totalPriceStr: textContent(xml, constants.TOTAL_PRICE_STR_QUERY),
        totalSum: sum(items, 'sum'),
        totalSumStr: textContent(xml, constants.TOTAL_SUM_STR_QUERY, false),
        totalTaxes: sum(items, 'taxes'),
        totalTaxesStr: textContent(xml, constants.TOTAL_TAXES_STR_QUERY),
    });
};

export const parseXmlPayment = (paymentXml) => {
    const date = textContent(paymentXml, constants.PAYMENT_DATE_QUERY);
    return new XmlPayment({
        date: date && moment(date),
        document: textContent(paymentXml, constants.PAYMENT_DOCUMENT_QUERY),
        debit: parseFloat(
            textContent(paymentXml, constants.PAYMENT_DEBIT_QUERY),
        ),
        credit: parseFloat(
            textContent(paymentXml, constants.PAYMENT_CREDIT_QUERY),
        ),
    });
};

export const parseXmlPayments = (xml) => {
    const paymentXmls = parseTableData(
        xml,
        constants.PAYMENTS_TABLE_QUERY,
        constants.TABLE_ROW_QUERY,
    );
    const items = new I.List(paymentXmls.map(parseXmlPayment));

    return new XmlPayments({
        currency: textContent(xml, constants.CURRENCY_QUERY),
        beginningBalance: parseFloat(
            textContent(xml, constants.BEGINNING_BALANCE),
        ),
        beginningBalanceCredit: parseFloat(
            textContent(xml, constants.BEGINNING_BALANCE_CREDIT),
        ),
        beginningBalanceDebit: parseFloat(
            textContent(xml, constants.BEGINNING_BALANCE_DEBIT),
        ),
        items,
        totalDebit: sum(items, 'debit'),
        totalCredit: sum(items, 'credit'),
    });
};

function getDeedKindFromRootTagName(xml) {
    const tagName = xml.getElementsByTagName('*')[0].tagName;
    return tagName.split('.')[1];
}

export const parseXml = (content) => {
    const xml = domParseXml(content);

    // Implemented it, bcs if we change the TYPE, we will not be able display xml doc
    // (before we parsed xml from TYPE parameter that can be changed)
    const kind = getDeedKindFromRootTagName(xml);

    let agreementName = textContent(xml, constants.AGREEMENT_NAME_QUERY, false);
    if (!agreementName) {
        const agreementDate = textContent(
            xml,
            constants.AGREEMENT_NAME_FALLBACK_DATE_QUERY,
            false,
        );
        const agreementNumber = textContent(
            xml,
            constants.AGREEMENT_NAME_FALLBACK_NUMBER_QUERY,
            false,
        );
        agreementName = formatAgreementName(agreementDate, agreementNumber);
    }

    switch (kind) {
        case constants.DeedKind.COMMODITY:
        case constants.DeedKind.INSURANCE: {
            const date = textContent(xml, constants.DATE_QUERY).trim();
            return new XmlData({
                kind,
                agreementName,
                date: date && moment(date),
                number: textContent(xml, constants.NUMBER_QUERY).trim(),
                owner: parseXmlOwner(xml),
                partner: parseXmlPartner(xml),
                services: parseXmlServices(xml),
            });
        }
        case constants.DeedKind.RECONCILIATION: {
            const dateFrom = textContent(xml, constants.DATE_FROM_QUERY).trim();
            const dateTo = textContent(xml, constants.DATE_TO_QUERY).trim();
            return new XmlData({
                kind,
                agreementName,
                dateFrom: dateFrom && moment(dateFrom),
                dateTo: dateFrom && moment(dateTo),
                owner: parseXmlOwner(xml),
                partner: parseXmlPartner(xml),
                payments: parseXmlPayments(xml),
            });
        }
        default:
            return {};
    }
};

export default (data) => parseXml(data.content);
