.root {
    position: relative;
    display: flex;
    flex-grow: 1;
}

@media screen and (max-width: 1200px) {
    .root {
        width: 100%;
        box-sizing: border-box;
    }
}

.root .content {
    width: 100%;
}

.content,
.contentMinimal {
    width: calc(100% - 260px);
    box-sizing: border-box;
}

@media all and (max-width: 1024px) {
    .content {
        width: 100%;
    }
}

.contentMinimal {
    width: 100%;
}

.wrapper {
    box-sizing: border-box;
    padding: 20px 20px 100px;
}

.item:first-child {
    position: relative;
}

.item + .item {
    padding-top: 20px;
    border-top: 1px solid var(--default-border);
    margin-top: 20px;
}

.extention {
    margin-top: 10px;
    color: #9cb2c4;
    font-size: 11px;
}

.button + .button {
    margin-top: 10px;
}

.donateBanner {
    height: auto;
    padding: 15px 15px 0;
}
