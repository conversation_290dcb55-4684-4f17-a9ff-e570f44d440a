import React from 'react';
import MediaQuery from 'react-responsive';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import { SendPopupStatus } from '../../services/enums';

import Button from '../ui/button/button';
import Message from '../ui/message/message';
import PopupMobile from '../ui/popup/mobile/popup';
import Popup from '../ui/popup/popup';
import PseudoLink from '../ui/pseudolink/pseudolink';

import { MEDIA_WIDTH } from '../../lib/constants';
import { BILL_GENERATION_PAGE_PATTERN } from '../../lib/routing/constants';
import RequiredFieldsResolver from '../RequiredFieldsResolver/RequiredFieldsResolver';
import DocumentActionInfoMessage from '../documentActionInfoMessage/documentActionInfoMessage';
import RecipientFormContainer from '../recipientForm/recipientFormContainer';

// styles
import css from './sendPopup.css';

class SendPopup extends React.Component {
    static propTypes = {
        isActive: PropTypes.bool.isRequired,
        isMultiAction: PropTypes.bool.isRequired,
        isOverlimit: PropTypes.bool.isRequired,
        documents: PropTypes.array.isRequired,
        currentRoleId: PropTypes.string.isRequired,
        sentResults: PropTypes.array.isRequired,
        status: PropTypes.string.isRequired,
        errorMessage: PropTypes.string,
        sentDocCount: PropTypes.number,
        onSend: PropTypes.func.isRequired,
        onClose: PropTypes.func.isRequired,
    };

    state = {
        isLoading: false,
        email: null,
        edrpou: null,
        isEmailHidden: false,
    };

    componentDidUpdate(prevProps) {
        if (
            prevProps.errorMessage !== this.props.errorMessage &&
            this.props.errorMessage &&
            this.state.isLoading
        ) {
            this.setState({ isLoading: false });
        }
    }

    handleSend = () => {
        this.setState({ isLoading: true });
        this.props.onSend();
    };

    handleFormSubmit = (email, edrpou, isEmailHidden) => {
        this.setState({ email, edrpou, isEmailHidden });
        this.props.onSend(email, edrpou, isEmailHidden);
    };

    renderPopupContent() {
        const props = this.props;
        const doc = props.documents[0];
        const documentsCount = props.documents.length;
        let content;

        const sendConfirmForm = (isMultiSend) => (
            <div>
                <div className={css.hint}>
                    {t`Ви впевнені, що хочете надіслати`}{' '}
                    {isMultiSend ? t`документи` : t`документ`}?
                </div>
                <div className={css.button}>
                    <Button
                        isLoading={this.state.isLoading}
                        theme="cta"
                        onClick={this.handleSend}
                    >
                        {t`Надіслати`}
                    </Button>
                </div>
                <div className={css.cancel}>
                    <PseudoLink
                        onClick={props.onClose}
                    >{t`Відмінити`}</PseudoLink>
                </div>
            </div>
        );

        if (props.status === SendPopupStatus.SUCCESS) {
            content = (
                <DocumentActionInfoMessage
                    isMultiAction={props.isMultiAction}
                    isOverlimit={props.isOverlimit}
                    docs={props.documents}
                    currentRoleId={props.currentRoleId}
                    multiActionResult={props.sentResults}
                    totalDocsCount={documentsCount}
                    actionName={t`надіслали`}
                    buttonText={
                        this.props.isMultiAction
                            ? t`Повернутись до документів`
                            : t`Повернутись до документа`
                    }
                    fileName={doc.title}
                    email={this.state.email || doc.emailRecipient}
                    edrpou={this.state.edrpou || doc.edrpouRecipient}
                    errorMessage={props.errorMessage}
                    onClose={props.onClose}
                />
            );
        } else if (
            props.status === SendPopupStatus.SEND_REQUIRED_FIELDS_ERROR
        ) {
            content = <RequiredFieldsResolver />;
        } else if (props.isMultiAction) {
            content = sendConfirmForm(true);
            // багатосторонній документ
        } else if (documentsCount === 1 && doc.isMultilateral) {
            content = sendConfirmForm(false);
        } else if (['SIGNED', 'APPROVED'].indexOf(doc.status) >= 0) {
            // Don't need to fill recipient
            content = sendConfirmForm(false);
        } else {
            content = (
                <div>
                    <div className={css.hint}>
                        {t`Вкажіть Email і ЄДРПОУ/ІПН контрагента`}
                    </div>
                    <RecipientFormContainer
                        hideHint
                        buttonText={t`Надіслати`}
                        defaultEmail={doc.emailRecipient}
                        defaultEdrpou={doc.edrpouRecipient}
                        defaultEmailHidden={doc.isRecipientEmailHidden}
                        onSubmit={this.handleFormSubmit}
                    />
                </div>
            );
        }

        return (
            <div className={css.root}>
                {content}
                {props.errorMessage && props.status === SendPopupStatus.ERROR && (
                    <div className={css.error}>
                        <Message sizeSmall type="error">
                            {props.errorMessage}
                            {props.isOverlimit && (
                                <div>
                                    <a href={BILL_GENERATION_PAGE_PATTERN}>
                                        {t`Поповніть баланс`}
                                    </a>
                                </div>
                            )}
                        </Message>
                    </div>
                )}
            </div>
        );
    }

    render() {
        const props = this.props;
        const doc = props.documents[0];
        let title = '';

        if (props.status !== SendPopupStatus.SUCCESS) {
            title = props.isMultiAction
                ? t`Надіслати документи`
                : t`Надіслати документ “${doc.title}”`;
        }

        return (
            <div>
                <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                    <Popup
                        inPortal
                        active={props.isActive}
                        onClose={props.onClose}
                        title={title}
                    >
                        {this.renderPopupContent()}
                    </Popup>
                </MediaQuery>
                <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                    <PopupMobile
                        inPortal
                        active={props.isActive}
                        onClose={props.onClose}
                        title={t`Надіслати документ`}
                        subtitle={doc.title}
                    >
                        {this.renderPopupContent()}
                    </PopupMobile>
                </MediaQuery>
            </div>
        );
    }
}

export default SendPopup;
