import React from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import { DocumentListDocumentItem } from '../documentList/types';

import IconButton from '../ui/iconButton/iconButton';

import eventTracking from '../../services/analytics/eventTracking';
import { useButtonHandler } from './useButtonHandler';

import SvgDownload from './images/download.svg';

interface Props {
    doc: DocumentListDocumentItem | Document;
    versionId?: string;
    isInPopup?: boolean;
}

const DownloadDocumentVersionButton: React.FC<Props> = ({
    doc,
    versionId,
    isInPopup,
}) => {
    const { handleDownloadDocument } = useButtonHandler(doc, versionId);

    const onClickIconAnalytics = () => {
        if (isInPopup) {
            eventTracking.sendToGTM({
                category: 'versions_pop-up',
                action: 'download_version',
            });
        } else {
            eventTracking.sendToGTM({
                category: 'version_download_button',
                action: 'download_version',
            });
        }
    };

    return (
        <BlackTooltip title={t`Завантажити`} disableInteractive>
            <span onClick={handleDownloadDocument}>
                <IconButton svg={SvgDownload} onClick={onClickIconAnalytics} />
            </span>
        </BlackTooltip>
    );
};

export default DownloadDocumentVersionButton;
