import React from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip } from '@vchasno/ui-kit';

import { getCurrentCompany } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { isAlredySigned3pDocument } from 'services/documents/utils';
import { t } from 'ttag';

import Icon from '../../ui/icon/icon';

import SvgMultilateral from '../images/multi.svg';
import SvgAlert from './images/alert.svg';
import SvgInput from './images/in.svg';
import SvgInternal from './images/internal.svg';
import SvgOutput from './images/out.svg';

import css from './headIconBlock.css';

const HeadBlock = ({ doc }) => {
    const { hasInvalidSignedDocuments } = useSelector(getCurrentCompany);

    let arrowSvg = SvgInternal;
    let arrowTitle = t`Внутрішній документ`;
    if (!doc.isInternal) {
        arrowSvg = doc.isInput ? SvgInput : SvgOutput;
        arrowTitle = doc.isInput ? t`Вхідний документ` : t`Вихідний документ`;
        if (isAlredySigned3pDocument(doc)) {
            arrowSvg = doc.isInput ? SvgOutput : SvgInput;
            arrowTitle = doc.isInput
                ? t`Вихідний документ`
                : t`Вхідний документ`;
        }
    }
    if (doc.isMultilateral) {
        arrowSvg = SvgMultilateral;
        arrowTitle = t`Багатосторонній документ`;
    }

    if (hasInvalidSignedDocuments && doc.isInvalidSigned) {
        arrowSvg = SvgAlert;
        arrowTitle = t`Цей документ має недійсні підписи`;
    }

    return (
        <BlackTooltip placement="left" title={arrowTitle} disableInteractive>
            <span
                className={css.icon}
                onClick={() => {
                    eventTracking.sendToGTM({
                        event: 'click_arrow',
                    });
                }}
            >
                <Icon glyph={arrowSvg} />
            </span>
        </BlackTooltip>
    );
};

export default HeadBlock;
