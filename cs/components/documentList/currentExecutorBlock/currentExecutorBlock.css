.cell,
.root {
    display: table-cell;
    overflow: hidden;
    padding: 7px 0 7px 20px;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: nowrap;
}

.cell:last-child {
    text-align: right;
}

@media all and (max-width: 1200px) {
    .cell {
        padding-left: 10px;
    }
}

.root {
    width: 12%;
    color: var(--content-color);
}

@media all and (max-width: 1200px) {
    .root {
        padding-left: 10px;
    }
}
