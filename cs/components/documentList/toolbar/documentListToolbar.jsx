import React from 'react';
import { connect } from 'react-redux';

import { BlackTooltip, Text, snackbarToast } from '@vchasno/ui-kit';

import { getApiErrorMessage } from 'components/DocumentEdit/utils';
import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import AddListToDirectoryButton from 'components/documentList/AddListToDirectoryButton';
import actionCreators from 'components/documentList/documentListActionCreators';
import ProLabel from 'components/proLabel/proLabel';
import { PermissionCategory } from 'components/proRateInfoPopup/proRateInfoPopupTypes';
import { WithDocumentProcessesRenderer } from 'contexts/documentProcesses';
import { getIsArchivePageUtility } from 'lib/ts/helpers';
import PropTypes from 'prop-types';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import {
    getIsOnlyExtraSignatureDocumentsSelector,
    getIsOnlyNotReadyToSignAfterReviewDocumentsSelector,
    getIsOnlySignDocumentsSelector,
} from 'selectors/documentList.selectors';
import { canRejectDocument } from 'services/documents/utils';
import { DocumentReviewType, SignatureFormat } from 'services/enums';
import { mapStatetoHasPermission } from 'store/utils';
import { msgid, ngettext, t } from 'ttag';

import { getIsReviewNeededBeforeAction } from '../helpers';

import Button from '../../ui/button/button';
import DropButton from '../../ui/dropButton/dropButton';
import Hint from '../../ui/hint/hint';
import IconButton from '../../ui/iconButton/iconButton';
import PromptPopup from '../../ui/promptPopup/promptPopup';

import eventTracking from '../../../services/analytics/eventTracking';
import KepSignButton from '../../KepSigner/KepSignButton';
import { MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT } from '../../KepSigner/constants';
import ActionTools from '../../actionTools/actionTools';
import ChangeDocumentButton from '../../changeDocumentButton/changeDocumentButton';
import ChangeRecipientPopup from '../../changeRecipientPopup/changeRecipientPopup';
import LinkDocumentsPopupButton from '../../linkDocumentsPopupButton/linkDocumentsPopupButton';
import ReviewPopup from '../../reviewPopup/reviewPopup';
import SendPopup from '../../sendPopup/sendPopup';

// icons
import SvgComment from '../images/comment.svg';
import SvgPerson from '../images/person.svg';
import SvgTag from '../images/tag.svg';

// styles
import css from './documentListToolbar.css';

class DocumentListToolbar extends React.Component {
    static propTypes = {
        hasPermission: PropTypes.func,
        currentRoleId: PropTypes.string.isRequired,
        isSelectedAllByFilter: PropTypes.bool,
        isSelectedAll: PropTypes.bool,
        totalDocumentsCount: PropTypes.number,
        isAddCommentsPopupOpened: PropTypes.bool,
        isRejectPopupOpened: PropTypes.bool,
        isReviewPopupOpened: PropTypes.bool,
        isDeletePopupOpened: PropTypes.bool,
        isChangeRecipientPopupOpened: PropTypes.bool,
        isSendPopupOpened: PropTypes.bool,
        isMultiDownloadEnable: PropTypes.bool,
        isOverlimit: PropTypes.bool,
        isSignSummaryLoading: PropTypes.bool.isRequired,
        documentActions: PropTypes.object.isRequired,
        reviewResults: PropTypes.array.isRequired,
        selectedDocuments: PropTypes.array.isRequired,
        selectedDirectories: PropTypes.array.isRequired,
        sentResults: PropTypes.array.isRequired,
        sendPopupStatus: PropTypes.string.isRequired,
        errorMessage: PropTypes.string,
        sentDocCount: PropTypes.number.isRequired,
        onDownloadSignSummary: PropTypes.func.isRequired,
        onSendDocuments: PropTypes.func.isRequired,
        onSign: PropTypes.func.isRequired,
        onShowComments: PropTypes.func.isRequired,
        onReject: PropTypes.func.isRequired,
        onReviewDocuments: PropTypes.func.isRequired,
        onDeleteDocuments: PropTypes.func.isRequired,
        onDocumentLinksUpdate: PropTypes.func.isRequired,
        onRecipientChange: PropTypes.func.isRequired,
        onCommentDocuments: PropTypes.func.isRequired,
        onOpenPopup: PropTypes.func.isRequired,
        onClosePopup: PropTypes.func.isRequired,
        onMultiDownloadDocuments: PropTypes.func.isRequired,
        onOpenChangeDocumentPopup: PropTypes.func.isRequired,
        onOpenTagsEditPopup: PropTypes.func.isRequired,
        onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
        onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
        onFindEmailClick: PropTypes.func.isRequired,
        onCheckReviewAbility: PropTypes.func.isRequired,
        companyEdrpou: PropTypes.string,
        isOnlySignDocuments: PropTypes.bool,
        isOnlyExtraSignatureDocuments: PropTypes.bool,
        isOnlyNotReadyToSignAfterReviewDocuments: PropTypes.bool,
        isAccessReviews: PropTypes.bool,
        pathname: PropTypes.string,
        isNewKepSignFlowEnabled: PropTypes.bool,
    };

    handleAddComment = (isMultiAction, doc) => {
        if (isMultiAction) {
            this.props.onOpenPopup('commentsList');
        } else {
            this.props.onShowComments(doc.id);
        }
        eventTracking.sendEvent('comment', 'add_comment_btn_click');
    };

    handleOpenRejectPopup = (evt) => {
        evt.preventDefault();
        eventTracking.sendToGTM({
            event: 'click_book_demo_header',
            category: 'document_toolbar',
        });
        eventTracking.sendToGTM({
            event: 'click_reject',
            category: 'document_toolbar',
        });
        this.props.onOpenPopup('reject');
    };

    handleRecipientChange = (email, edrpou, isEmailHidden) => {
        this.props.onRecipientChange(
            this.props.selectedDocuments,
            email,
            edrpou,
            isEmailHidden,
        );
    };

    handleSendDocument = (email, edrpou, isEmailHidden) => {
        this.props.onSendDocuments(
            this.props.selectedDocuments,
            email,
            edrpou,
            isEmailHidden,
        );
    };

    filterRejectedDocuments = (doc) =>
        canRejectDocument(
            doc,
            this.props.companyEdrpou,
            this.props.currentRoleId,
        );

    handleFilterRejected = () => {
        this.props.dispatch(
            actionCreators.batchSelection(
                this.props.selectedDocuments
                    .filter(this.filterRejectedDocuments)
                    .map((item) => item.id),
            ),
        );
    };

    renderMainActions() {
        const props = this.props;

        const isKepDisallowToSignOverflowMaxLimit =
            props.isSelectedAllByFilter &&
            props.totalDocumentsCount > MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT;

        const {
            EXTERNAL_SEPARATED,
            INTERNAL_SEPARATED,
            INTERNAL_ASIC,
            INTERNAL_WRAPPED,
        } = SignatureFormat;
        const isKepDisabled = props.selectedDocuments.some(
            (doc) =>
                ![
                    EXTERNAL_SEPARATED,
                    ...(props.featureFlags
                        .ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT
                        ? [INTERNAL_SEPARATED, INTERNAL_ASIC, INTERNAL_WRAPPED]
                        : []),
                ].includes(doc.expectedSignatureFormat),
        );

        const canSignAndRejectDocument = props.hasPermission(
            'canSignAndRejectDocument',
        );
        const isMultiAction = props.selectedDocuments.length > 1;
        const isReviewNeededBeforeAction = getIsReviewNeededBeforeAction(
            props.selectedDocuments,
            props.companyEdrpou,
        );
        let mainButton;

        if (props.documentActions.send) {
            mainButton = (
                <div className={css.button}>
                    <Button
                        disabled={isReviewNeededBeforeAction}
                        size="small"
                        theme="cta"
                        onClick={() => props.onOpenPopup('send')}
                    >
                        {t`Надіслати`}
                    </Button>
                    {props.isSendPopupOpened && (
                        <SendPopup
                            isActive={props.isSendPopupOpened}
                            isMultiAction={isMultiAction}
                            isOverlimit={props.isOverlimit}
                            documents={props.selectedDocuments}
                            currentRoleId={props.currentRoleId}
                            sentResults={props.sentResults}
                            status={props.sendPopupStatus}
                            errorMessage={props.errorMessage}
                            sentDocCount={props.sentDocCount}
                            onSend={this.handleSendDocument}
                            onClose={() => props.onClosePopup('send')}
                        />
                    )}
                </div>
            );
        } else {
            const getButtonText = () => {
                if (props.isOnlyExtraSignatureDocuments) {
                    return t`Додати підпис`;
                }
                if (props.isOnlySignDocuments) {
                    return t`Підписати`;
                }
                return t`Підписати і надіслати`;
            };

            const buttonText = getButtonText();

            if (props.isNewKepSignFlowEnabled) {
                mainButton = (
                    <div className={css.button}>
                        <KepSignButton
                            onSign={() => {
                                eventTracking.sendToGTMV4({
                                    event:
                                        KEP_SIGN_FLOW_ANALYTICS_EVENT.SIGN_DOC_LIST_PAGE,
                                });
                                props.onSign(props.selectedDocuments, {
                                    isKepNew: !isKepDisabled,
                                });
                            }}
                            buttonTitle={buttonText}
                            size="small"
                        />
                    </div>
                );
            } else {
                mainButton = (
                    <>
                        <div className={css.button}>
                            <Button
                                size="small"
                                theme="cta"
                                onClick={() =>
                                    props.onSign(props.selectedDocuments)
                                }
                                dataQa="qa_sign_and_send_button"
                            >
                                {buttonText}
                            </Button>
                        </div>
                        {!isKepDisallowToSignOverflowMaxLimit &&
                            !isKepDisabled && (
                                <div className={css.button}>
                                    <KepSignButton
                                        disabled={isKepDisabled}
                                        onSign={() =>
                                            props.onSign(
                                                props.selectedDocuments,
                                                {
                                                    isKep: true,
                                                },
                                            )
                                        }
                                        buttonTitle={t`Підписати Вчасно.КЕП`}
                                        size="small"
                                    />
                                </div>
                            )}
                    </>
                );
            }

            if (
                (!isMultiAction && !props.documentActions.sign) ||
                props.isOnlyNotReadyToSignAfterReviewDocuments
            ) {
                mainButton = null;
            }
        }

        return (
            <>
                {mainButton}
                <div
                    className={css.buttonAdditional}
                    data-tour-id="rewiews-dropdown"
                    data-qa="qa_rewiews_dd"
                >
                    <DropButton
                        buttonList={[
                            {
                                label: t`Погодити документи`,
                                onClick: () => props.onOpenPopup('review'),
                            },
                            {
                                label: t`Додати співробітників для погодження`,
                                onClick: () =>
                                    props.onOpenChangeDocumentPopup({
                                        selectedItemIndex: 'reviewers',
                                    }),
                            },
                        ]}
                        onCheckToggleAbility={() => {
                            if (!props.isAccessReviews) {
                                eventTracking.sendToGTMV4({
                                    event: 'ec_approvedoc_mass_actions_trial',
                                });
                            }

                            return props.onCheckReviewAbility();
                        }}
                        interactiveIcon={
                            <ProLabel
                                permission={PermissionCategory.REVIEWS}
                                isShowLock
                                lockSize={16}
                            />
                        }
                    >
                        {t`Погодження`}
                    </DropButton>
                    {props.isReviewPopupOpened && (
                        <ReviewPopup
                            isActive={props.isReviewPopupOpened}
                            isMultiAction={isMultiAction}
                            documents={props.selectedDocuments}
                            reviewResults={props.reviewResults}
                            errorMessage={props.errorMessage}
                            reviewType={DocumentReviewType.APPROVE}
                            onReview={props.onReviewDocuments}
                            onClose={() => props.onClosePopup('review')}
                        />
                    )}
                </div>
                {props.documentActions.findRecipients && (
                    <div className={css.buttonAdditional}>
                        <Button
                            size="small"
                            typeContour
                            theme="blue"
                            onClick={() =>
                                props.onFindEmailClick(props.selectedDocuments)
                            }
                        >
                            {t`Підібрати email`}
                        </Button>
                        <Hint black position="left">
                            {t`За кодом ЄДРПОУ/ІПН сервіс підбере email-адреси вашого контрагента, якщо така компанія вже зареєстрована у ${config.BRAND_NAME}, або додана до переліку ваших контактів.`}
                        </Hint>
                    </div>
                )}
                {(isMultiAction || props.documentActions.reject) && (
                    <BlackTooltip
                        title={
                            props.documentActions.reject ? (
                                isMultiAction ? (
                                    t`Відхилити обрані документи`
                                ) : (
                                    t`Відхилити документ`
                                )
                            ) : (
                                <>
                                    {t`Деякі з обраних документів не можна відхилити.`}{' '}
                                    <Text
                                        style={{ color: 'inherit' }}
                                        onClick={this.handleFilterRejected}
                                        type="link"
                                    >{t`Обрати тільки ті, що можна відхилити?`}</Text>
                                </>
                            )
                        }
                    >
                        <div className={css.buttonAdditional}>
                            <Button
                                disabled={
                                    !canSignAndRejectDocument ||
                                    !props.documentActions.reject
                                }
                                size="small"
                                typeContour
                                theme="red"
                                onClick={this.handleOpenRejectPopup}
                            >
                                {t`Відхилити`}
                            </Button>
                        </div>
                    </BlackTooltip>
                )}
            </>
        );
    }

    render() {
        const isArchivePage = getIsArchivePageUtility(this.props.pathname);
        const props = this.props;
        const doc = props.selectedDocuments[0];
        const canSignAndRejectDocument = props.hasPermission(
            'canSignAndRejectDocument',
        );
        const selectedDocumentCount = props.selectedDocuments.length;
        const directories = props.selectedDirectories;
        const isDirectoriesSelected = directories.length > 0;
        const isMultiAction = props.selectedDocuments.length > 1;
        const isChangeRecipient = props.selectedDocuments.every(
            (d) => ['READY', 'SENT', 'SIGNED_AND_SENT'].indexOf(d.status) >= 0,
        );
        const changeRecipientButtonText = isChangeRecipient
            ? t`Змінити контрагента`
            : t`Призначити контрагента`;

        const hint = (
            <div className={css.hint}>
                <b>{t`Щоб видалити, оберіть документи зі статусами`}:</b>
                <br />- {t`Завантажені`}
                <br />- {t`Готові для підписання`}
                <br />- {t`Очікують вашого підпису`}
                <br />- {t`Відхилені контрагентом`}
            </div>
        );

        const renderAddListToDirectoryButton = () => (
            <AddListToDirectoryButton
                theme="secondary"
                selectedDocuments={props.selectedDocuments}
                selectedDirectories={props.selectedDirectories}
            />
        );

        if (isDirectoriesSelected) {
            return (
                <div className={css.root}>
                    <div className={css.cell}>
                        {isArchivePage && renderAddListToDirectoryButton()}
                    </div>
                    <div className={css.cellRight}>
                        <ActionTools
                            isDisabledDelete={
                                !props.documentActions.delete &&
                                !props.documentActions.deleteRequest
                            }
                            isDisabledPrint={!props.documentActions.print}
                            isDisabledDownload={!props.documentActions.download}
                            isDeletePopupOpened={props.isDeletePopupOpened}
                            isMultiDownloadEnable={props.isMultiDownloadEnable}
                            isShareDocumentEnabled={false}
                            isSignSummaryLoading={props.isSignSummaryLoading}
                            selectedDocuments={props.selectedDocuments}
                            selectedDirectories={props.selectedDirectories}
                            hint={hint}
                            errorMessage={props.errorMessage}
                            onDownloadSignSummary={props.onDownloadSignSummary}
                            onOpenDeletePopup={() =>
                                props.onOpenPopup('delete')
                            }
                            onCloseDeletePopup={() =>
                                props.onClosePopup('delete')
                            }
                            onDelete={props.onDeleteDocuments}
                            onMultiDownloadDocuments={
                                props.onMultiDownloadDocuments
                            }
                            isDeleteRequestAllowed={
                                props.documentActions.deleteRequest
                            }
                            isDeleteRequestReceiver={
                                props.documentActions.deleteRequestReceiver
                            }
                            isDeleteRequestInitiator={
                                props.documentActions.deleteRequestInitiator
                            }
                            onOpenCreateDeleteRequestPopup={
                                props.onOpenCreateDeleteRequestPopup
                            }
                            onOpenResolveDeleteRequestPopup={
                                props.onOpenResolveDeleteRequestPopup
                            }
                        />
                    </div>
                </div>
            );
        }

        return (
            <div className={css.root}>
                <div className={css.cell}>
                    {isArchivePage && renderAddListToDirectoryButton()}
                    {!isArchivePage && this.renderMainActions()}
                </div>
                <div className={css.cellRight}>
                    {!isArchivePage && (
                        <div className={css.button}>
                            <LinkDocumentsPopupButton
                                type="icon"
                                onUpdate={props.onDocumentLinksUpdate}
                                documents={props.selectedDocuments}
                                isDocPage={false}
                            />
                        </div>
                    )}
                    <BlackTooltip title={t`Ярлики`}>
                        <div className={css.button} data-tour-id="tags-icon">
                            <IconButton
                                svg={SvgTag}
                                onClick={props.onOpenTagsEditPopup}
                            />
                        </div>
                    </BlackTooltip>

                    <div className={css.button}>
                        <WithDocumentProcessesRenderer>
                            {({ isNewUpload }) => (
                                <ChangeDocumentButton
                                    onOpenChangeDocumentPopup={
                                        props.onOpenChangeDocumentPopup
                                    }
                                    onClick={
                                        isNewUpload
                                            ? () => {
                                                  eventTracking.sendToGTM({
                                                      event:
                                                          'new_settings_click',
                                                      category: 'doc_actions',
                                                  });
                                              }
                                            : undefined
                                    }
                                />
                            )}
                        </WithDocumentProcessesRenderer>
                    </div>
                    {props.documentActions.changeRecipient && !isArchivePage && (
                        <>
                            <BlackTooltip title={t`Змінити контрагента`}>
                                <div className={css.button}>
                                    <IconButton
                                        disabled={!canSignAndRejectDocument}
                                        svg={SvgPerson}
                                        onClick={() =>
                                            props.onOpenPopup('changeRecipient')
                                        }
                                    />
                                </div>
                            </BlackTooltip>
                            {props.isChangeRecipientPopupOpened && (
                                <ChangeRecipientPopup
                                    isChangeRecipientPopupOpened={
                                        props.isChangeRecipientPopupOpened
                                    }
                                    showHint={isChangeRecipient}
                                    buttonText={changeRecipientButtonText}
                                    email={
                                        isMultiAction ? '' : doc.emailRecipient
                                    }
                                    edrpou={
                                        isMultiAction ? '' : doc.edrpouRecipient
                                    }
                                    isEmailHidden={
                                        isMultiAction
                                            ? false
                                            : doc.isRecipientEmailHidden
                                    }
                                    documentTitle={
                                        isMultiAction ? undefined : doc.title
                                    }
                                    formTrackingName={
                                        isMultiAction
                                            ? 'mass_change_recipient'
                                            : undefined
                                    }
                                    onCloseChangeRecipientPopup={() =>
                                        props.onClosePopup('changeRecipient')
                                    }
                                    onChangeRecipient={
                                        this.handleRecipientChange
                                    }
                                />
                            )}
                        </>
                    )}
                    {props.documentActions.changeFlow && !isArchivePage && (
                        <BlackTooltip
                            title={t`Додати контрагентів`}
                            disableInteractive
                        >
                            <div className={css.button}>
                                <IconButton
                                    disabled={!canSignAndRejectDocument}
                                    svg={SvgPerson}
                                    onClick={() =>
                                        props.onOpenPopup('addRecipients')
                                    }
                                />
                            </div>
                        </BlackTooltip>
                    )}
                    {props.documentActions.comment && (
                        <BlackTooltip
                            title={t`Додати коментар`}
                            disableInteractive
                        >
                            <div className={css.button}>
                                <IconButton
                                    svg={SvgComment}
                                    onClick={() =>
                                        this.handleAddComment(
                                            isMultiAction,
                                            doc,
                                        )
                                    }
                                />
                            </div>
                        </BlackTooltip>
                    )}
                    <ActionTools
                        isDisabledDelete={
                            !props.documentActions.delete &&
                            !props.documentActions.deleteRequest
                        }
                        isDisabledPrint={!props.documentActions.print}
                        isDisabledDownload={!props.documentActions.download}
                        isDeletePopupOpened={props.isDeletePopupOpened}
                        isMultiDownloadEnable={props.isMultiDownloadEnable}
                        isShareDocumentEnabled={false}
                        isSignSummaryLoading={props.isSignSummaryLoading}
                        selectedDocuments={props.selectedDocuments}
                        selectedDirectories={props.selectedDirectories}
                        hint={hint}
                        errorMessage={props.errorMessage}
                        onDownloadSignSummary={props.onDownloadSignSummary}
                        onOpenDeletePopup={() => props.onOpenPopup('delete')}
                        onCloseDeletePopup={() => props.onClosePopup('delete')}
                        onDelete={props.onDeleteDocuments}
                        onMultiDownloadDocuments={
                            props.onMultiDownloadDocuments
                        }
                        isDeleteRequestAllowed={
                            props.documentActions.deleteRequest
                        }
                        isDeleteRequestReceiver={
                            props.documentActions.deleteRequestReceiver
                        }
                        isDeleteRequestInitiator={
                            props.documentActions.deleteRequestInitiator
                        }
                        onOpenCreateDeleteRequestPopup={
                            props.onOpenCreateDeleteRequestPopup
                        }
                        onOpenResolveDeleteRequestPopup={
                            props.onOpenResolveDeleteRequestPopup
                        }
                    />
                </div>
                <PromptPopup
                    isActive={props.isRejectPopupOpened}
                    title={ngettext(
                        msgid`Відхилення документу`,
                        `Відхилення документів`,
                        `Відхилення документів`,
                        selectedDocumentCount,
                    )}
                    label={t`Напишіть коментар контрагенту, чому ви вирішили відхилити документ`}
                    buttonText={ngettext(
                        msgid`Відхилити документ`,
                        `Відхилити документи`,
                        `Відхилити документи`,
                        selectedDocumentCount,
                    )}
                    emptyErrorMessage={t`Необхідно вказати причину відхилення документа`}
                    messageMaxLength={1000}
                    onClick={(comment) =>
                        props
                            .onReject(props.selectedDocuments, comment)
                            .then(() => {
                                snackbarToast.success(
                                    ngettext(
                                        msgid`Документ відхилено`,
                                        `Документи відхилено`,
                                        `Документи відхилено`,
                                        selectedDocumentCount,
                                    ),
                                );
                            })
                            .catch((error) => {
                                snackbarToast.error(
                                    ngettext(
                                        msgid`Помилка відхилення документу`,
                                        `Помилка відхилення документів`,
                                        `Помилка відхилення документів`,
                                        selectedDocumentCount,
                                    ) + `: "${getApiErrorMessage(error)}"`,
                                );
                            })
                    }
                    onClose={() => props.onClosePopup('reject')}
                />
            </div>
        );
    }
}

const mapStateToProps = (state) => ({
    ...mapStatetoHasPermission(state),
    companyEdrpou: state.app.currentUser?.currentCompany.edrpou,
    isOnlySignDocuments: getIsOnlySignDocumentsSelector(state),
    isOnlyExtraSignatureDocuments: getIsOnlyExtraSignatureDocumentsSelector(
        state,
    ),
    isOnlyNotReadyToSignAfterReviewDocuments: getIsOnlyNotReadyToSignAfterReviewDocumentsSelector(
        state,
    ),
    featureFlags: state.app.flags,
    pathname: state.router.location.pathname,
    isNewKepSignFlowEnabled: signWithKepFlowSelectors.selectIsFlowEnabled(
        state,
    ),
    isAccessReviews: getCurrentCompanyPermissionMap(state)[
        PermissionCategory.REVIEWS
    ],
});

export default connect(mapStateToProps)(DocumentListToolbar);
