import React, { Fragment, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@vchasno/ui-kit';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import AddListToDirectoryButton from 'components/documentList/AddListToDirectoryButton';
import PropTypes from 'prop-types';
import { getAppFlags, getCurrentCompanyEdrpou } from 'selectors/app.selectors';
import { getIsArchivePage } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { SignatureFormat } from 'services/enums';
import { t } from 'ttag';

import { getIsReviewNeededBeforeAction } from '../../helpers';

import IconButton from '../../../ui/iconButton/iconButton';
import PopupMobile from '../../../ui/popup/mobile/popup';

import KepSignButton from '../../../KepSigner/KepSignButton';
import { MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT } from '../../../KepSigner/constants';
import ActionTools from '../../../actionTools/actionTools';
import SendPopup from '../../../sendPopup/sendPopup';

import SvgComment from '../../images/comment.svg';

// styles
import css from './documentListToolbar.css';

const Toolbar = (props) => {
    const [showSignPopup, setShowPopup] = React.useState(false);
    const featureFlags = useSelector(getAppFlags);
    const currentCompanyEdrpou = useSelector(getCurrentCompanyEdrpou);
    const isNewKepSignFlowEnabled = useSelector(
        signWithKepFlowSelectors.selectIsFlowEnabled,
    );
    const isMultiAction = props.selectedDocuments.length > 1;
    const isArchivePage = useSelector(getIsArchivePage);

    const handleAddComment = () => {
        if (isMultiAction) {
            props.onOpenPopup('commentsList');
        } else {
            props.onShowComments(props.selectedDocuments[0].id);
        }
        eventTracking.sendEvent('comment', 'add_comment_btn_click');
    };

    const handleSendDocument = (email, edrpou, isEmailHidden) => {
        props.onSendDocuments(
            props.selectedDocuments,
            email,
            edrpou,
            isEmailHidden,
        );
    };
    const isReviewNeededBeforeAction = getIsReviewNeededBeforeAction(
        props.selectedDocuments,
        currentCompanyEdrpou,
    );

    const isKepDisallowToSignOverflowMaxLimit =
        props.isSelectedAllByFilter &&
        props.totalDocumentsCount > MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT;

    const { EXTERNAL_SEPARATED, INTERNAL_SEPARATED } = SignatureFormat;
    const isKepDisabled = props.selectedDocuments.some(
        (doc) =>
            ![
                EXTERNAL_SEPARATED,
                ...(featureFlags.ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT
                    ? [INTERNAL_SEPARATED]
                    : []),
            ].includes(doc.expectedSignatureFormat),
    );

    const signButton = useMemo(() => {
        if (isNewKepSignFlowEnabled) {
            return (
                <KepSignButton
                    buttonTitle={t`Підписати`}
                    size="sm"
                    onSign={() => {
                        eventTracking.sendToGTMV4({
                            event:
                                KEP_SIGN_FLOW_ANALYTICS_EVENT.SIGN_DOC_LIST_PAGE,
                        });
                        props.onSignDocuments(props.selectedDocuments, {
                            isKepNew: !isKepDisabled,
                        });
                    }}
                />
            );
        }

        return (
            <Button
                disabled={!props.documentActions.sign}
                size="sm"
                onClick={() => setShowPopup(true)}
            >
                {t`Підписати`}
            </Button>
        );
    }, [isNewKepSignFlowEnabled]);

    return (
        <div className={css.table}>
            <div className={css.cell}>
                <div className={css.table}>
                    <ActionTools
                        isSignSummaryLoading={props.isSignSummaryLoading}
                        isDisabledDelete={
                            !props.documentActions.delete &&
                            !props.documentActions.deleteRequest
                        }
                        isDisabledPrint={!props.documentActions.print}
                        isShareDocumentEnabled={false}
                        isDisabledDownload={!props.documentActions.download}
                        isDeletePopupOpened={props.isDeletePopupOpened}
                        selectedDocuments={props.selectedDocuments}
                        onDownloadSignSummary={props.onDownloadSignSummary}
                        onOpenDeletePopup={() => props.onOpenPopup('delete')}
                        onCloseDeletePopup={() => props.onClosePopup('delete')}
                        onDelete={props.onDeleteDocuments}
                        onOpenCreateDeleteRequestPopup={
                            props.onOpenCreateDeleteRequestPopup
                        }
                        onOpenResolveDeleteRequestPopup={
                            props.onOpenResolveDeleteRequestPopup
                        }
                        isDeleteRequestAllowed={
                            props.documentActions.deleteRequest
                        }
                        isDeleteRequestReceiver={
                            props.documentActions.deleteRequestReceiver
                        }
                        isDeleteRequestInitiator={
                            props.documentActions.deleteRequestInitiator
                        }
                    />
                    <div className={css.icon}>
                        <IconButton
                            disabled={!props.documentActions.comment}
                            title={t`Додати коментар`}
                            svg={SvgComment}
                            onClick={handleAddComment}
                        />
                    </div>
                </div>
            </div>
            <div className={css.tableRight}>
                <div className={css.counter}>
                    <b>{props.selectedCount}</b>
                </div>
                <div className={css.button}>
                    {isArchivePage && (
                        <AddListToDirectoryButton
                            theme="primary"
                            size="sm"
                            selectedDocuments={props.selectedDocuments}
                        />
                    )}
                    {!isArchivePage && (
                        <>
                            {props.documentActions.send ? (
                                <Fragment>
                                    <Button
                                        disabled={isReviewNeededBeforeAction}
                                        size="sm"
                                        onClick={() =>
                                            props.onOpenPopup('send')
                                        }
                                    >
                                        {t`Надіслати`}
                                    </Button>
                                    {props.isSendPopupOpened && (
                                        <SendPopup
                                            currentRoleId={props.currentRoleId}
                                            isActive={props.isSendPopupOpened}
                                            isMultiAction={
                                                props.selectedDocuments.length >
                                                1
                                            }
                                            isOverlimit={props.isOverlimit}
                                            documents={props.selectedDocuments}
                                            sentResults={props.sentResults}
                                            status={props.sendPopupStatus}
                                            errorMessage={props.errorMessage}
                                            sentDocCount={props.sentDocCount}
                                            onSend={handleSendDocument}
                                            onClose={() =>
                                                props.onClosePopup('send')
                                            }
                                        />
                                    )}
                                </Fragment>
                            ) : (
                                <>
                                    {signButton}
                                    <PopupMobile
                                        inPortal
                                        active={showSignPopup}
                                        onClose={() => setShowPopup(false)}
                                    >
                                        <h4 className={css.mobilePopupTitle}>
                                            {t`Підписати`}
                                        </h4>
                                        <div
                                            className={css.mobilePopupSubTitle}
                                        >{`${t`Вибрано`} ${
                                            props.selectedDocuments.length
                                        } ${
                                            props.selectedDocuments.length === 1
                                                ? t`документ`
                                                : t`документи`
                                        } ${t`до підписання`}`}</div>
                                        <div
                                            className={
                                                css.mobilePopupBtnContainer
                                            }
                                        >
                                            <Button
                                                disabled={
                                                    !props.documentActions.sign
                                                }
                                                wide
                                                size="sm"
                                                onClick={() => {
                                                    setShowPopup(false);
                                                    props.onSignDocuments(
                                                        props.selectedDocuments,
                                                    );
                                                }}
                                            >
                                                {t`Підписати ключем КЕП/ЕЦП`}
                                            </Button>
                                            {!isKepDisallowToSignOverflowMaxLimit &&
                                                !isKepDisabled && (
                                                    <KepSignButton
                                                        disabled={isKepDisabled}
                                                        width="full"
                                                        onSign={() => {
                                                            setShowPopup(false);
                                                            props.onSignDocuments(
                                                                props.selectedDocuments,
                                                                {
                                                                    isKep: true,
                                                                },
                                                            );
                                                        }}
                                                        buttonTitle={t`Підписати Вчасно.КЕП`}
                                                    />
                                                )}
                                        </div>
                                    </PopupMobile>
                                </>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

Toolbar.propTypes = {
    isAddCommentsPopupOpened: PropTypes.bool,
    isSendPopupOpened: PropTypes.bool,
    isDeletePopupOpened: PropTypes.bool,
    isOverlimit: PropTypes.bool,
    documentActions: PropTypes.object.isRequired,
    sendPopupStatus: PropTypes.string,
    errorMessage: PropTypes.string,
    sentDocCount: PropTypes.number,
    selectedCount: PropTypes.number,
    sentResults: PropTypes.array,
    selectedDocuments: PropTypes.array.isRequired,
    isSignSummaryLoading: PropTypes.bool.isRequired,
    onDownloadSignSummary: PropTypes.func.isRequired,
    onCommentDocuments: PropTypes.func.isRequired,
    onSignDocuments: PropTypes.func.isRequired,
    onDeleteDocuments: PropTypes.func.isRequired,
    onShowComments: PropTypes.func.isRequired,
    onOpenPopup: PropTypes.func.isRequired,
    onClosePopup: PropTypes.func.isRequired,
    onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
    onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
};

export default Toolbar;
