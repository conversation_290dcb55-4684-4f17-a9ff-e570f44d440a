.icon,
.closeIcon {
    position: relative;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.sidebar,
.sidebarAnimated {
    position: fixed;
    z-index: 101;
    top: 0;
    right: -500px;
    bottom: 0;
    display: flex;
    width: 500px;
    flex-direction: column;
    background-color: var(--white-bg);
    box-shadow: -2px 0 2px rgba(28, 54, 73, 0.11);
    overflow-x: auto;
    transition: transform 0.3s ease-out;
}

.sidebarAnimated {
    transform: translateX(-500px);
}

.header {
    padding: 30px;
    border-bottom: 1px solid var(--default-border);
}

.closeIcon {
    position: absolute;
    top: 20px;
    right: 20px;
}

.headerTitle {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
}

.block {
    display: grid;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--default-border);
    grid-column-gap: 20px;
    grid-template-columns: 70px auto;
    transition: background-color 0.3s ease-out;
}

.block:hover {
    background-color: var(--grey-bg);
}

.block:hover .subText {
    display: none;
}

.block:hover .buttons {
    display: block;
}

.image {
    justify-self: center;
}

.blockTitle {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

.subText {
    padding-right: 40px;
    color: var(--dark-grey-color);
    font-size: 14px;
}

.buttons {
    display: none;
    margin-top: 10px;
}

.subText,
.buttons {
    min-height: 45px;
}

.linkButton {
    display: inline-flex;
    min-height: 30px;
    box-sizing: border-box;
    align-items: center;
    padding: 7px 15px;
    border-radius: var(--border-radius);
    color: var(--content-color);
    font-size: 14px;
    text-decoration: none;
}

.linkButton + .linkButton {
    margin-left: 12px;
}

.linkButton:hover,
.linkButton:active {
    color: #333;
    text-decoration: none;
}

.videoLink {
    background-color: var(--corporate-color);
    color: #333;
}

.helpLink {
    background-color: var(--default-border);
}

.bottomBlock {
    padding: 20px;
    text-align: center;
}

.bottomLink {
    border-bottom: 1px dotted;
    color: var(--link-color);
    text-decoration: none;
}

.bottomLink:hover,
.bottomLink:active {
    border-color: transparent;
    text-decoration: none;
}
