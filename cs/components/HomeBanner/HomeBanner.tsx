import React, { FC, useEffect, useState } from 'react';
import { useMediaQuery } from 'react-responsive';

import cn from 'classnames';
import { useHomeBannerContext } from 'components/HomeBanner/HomeBanner.context';
import { LOCAL_STORAGE_KEY, images } from 'components/HomeBanner/constants';
import { HomeBannerProps } from 'components/HomeBanner/types';
import { MEDIA_WIDTH } from 'lib/constants';
import { setLocalStorageItem } from 'lib/webStorage';
import Icon from 'ui/icon';

import Cross from '../../icons/cross.svg';

import css from './HomeBanner.css';

export const HomeBanner: FC<HomeBannerProps> = ({ isSideBar, slide }) => {
    const { isClose, setIsClose } = useHomeBannerContext();
    const [slideIndex, setSlide] = useState(slide ? slide : 0);
    const isMobileOrTablet = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });
    const showSlide = images[slideIndex];

    const getImage = () => {
        if (isMobileOrTablet) {
            return showSlide.small;
        }

        if (isSideBar) {
            return showSlide.side;
        }

        return showSlide.big;
    };

    const clickCloseBtn = () => {
        setIsClose(true);
        setLocalStorageItem(LOCAL_STORAGE_KEY, true);
    };

    useEffect(() => {
        if (typeof slide === 'number') {
            return;
        }

        if (!isClose || isSideBar) {
            const timer = setInterval(() => {
                setSlide((prev) => {
                    return prev === images.length - 1 ? 0 : prev + 1;
                });
            }, 3_000);

            return () => {
                clearInterval(timer);
            };
        }
    }, []);

    if (isClose && !isSideBar) {
        return null;
    }

    return (
        <div
            className={cn(css.root, {
                [css.rootSideBar]: isSideBar,
            })}
        >
            <div className={css.wrapper}>
                <a
                    href="https://send.monobank.ua/jar/4n9LUJj2Dd"
                    target="_blank"
                    className={css.link}
                >
                    <img src={getImage()} alt="Banner" className={css.img} />
                </a>
                {!isSideBar && (
                    <span className={css.cross} onClick={clickCloseBtn}>
                        <Icon glyph={Cross} />
                    </span>
                )}
            </div>
        </div>
    );
};
