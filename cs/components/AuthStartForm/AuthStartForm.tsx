import React, { FC, useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { redirect } from 'lib/navigation';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import { getAuthFeatureFlags } from 'selectors/authFeatureFlags.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import { RegistrationProjectsSource } from '../registration/types';
import { AuthStartFields } from './types';

import {
    loadAuthFormStorageItem,
    saveAuthFormStorageItem,
} from '../auth/utils';

import Alert from '../ui/Alert/Alert';
import BackButton from '../ui/BackButton/BackButton';
import HorizontalLineSeparator from '../ui/HorizontalLineSeparator/HorizontalLineSeparator';
import Button from '../ui/button/button';
import OutlinedInput from '../ui/input/OutlinedInput/OutlinedInput';

import { LOGIN_MAX_LENGTH, PHONE_PATTERN } from '../../lib/constants';
import auth from '../../services/auth';
import PartnerLogos from '../AuthLayout/PartnerLogos';
import FlexBox from '../FlexBox/FlexBox';
import SessionExpiredPopup from '../SessionExpiredPopup';
import ThirdPartyAuthButtons from '../ThirdPartyAuthButtons';
import {
    HANDLED_ERRORS_BY_CODES,
    OBJECT_DOES_NOT_EXIST_ERROR_CODE,
    STATIC_ERROR_PHRASES,
} from '../auth/constants';
import PageTitle from '../pageTitle/pageTitle';
import { VALID_PROJECT_SOURCES } from '../registration/constants';
import { LANDINGS_REDIRECT_LINKS_MAP } from '../registration/hooks/useRegistrationRedirectLink';
import { useRegistrationSource } from '../registration/hooks/useRegistrationSource';
import { authCheckEmailResolver, startAuthResolver } from './validation';

import css from './AuthStartForm.css';

const AuthStartForm: FC = () => {
    const [commonErrorMessage, setCommonErrorMessage] = useState<string>('');
    const [
        isActiveSessionExpiredPopup,
        setIsActiveSessionExpiredPopup,
    ] = useState(false);

    const history = useHistory();
    const searchQuery = getLocationQuery(history.location);

    useEffect(() => {
        if (searchQuery.reason_logout === 'session_expired') {
            setIsActiveSessionExpiredPopup(true);
        }
    }, [searchQuery.reason_logout]);

    const onCloseSessionExpiredPopup = () =>
        setIsActiveSessionExpiredPopup(false);

    const isShowBackBtn = [...VALID_PROJECT_SOURCES, 'edo'].includes(
        (searchQuery?.source || '') as RegistrationProjectsSource,
    );

    const featureFlags = useSelector(getAuthFeatureFlags);
    const sourceLink = useRegistrationSource();
    const isTTNAppContext = sourceLink === 'ttn';
    const isShowThirdPartyAuthMethods = !isTTNAppContext;
    const isPhoneAuthSupported =
        isTTNAppContext && featureFlags.ENABLE_PHONE_AUTH_TTN;

    const resolver = isPhoneAuthSupported
        ? startAuthResolver
        : authCheckEmailResolver;

    const {
        control,
        handleSubmit,
        formState,
        setValue,
    } = useForm<AuthStartFields>({
        resolver,
        defaultValues: { login: '' },
    });

    useEffect(() => {
        eventTracking.sendToGTM({
            event: 'funnel_reg_step_1',
            action: '',
            category: '',
        });

        const authInitialForm = loadAuthFormStorageItem();
        if (authInitialForm?.login) {
            setValue('login', authInitialForm.login);
        }
    }, []);

    const onSubmit: SubmitHandler<AuthStartFields> = async ({ login }) => {
        try {
            setCommonErrorMessage('');

            const isPhoneCandidate =
                isPhoneAuthSupported && PHONE_PATTERN.test(login);
            if (isPhoneCandidate) {
                saveAuthFormStorageItem({
                    authType: 'phone',
                    login: login,
                });
                try {
                    await auth.sendPhoneAuthCode({ phone: login });
                    history.push({
                        pathname: '/auth/phone/verify',
                        search: stringifyLocationQuery(searchQuery),
                    });
                } catch (error: any) {
                    setCommonErrorMessage(
                        error.reason ||
                            error.message ||
                            STATIC_ERROR_PHRASES.COMMON_ERROR,
                    );
                }
                return;
            }

            saveAuthFormStorageItem({
                authType: 'email',
                login: login,
            });
            await auth.checkUserIsRegistered(login);
            history.push({
                pathname: '/auth/login',
                search: stringifyLocationQuery(searchQuery),
            });
        } catch (error: any) {
            if (error.code === OBJECT_DOES_NOT_EXIST_ERROR_CODE) {
                eventTracking.sendToGTM({
                    event: 'funnel_reg_step_2_email',
                    action: 'success',
                    category: '',
                });
                history.push({
                    pathname: '/auth/registration',
                    search: stringifyLocationQuery(searchQuery),
                });
                return;
            }
            eventTracking.sendToGTM({
                event: 'funnel_reg_step_2_email',
                action: 'fail',
                category: '',
            });
            const errorMessage = HANDLED_ERRORS_BY_CODES.includes(error.code)
                ? error.reason
                : STATIC_ERROR_PHRASES.COMMON_ERROR;
            setCommonErrorMessage(errorMessage);
        }
    };

    const onBackHandler = () => {
        const url =
            LANDINGS_REDIRECT_LINKS_MAP[
                searchQuery?.source as keyof typeof LANDINGS_REDIRECT_LINKS_MAP
            ];
        if (url) {
            redirect(url);
        }
    };

    const handleAuthError = (error: { code: string; reason: string }) => {
        setCommonErrorMessage(error.reason);
    };

    /**
     * @see https://web.dev/articles/sign-in-form-best-practices for more details
     */
    return (
        <form
            id="login"
            style={{ height: '100%' }}
            onSubmit={handleSubmit(onSubmit)}
        >
            <PageTitle>{t`Вітаємо у Вчасно`}</PageTitle>
            <FlexBox
                className={css.container}
                align="center"
                justify="center"
                gap={0}
            >
                <div className={css.content}>
                    <FlexBox className={css.header} direction="column" gap={12}>
                        {isShowBackBtn && (
                            <BackButton
                                className={css.backBtn}
                                onClick={onBackHandler}
                            />
                        )}
                        <PartnerLogos className={css.partnersLogos} />
                        <h1>
                            <span role="img" aria-label={t`Вітаємо`}>
                                👋
                            </span>{' '}
                            {t`Вітаємо у Вчасно`}
                        </h1>
                        <h4>{t`Авторизуйтеся у Вчасно за допомогою облікового запису або Email`}</h4>
                    </FlexBox>
                    <div>
                        {isShowThirdPartyAuthMethods && (
                            <>
                                <ThirdPartyAuthButtons
                                    microsoftAuthOn
                                    googleAuthOn
                                    appleAuthOn
                                    onError={handleAuthError}
                                />
                                <HorizontalLineSeparator
                                    className={css.horizontalLineSeparator}
                                >
                                    {t`або`}
                                </HorizontalLineSeparator>
                            </>
                        )}

                        <Controller
                            control={control}
                            name="login"
                            render={({ field, fieldState }) => (
                                <OutlinedInput
                                    id="login"
                                    type="text"
                                    name="login"
                                    autoComplete="email"
                                    inputMode="email"
                                    label={
                                        isPhoneAuthSupported
                                            ? t`Email або номер телефону`
                                            : t`Email`
                                    }
                                    value={field.value}
                                    onChange={(event) =>
                                        field.onChange(
                                            event.target.value.trim(),
                                        )
                                    }
                                    autoFocus
                                    maxLength={LOGIN_MAX_LENGTH + 1}
                                    error={fieldState.error?.message}
                                />
                            )}
                        />
                    </div>
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <Button
                        className={css.submitBtn}
                        type="submit"
                        theme="darkGray"
                        isLoading={formState.isSubmitting}
                        disabled={formState.isSubmitting}
                    >{t`Продовжити`}</Button>
                </div>
            </FlexBox>

            <SessionExpiredPopup
                isActive={isActiveSessionExpiredPopup}
                onClose={onCloseSessionExpiredPopup}
            />
        </form>
    );
};

export default AuthStartForm;
