.table {
    width: 100%;
    background-color: var(--white-bg);
}

.table td,
.table th {
    padding: 15px 20px;
}

.table tr {
    border-bottom: 1px solid var(--default-border);
}

.table th {
    font-weight: bold;
    text-align: left;
}

.tableRow {
    border-top: 1px solid var(--default-border);
}

.titleContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
}

.icon,
.actionIcon {
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
}


.actionIcon {
    cursor: pointer;
}

.actionIcon + .actionIcon {
    margin-left: 10px;
}

.color,
.yellowColor,
.greenColor,
.blueColor {
    display: inline-block;
    width: 40px;
    height: 20px;
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
}

.yellowColor {
    background-color: #fffcde;
}

.greenColor {
    background-color: #e3f5ee;
}

.blueColor {
    background-color: #e0ecfa;
}

.textGray {
    color: var(--dark-pigeon-color);
}

.link {
    word-break: break-word;
}
