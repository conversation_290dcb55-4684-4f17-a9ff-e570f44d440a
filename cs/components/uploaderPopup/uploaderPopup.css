.root {
    display: flex;
    width: 740px;
    min-height: 540px;
}

.title {
    max-width: 240px;
    font-size: 18px;
    font-weight: bold;
}

.menu {
    margin-top: 20px;
}

.item {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    cursor: pointer;
}

.item + .item {
    margin-top: 6px;
}

.content {
    flex: 1;
    margin-bottom: 20px;
}

.radioButtons {
    position: relative;
    z-index: 1;
    margin-bottom: 15px;
}

.itemActive {
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    font-weight: bold;
}

.sidebar {
    min-width: 300px;
    box-sizing: border-box;
    padding: 40px 30px;
}

.settings,
.settingsPadded,
.settingsSmallPad {
    display: flex;
    min-width: 380px;
    flex-direction: column;
    padding: 85px 30px 40px;
    background-color: var(--white-bg);
    border-radius: 0 8px 8px 0;
}

.settingsPadded {
    padding-top: 112px;
}

.settingsSmallPad {
    padding-top: 36px;
}

.sourceBlock {
    display: table;
    margin: 0 auto 20px;
}

.message {
    margin-top: 6px;
}

.label {
    display: block;
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: bold;
}

.checkbox {
    margin-top: 20px;
}

.fields {
    display: flex;
}

.field + .field {
    margin-left: 30px;
}

.info {
    margin-top: 20px;
    color: var(--dark-pigeon-color);
}

.userBillet {
    max-width: 85%;
}

.proLabel {
    display: inline-block;
    margin-left: 5px;
}

.proRatePrice {
    display: grid;
    margin-top: 30px;
    grid-row-gap: 20px;
}

.disabledListItem {
    color: var(--dark-pigeon-color);
}

.boxTitle {
    margin-bottom: 20px;
    font-size: 22px;
    font-weight: 500;
}

.icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.expandableList {
    padding-top: 20px;
    border-top: 1px solid var(--pigeon-color);
    margin-top: 15px;
}

.expandableList ~ .expandableList {
    padding-top: 0;
    border-top: none;
    margin-top: 0;
}
