.root {
    position: relative;
    color: var(--slate-grey-color);
}

:global(.vchasno-dark-theme) .root {
    --white-bg: var(--dark-1-color);
}

.label {
    margin-bottom: 5px;
    color: var(--slate-grey-color);
    font-size: 14px;
    line-height: 25px;
}

.input {
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
    padding: 15px 15px;
    border: 1px solid var(--default-border);
    -moz-appearance: none;
    -webkit-appearance: none;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    box-shadow: none;
    color: var(--content-color);
    font-size: 13px;
    line-height: 18px;
    outline: 2px solid transparent;
    transition: border 0.3s, outline 0.3s;
    vertical-align: middle;
}

.input:disabled {
    background-color: var(--grey-bg);
    color: var(--dark-pigeon-color);
}

.input:disabled:hover {
    cursor: default;
}

.input:not(:disabled):hover,
.input:focus {
    border-color: var(--vchasno-ui-pagination-border-color);
    outline: 2px solid var(--vchasno-ui-input-outline-color-focused);
}

.root:focus-within .leadingIcon {
    color: var(--vchasno-ui-pagination-border-color);
}

.input[type='password'] {
    padding: 15px 35px 15px 15px;
}

.input[type='number'] {
    -moz-appearance: textfield;
}

.input::-webkit-outer-spin-button,
.input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

.input::-ms-clear {
    display: none;
}

.input[type='password']::-ms-reveal {
    display: none;
}

.inputBorderNone {
    border-color: #fff;
}

.inputBorderNone:disabled {
    border-color: #f3f6f8;
    background-color: var(--grey-bg);
}

.inputBorderNone:focus {
    border-color: #fff;
}

.inputError {
    border-color: var(--error-border);
}

.inputError:focus {
    border-color: var(--error-border);
}

.inputValid {
    border-color: var(--green-color);
}

.inputValid:focus {
    border-color: var(--green-color);
}

.inputSmall {
    padding: 5px 15px;
}

.inputBig {
    padding: 15px;
}

.inputOpened {
    border-color: var(--primary-cta-color);
}

.withLeadingIcon {
    padding-left: 40px;
}

.withClearButton {
    padding-right: 30px;
}

.text {
    color: #eee;
}

.hint,
.hintActive {
    display: block;
    margin-top: 5px;
    color: var(--dark-pigeon-color);
    font-size: 11px;
}

.hintActive {
    color: var(--vchasno-ui-pagination-border-color);
}

.validation,
.valid,
.error {
    display: block;
    margin-top: 5px;
    font-size: 11px;
}

.error {
    color: var(--red-color);
}

.valid {
    color: var(--green-color);
}

.leadingIcon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 20px;
    height: 20px;
    margin: auto;
}

.icon {
    position: absolute;
    top: 0;
    right: 10px;
    bottom: 0;
    width: 20px;
    height: 20px;
    margin: auto;
}

.icon.iconClose {
    right: 15px;
    width: 10px;
    cursor: pointer;
}

.iconChecked {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 100%;
    width: 20px;
    height: 20px;
    margin: auto 0 auto 10px;
}

.passwordIcon {
    width: 30px;
    height: 30px;
    box-sizing: border-box;
    padding: 5px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.passwordIcon:hover {
    background-color: var(--grey-bg);
}

@media screen and (max-width: 768px) {
    .iconChecked {
        margin: auto 0 auto 5px;
    }
}

.positionLeft {
    border-right: 0;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.inputHidden {
    display: none;
}

.inputWrapper {
    position: relative;
}

@media all and (max-width: 480px) {
    .input {
        line-height: 28px;
    }
}
