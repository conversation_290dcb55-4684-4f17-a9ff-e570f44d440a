import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { FlexBox, Pagination, Text } from '@vchasno/ui-kit';

import { useShowProRatePopupHandlerWrapper } from 'hooks/useShowProRatePopupHandlerWrapper';
import { openInNewTab } from 'lib/navigation';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import {
    deleteDocumentTemplate,
    getDocumentTemplates,
} from 'services/documentTemplates';
import { t } from 'ttag';
import Button from 'ui/button/button';
import { confirmPopupConfigProps } from 'ui/confirmPopup/confirmPopup';
import Dropdown from 'ui/dropdown/dropdown';
import Input from 'ui/input/input';

import { Template } from './types';

import {
    filterDocumentTemplateBySearch,
    getCurrentDocumentTemplates,
    switchTemplateAutomationStatus,
} from './utils';

import { PermissionCategory } from '../proRateInfoPopup/proRateInfoPopupTypes';
import { TEMPLATES_PER_PAGE_OPTIONS } from './constants';
import DocumentTemplate from './documentTemplate';
import DocumentTemplateEditPopup from './documentTemplateEditPopup';
import SuccessPopup from './successPopup';

import SearchSvg from './images/search.svg';

import css from './documentTemplatesList.css';

interface Props {
    onConfirmPopupShow: (popupProps: confirmPopupConfigProps) => void;
    onAlertPopupShow: (title: string, content: string) => void;
}

const DocumentTemplatesList: FC<React.PropsWithChildren<Props>> = (
    props: Props,
) => {
    const isAccess = useSelector(getCurrentCompanyPermissionMap)[
        PermissionCategory.TEMPLATES
    ];
    const showProRatePopupHandlerWrapper = useShowProRatePopupHandlerWrapper();
    const [documentTemplates, setDocumentTemplates] = useState<Template[]>([]);
    const prevSearchTemplate = useRef<string>('');
    const [searchTemplate, setSearchTemplate] = useState('');
    const [editPopupOpen, setEditPopupOpen] = useState<boolean>(false);
    const [editingTemplate, setEditingTemplate] = useState<
        Template | undefined
    >(undefined);
    const [successPopupData, setSuccessPopupData] = useState<
        Template | undefined
    >(undefined);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [templatesPerPage, setTemplatesPerPage] = useState<number>(
        TEMPLATES_PER_PAGE_OPTIONS[0].value,
    );

    const updateTemplatesList = async () => {
        const newTemplates = await getDocumentTemplates();
        setDocumentTemplates(newTemplates);
        return newTemplates;
    };

    useEffect(() => {
        updateTemplatesList().catch((_) => _);
    }, []);

    const onCloseEditForm = () => {
        setEditPopupOpen(false);
        setEditingTemplate(undefined);
    };

    const onSubmitEditForm = async (templateId?: string) => {
        const templateList = await updateTemplatesList();
        const templateData = templateId
            ? templateList.find(({ id }: Template) => id === templateId)
            : templateList[templateList.length - 1];

        onCloseEditForm();
        setSuccessPopupData(templateData);
    };

    const onCreateTemplate = showProRatePopupHandlerWrapper(
        () => {
            setEditPopupOpen(true);
        },
        'document-settings/templates/create',
        PermissionCategory.TEMPLATES,
    );

    const onCopyTemplate = showProRatePopupHandlerWrapper(
        (data: Template) => {
            const dataForCopy = {
                ...data,
                id: '',
                name: `${t`Копія`} ${data.name}`,
            };
            setEditingTemplate(dataForCopy);
            setEditPopupOpen(true);
        },
        'document-settings/templates/create',
        PermissionCategory.TEMPLATES,
    );

    const onEditTemplate = showProRatePopupHandlerWrapper(
        (data: Template) => {
            setEditingTemplate(data);
            setEditPopupOpen(true);
        },
        'document-settings/templates/edit',
        PermissionCategory.TEMPLATES,
    );

    const onDeleteTemplateConfirm = async (id: string) => {
        try {
            await deleteDocumentTemplate(id);
            eventTracking.sendEvent('sign-and-coordination-script', 'delete');
        } catch (err) {
            props.onAlertPopupShow(
                t`Виникла помилка при видаленні сценарію.`,
                err.message,
            );
        }
        await updateTemplatesList();
    };

    const onDeleteTemplate = (template: Template) => {
        props.onConfirmPopupShow({
            title: t`Ви впевнені, що хочете видалити сценарій "${template.name}"?`,
            text: t`Сценарій не можна буде застосувати до нових документів.`,
            buttonText: t`Видалити`,
            buttonTheme: 'danger',
            onConfirm: () => onDeleteTemplateConfirm(template.id),
        });
    };

    const onToggleTemplateStatus = showProRatePopupHandlerWrapper(
        async (template: Template) => {
            await switchTemplateAutomationStatus(template);
            const templates = await updateTemplatesList();
            if (successPopupData) {
                const data = templates.find(
                    (temp: Template) => temp.id === template.id,
                );
                setSuccessPopupData(data);
            }
        },
        '',
        PermissionCategory.TEMPLATES,
    );

    const renderTemplate = (data: Template) => (
        <DocumentTemplate
            key={data.id}
            data={data}
            onCopy={onCopyTemplate}
            onEdit={onEditTemplate}
            onDelete={onDeleteTemplate}
            onToggleStatus={onToggleTemplateStatus}
        />
    );

    const handleChangeTemplate = (
        event: React.ChangeEvent<HTMLInputElement>,
    ) => {
        setSearchTemplate((prevState) => {
            prevSearchTemplate.current = prevState;
            return event.currentTarget.value;
        });
    };

    const handleChangeCurrentPaginationPage = (page: number) =>
        setPageNumber(page);

    const handleChangeTemplatesPerPage = (newTemplatesPerPage: number) => {
        const firstTemplatePage = (pageNumber - 1) * templatesPerPage + 1;
        setPageNumber(Math.ceil(firstTemplatePage / newTemplatesPerPage));
        setTemplatesPerPage(newTemplatesPerPage);
    };

    const filteredDocumentTemplates = useMemo(() => {
        if (prevSearchTemplate.current !== searchTemplate) {
            setPageNumber(1);
        }

        return documentTemplates.filter((template) => {
            return filterDocumentTemplateBySearch(searchTemplate, template);
        });
    }, [searchTemplate, documentTemplates]);

    const currentPageDocumentTemplates = useMemo(() => {
        return getCurrentDocumentTemplates(
            pageNumber,
            templatesPerPage,
            filteredDocumentTemplates,
        );
    }, [pageNumber, templatesPerPage, filteredDocumentTemplates]);

    return (
        <>
            <FlexBox direction="column" gap={5} align="flex-start">
                <Text type="secondary">{t`Ви можете налаштувати послідовність погодження та підписання, за яким буде проходити документ, залежно від умов.`}</Text>
                <Text
                    type="link"
                    onClick={() =>
                        openInNewTab(
                            'https://www.youtube.com/watch?v=grXtqsdUO2U',
                        )
                    }
                >{t`Як створити сценарій документів?`}</Text>
            </FlexBox>
            <div className={css.controlsContainer}>
                {isAccess && (
                    <div className={css.searchInput}>
                        <Input
                            leadingIcon={SearchSvg}
                            placeholder={t`Введіть назву сценарія, email або ім'я користувача`}
                            onChange={handleChangeTemplate}
                        />
                    </div>
                )}
                <Button
                    theme="blue"
                    onClick={() => {
                        onCreateTemplate();

                        if (!isAccess) {
                            eventTracking.sendToGTMV4({
                                event: 'ec_scripts_features_settings_trial',
                            });
                        }
                    }}
                >
                    {t`Створити сценарій`}
                </Button>
            </div>

            {isAccess && (
                <>
                    {currentPageDocumentTemplates.map(renderTemplate)}

                    {editPopupOpen && (
                        <DocumentTemplateEditPopup
                            editingTemplate={editingTemplate}
                            onSubmit={onSubmitEditForm}
                            onClose={onCloseEditForm}
                        />
                    )}
                    {successPopupData && (
                        <SuccessPopup
                            data={successPopupData}
                            onClose={() => setSuccessPopupData(undefined)}
                            onToggleStatus={onToggleTemplateStatus}
                        />
                    )}
                    <div className={css.listFooter}>
                        <div className={css.pagination}>
                            <Pagination
                                gapStep={
                                    filteredDocumentTemplates.length > 100
                                        ? 10
                                        : 5
                                }
                                scrollOnChange
                                total={Math.ceil(
                                    filteredDocumentTemplates.length /
                                        templatesPerPage,
                                )}
                                current={pageNumber}
                                hideOnSinglePage
                                onChange={handleChangeCurrentPaginationPage}
                            />
                        </div>
                        <div className={css.itemsPerPage}>
                            <div className={css.itemsPerPageTitle}>
                                {t`Кількість сценаріїв на сторінці`}
                            </div>
                            <div className={css.itemsPerPageDropdown}>
                                <Dropdown
                                    options={TEMPLATES_PER_PAGE_OPTIONS}
                                    value={templatesPerPage}
                                    onChange={handleChangeTemplatesPerPage}
                                />
                            </div>
                        </div>
                    </div>
                </>
            )}
        </>
    );
};

export default DocumentTemplatesList;
