.root {
    width: 100%;
    height: 100%;
    background: linear-gradient(
        80.35deg,
        #eef6ff 0.21%,
        #fbefff 49.78%,
        #fff2ed 99.34%
    );
}

.phoneContainer {
    position: relative;
    width: 70%;
    height: 70%;
    margin: 20% auto 0;
}

.stars {
    position: absolute;
    top: -30px;
    left: -20px;
}

.infoContainer {
    position: fixed;
    z-index: 100;
    bottom: 0;
    left: 0;
    height: 50%;
    padding: 30px 20px;
    border: 1px solid white;
    backdrop-filter: blur(5px);
    background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.59) 0%,
        rgba(255, 255, 255, 0.94) 15%,
        rgba(255, 255, 255, 0.99) 40%
    );
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.edoLogo {
    width: 50px;
    margin-bottom: 10px;
}

.subtitle {
    color: var(--slate-grey-color);
    font-size: 14px;
}

.buttonWrapper {
    width: 40%;
    margin-top: 30px;
}
