import { useFormContext } from 'react-hook-form';

import { InfoForm } from 'components/DocumentsUploader/DocumentInfo/types';
import { DocumentsUploadForm } from 'components/DocumentsUploader/types';
import {
    fetchDocumentRequiredFields,
    getRequiredFields,
} from 'components/DocumentsUploader/utils';
import { t } from 'ttag';

type InfoFields = keyof InfoForm;

type DocumentRequiredFieldsHandlerForm = Pick<
    DocumentsUploadForm,
    'counterparties' | 'companyEdrpou' | InfoFields
>;

// Перевіряє заповненість полів, які вимагаються від отримувачів-контрагентів
export const useCheckRequiredFieldsHandler = () => {
    const methods = useFormContext<DocumentRequiredFieldsHandlerForm>();
    const isSomeInfoFiledEmpty: boolean =
        methods.getValues('amount') !== null ||
        !methods.getValues('category') ||
        !methods.getValues('title') ||
        !methods.getValues('number') ||
        !methods.getValues('date');

    return async () => {
        // якщо всі поля заповнені, то не потрібно робити запиту про перевірку заповненості обовязкових полів
        if (!isSomeInfoFiledEmpty) {
            return [];
        }

        const companyEdrpou = methods.getValues('companyEdrpou');
        const counterparties = methods.getValues('counterparties');
        const category = methods.getValues('category');

        const recipientCounterparties = counterparties.filter(
            (item) => item.edrpou !== companyEdrpou,
        );
        const uniqueRecipientCounterparties = [
            ...new Set(recipientCounterparties.map((item) => item.edrpou)),
        ];

        // якщо відсутні контрагенти отримувачі, то не потрібно робити запиту про перевірку заповненості обовязкових полів
        if (uniqueRecipientCounterparties.length === 0) {
            return [];
        }

        const companiesFieldRules = await fetchDocumentRequiredFields(
            uniqueRecipientCounterparties,
        );

        const requiredFieldsRules = getRequiredFields(
            companiesFieldRules,
            category,
        );
        const requiredFields: InfoFields[] = [];

        if (
            requiredFieldsRules?.isNumberRequired &&
            !methods.getValues('number')
        ) {
            requiredFields.push('number');
        }

        if (requiredFieldsRules?.isDateRequired && !methods.getValues('date')) {
            requiredFields.push('date');
        }

        if (
            requiredFieldsRules?.isAmountRequired &&
            methods.getValues('amount') === null
        ) {
            requiredFields.push('amount');
        }

        if (
            requiredFieldsRules?.isTypeRequired &&
            !methods.getValues('category')
        ) {
            requiredFields.push('category');
        }

        if (requiredFields.length > 0) {
            requiredFields.forEach((field) => {
                methods.setError(field, {
                    type: 'requiredFields',
                    message: t`Контрагент вимагає заповнити поле`,
                });
            });
        }

        return requiredFields;
    };
};
