import React from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import FlexBox from 'components/FlexBox';
import { t } from 'ttag';
import Switch from 'ui/Switch';
import Icon from 'ui/icon';

import FormController from '../FormController';

import SvgAlarm from '../../../icons/alarm.svg';

import css from './CounterpartySection.css';

interface OrderSignSectionProps {
    disabled?: boolean;
}

const OrderSignSection: React.FC<OrderSignSectionProps> = ({ disabled }) => {
    return (
        <FlexBox gap={5}>
            <FormController
                name="isParallel"
                render={({ field }) => (
                    <Switch
                        disabled={disabled}
                        onChange={field.onChange}
                        value={field.value}
                        label={t`Застосувати паралельне підписання`}
                    />
                )}
            />

            <BlackTooltip
                disableInteractive
                title={t`У разі вибору опції "Застосувати паралельне підписання" документи можуть бути підписані в будь-якому порядку.`}
            >
                <span className={css.alarmIcon}>
                    <Icon glyph={SvgAlarm} />
                </span>
            </BlackTooltip>
        </FlexBox>
    );
};

export default OrderSignSection;
