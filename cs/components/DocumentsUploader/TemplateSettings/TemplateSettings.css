.select {
    --vchasno-ui-input-border-color-default: var(--default-border);
    --vchasno-ui-input-border-color-focused: var(--link-color);
    --vchasno-ui-select-menu-border-color: var(--link-color);
}


.select :global(.vchasno-ui-meta) {
    display: none;
}

.select:global(.vchasno-ui-select) :global(.vchasno-ui-select__control) {
    min-width: 220px;
    min-height: 40px;
    outline: 0;
}

.select:hover {
    cursor: pointer;
}

.placeholder {
    display: flex;
    cursor: pointer;
    gap: 10px;
}

.select .placeholder:global(.vchasno-ui-select__placeholder) {
    color: var(--link-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
}

.placeholder > svg {
    width: 20px;
    height: 20px;
    color: inherit;
    transition: width 0.3s, opacity 0.3s;
}

.placeholder.focused {
    gap: 0;
    opacity: 0.2;
}

.placeholder.focused > svg {
    width: 0;
    opacity: 0;
}

@media all and (max-width: 480px) {
    .select {
        width: 100%;
    }

    .select :global(.vchasno-ui-select__value-container) {
        justify-content: center;
    }
}
