import React from 'react';
import { UseFieldArrayReturn } from 'react-hook-form';

import { EditDocumentForm } from 'components/DocumentEdit/types';
import { MultiEditDocumentForm } from 'components/MultiDocumentEditModal/types';
import ChosenGroupItem from 'components/uploaderPopup/SignersReviewersAutosuggestionList/ChosenListItems/ChosenGroupItem';
import ChosenRoleItem from 'components/uploaderPopup/SignersReviewersAutosuggestionList/ChosenListItems/ChosenRoleItem';
import SelectedDraggableList from 'ui/SelectedDraggableList/SelectedDraggableList';
import SelectedItemsList, {
    SelectedItemsListProps,
} from 'ui/SelectedItemsList';

import { DocumentsUploadForm } from './types';

import { useUploadDocumentsFormContext } from './useUploadDocumentsFormContext';

export interface SelectedActorListProps {
    actorFieldArray:
        | UseFieldArrayReturn<DocumentsUploadForm, 'employeesSigners'>
        | UseFieldArrayReturn<DocumentsUploadForm, 'employeesViewers'>
        | UseFieldArrayReturn<MultiEditDocumentForm, 'employeesViewers'>
        | UseFieldArrayReturn<EditDocumentForm, 'employeesSigners'>
        | UseFieldArrayReturn<EditDocumentForm, 'employeesReviewers'>
        | UseFieldArrayReturn<EditDocumentForm, 'employeesViewers'>
        | UseFieldArrayReturn<DocumentsUploadForm, 'employeesReviewers'>;
    isOrdered?: boolean;
    disabled?: boolean;
    startIndex?: number;
    disableDelete?: (index: number) => boolean;
    disableReorder?: (index: number) => boolean;
    onRemove?: (id: string) => void;
}

const SelectedActorList: React.FC<SelectedActorListProps> = ({
    actorFieldArray,
    isOrdered,
    disabled,
    startIndex,
    disableDelete,
    disableReorder,
    onRemove,
}) => {
    const methods = useUploadDocumentsFormContext();
    const { fields, swap, remove } = actorFieldArray;

    const items: SelectedItemsListProps['items'] = fields.map((item, index) => {
        if (item.type === 'role') {
            return {
                id: item.id,
                fields: [<ChosenRoleItem fetchIsActive role={item.source} />],
                isDisabledRemove: disableDelete?.(index),
                isDisableReOrder: disableReorder?.(index),
            };
        }
        return {
            id: item.id,
            fields: [<ChosenGroupItem group={item.source} />],
            isDisabledRemove: disableDelete?.(index),
            isDisableReOrder: disableReorder?.(index),
        };
    });

    const handleRemove = (_: React.MouseEvent<HTMLDivElement>, id: string) => {
        remove(fields.indexOf(fields.find((item) => item.id === id)!));
        methods.trigger('counterparties');
        onRemove?.(id);
    };

    if (items.length === 0) {
        return null;
    }

    const onRearrangeItem = (dragIndex: number, hoverIndex: number) => {
        if (disableReorder?.(dragIndex) || disableReorder?.(hoverIndex)) {
            return;
        }
        swap(dragIndex, hoverIndex);
    };

    if (!isOrdered) {
        return (
            <SelectedItemsList
                disabledActions={methods.formState.disabled || disabled}
                items={items}
                onRearrangeItem={onRearrangeItem}
                onRemoveItem={handleRemove}
            />
        );
    }

    return (
        <SelectedDraggableList
            disabledActions={methods.formState.disabled || disabled}
            isOrdered
            items={items}
            onRearrangeItem={onRearrangeItem}
            onRemoveItem={handleRemove}
            startIndex={startIndex}
        />
    );
};

export default SelectedActorList;
