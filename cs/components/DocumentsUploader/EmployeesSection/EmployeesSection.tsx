import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

import { Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import AccessLevelGroupAlert from 'components/DocumentsUploader/EmployeesShareSection/AccessLevelGroupAlert';
import FlexBox from 'components/FlexBox';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import Button from 'ui/button/button';

import {
    EmployeesForms,
    EmployeesSectionTab,
    EmployeesSectionTabConfig,
} from './types';

import { tabItems } from './tabs';

import css from './EmployeesSection.css';

type EmployeesSectionDisabledProps = Partial<
    Record<EmployeesSectionTab, boolean>
>;

interface EmployeesSectionProps {
    disabledFields?: EmployeesSectionDisabledProps;
    tabs?: ReadonlyArray<EmployeesSectionTabConfig>;
    children?: (active: EmployeesSectionTabConfig['key']) => React.ReactNode;
    hideEmptyPlaceholder?: boolean;
    initTabKey?: EmployeesSectionTab;
}

const EmployeesSection: React.FC<EmployeesSectionProps> = ({
    disabledFields = {} as EmployeesSectionDisabledProps,
    tabs = tabItems,
    children = () => null,
    hideEmptyPlaceholder,
    initTabKey,
}) => {
    const [activeTab, setActiveTab] = React.useState<EmployeesSectionTab>(
        initTabKey || tabs[0].key,
    );
    const methods = useFormContext<EmployeesForms>();
    const { errors } = methods.formState;
    const { Component } = tabs.find((tab) => tab.key === activeTab)!;

    const employeesErrorMsg =
        errors.employeesReviewers?.message ||
        errors.employeesSigners?.message ||
        errors.employeesViewers?.message ||
        errors.employeesSigners?.root?.message ||
        errors.employeesReviewers?.root?.message ||
        errors.employeesViewers?.root?.message ||
        '';

    useEffect(() => {
        // автоматично перемикаємо на вкладку з помилкою
        if (errors.employeesReviewers) {
            setActiveTab('employeesReviewers');
        }
    }, [errors.employeesReviewers]);

    return (
        <FlexBox direction="column" gap={32}>
            <FlexBox gap={8} align="center" className={css.tabsWrapper}>
                {tabs.map((tab) => (
                    <Button
                        type="button"
                        theme="blue"
                        typeContour={tab.key !== activeTab}
                        key={tab.key}
                        className={cn(css.btn, {
                            [css.active]: tab.key === activeTab,
                        })}
                        onClick={() => setActiveTab(tab.key)}
                    >
                        <FlexBox tagName="span" gap={8} align="center">
                            <span className={css.icon}>{tab.icon}</span>
                            {tab.label(methods)}
                        </FlexBox>
                    </Button>
                ))}
            </FlexBox>
            <Component
                hideEmptyPlaceholder={hideEmptyPlaceholder}
                disabled={disabledFields[activeTab]}
            />
            {employeesErrorMsg && (
                <Alert scrollIntoView theme="warning">
                    <Text strong>{t`Зверніть увагу!`}</Text>{' '}
                    <Text>{employeesErrorMsg}</Text>
                </Alert>
            )}
            {children(activeTab)}
            <AccessLevelGroupAlert />
        </FlexBox>
    );
};

export default EmployeesSection;
