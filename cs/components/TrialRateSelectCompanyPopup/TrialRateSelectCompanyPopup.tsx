import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { FlexBox } from '@vchasno/ui-kit';

import Popup from 'components/ui/popup/popup';
import { getIsTrialRateSelectCompanyPopupActive } from 'selectors/trialRateSelectCompanyPopup.selectors';
import { updateUserTrialAutoEnabledField } from 'services/user';
import { setTrialRateSelectCompanyPopupOpen } from 'store/trialRateSelectCompanyPopupSlice';
import { t } from 'ttag';

import TrialSelectCompanyList from './TrialSelectCompanyList';

import css from './TrialRateSelectCompanyPopup.css';

const TrialRateSelectCompanyPopup: React.FC = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getIsTrialRateSelectCompanyPopupActive);

    const handleClosePopup = async () => {
        dispatch(setTrialRateSelectCompanyPopupOpen(false));
    };

    return (
        <Popup fullContent active={isOpen} onClose={handleClosePopup}>
            <FlexBox direction="column" gap={15} className={css.root}>
                <h2 className={css.title}>{t`Активувати тестовий період`}</h2>
                <h4
                    className={css.subTitle}
                >{t`Оберіть компанію для якої будуть  увімкнені всі
функції сервісу на період 14ти днів`}</h4>
                <ul className={css.companyListWrapper}>
                    <TrialSelectCompanyList closePopup={handleClosePopup} />
                </ul>
            </FlexBox>
        </Popup>
    );
};

export default TrialRateSelectCompanyPopup;
