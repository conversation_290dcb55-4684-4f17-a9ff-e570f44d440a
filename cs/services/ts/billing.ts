import {
    MAX_RATES_SET,
    PRO_RATES_SET,
    START_RATES_SET,
} from 'services/billing/constants';
import { AccountRate } from 'services/enums';
import io from 'services/io';

import { CompanyRate } from '../../types/billing';

export const changeBillPaymentStatus = async ({
    billID,
    status,
}: {
    billID: string;
    status: string;
}) => {
    return await io.patch('/api/private/billing/cancel-bill', {
        bill_id: billID,
        status: status,
    });
};

export type EmployeesNumber =
    | 'range_1_49'
    | 'range_50_249'
    | 'range_250_499'
    | 'range_500_plus';

interface ContactSalesManagerPayload {
    employeesNumber: EmployeesNumber;
}

export const contactSalesManagerWhenEmployeeLimitReached = async (
    payload?: ContactSalesManagerPayload,
) => {
    return io.post('/internal-api/contact-manager', payload);
};

export const getMostExpensiveRate = (rates: CompanyRate[] = []) => {
    return (
        rates.find((rate) => MAX_RATES_SET.has(rate.rate)) ||
        rates.find((rate) => PRO_RATES_SET.has(rate.rate)) ||
        rates.find((rate) => START_RATES_SET.has(rate.rate)) ||
        rates.find((rate) => rate.rate === AccountRate.FREE)
    );
};
