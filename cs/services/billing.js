import { getRateTotalDocuments } from 'components/RatesPopup/helpers';
import { isFopCompany } from 'lib/company';
import { INACTIVE_RATE_STATUSES_SET, QUERY_DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import {
    getGoogleAnalyticsClientId,
    getGoogleAnalyticsSessionData,
} from 'lib/googleAnalytics/utils';
import { getIsArchiveRate, getIsIntegrationRate } from 'lib/rates/utils';
import moment from 'moment';
import { t } from 'ttag';

import { AccountRate, AccountType } from './enums';

import {
    INTEGRATION_RATES_SET,
    PRO_RATES_SET,
    PRO_TRIALS_SET,
    UNLIMITED_RATES_SEND_DOCS_FOR_FOP,
    UNLIMITED_RATES_SET,
} from './billing/constants';
import io from './io';

export {
    contactSalesManagerWhenEmployeeLimitReached,
    getMostExpensiveRate,
} from './ts/billing';

export const ACCOUNT_RATE_TITLE_MAP = {
    [AccountRate.OLD]: t`Тариф "Old"`,
    [AccountRate.ENTERPRISE]: t`Тариф "Enterprise"`,
    [AccountRate.FREE]: t`Базовий`,
    [AccountRate.PRO]: t`Безлімітний`,
    [AccountRate.PRO_TRIAL]: t`Професійний (тестовий)`,
    [AccountRate.PRO_PLUS_TRIAL]: t`ПРО+ (тестовий)`,
    [AccountRate.PRO_PLUS_TRIAL_2022]: t`Тестовий період (веб)`,
    [AccountRate.PRO_PLUS_TRIAL_2022_12]: t`Тестовий період (веб)`,
    [AccountRate.PRO_FREE]: t`Професійний (безкоштовний)`,
    [AccountRate.INTEGRATION]: t`Інтеграція`,
    [AccountRate.INTEGRATION_TRIAL]: t`Тестовий період (API)`,
    [AccountRate.START]: t`Старт`,
    [AccountRate.START_2022_01]: t`Старт`,
    [AccountRate.START_2022_04]: t`Старт`,
    [AccountRate.START_2022_08]: t`Старт`,
    [AccountRate.START_2023_10]: t`Старт`,
    [AccountRate.START_2024_10]: t`Старт`,
    [AccountRate.START_2025_04]: t`Старт`,
    [AccountRate.PRO_2021]: t`Професійний`,
    [AccountRate.PRO_2022]: t`Професійний`,
    [AccountRate.PRO_2022_01]: t`Професійний`,
    [AccountRate.PRO_2022_04]: t`Професійний`,
    [AccountRate.PRO_2022_12]: t`Професійний`,
    [AccountRate.PRO_2023_07]: t`Професійний`,
    [AccountRate.PRO_2023_10]: t`Професійний`,
    [AccountRate.PRO_2024_10]: t`Професійний`,
    [AccountRate.PRO_2025_04]: t`Професійний`,
    [AccountRate.PRO_PLUS]: t`ПРО+`,
    [AccountRate.PRO_PLUS_2022_04]: t`ПРО+`,
    [AccountRate.PRO_PLUS_2022_12]: t`ПРО+`,
    [AccountRate.ULTIMATE]: t`Максимальний`,
    [AccountRate.ULTIMATE_2022_12]: t`Максимальний`,
    [AccountRate.ARCHIVE_SMALL]: t`Архів +1000`,
    [AccountRate.ARCHIVE_BIG]: t`Архів +5000`,
    [AccountRate.ADD_EMPLOYEE]: t`Додаткові користувачі`,
};

export const ACCOUNT_RATE_SHORT_TITLE_MAP = {
    [AccountRate.OLD]: t`Тариф "Old"`,
    [AccountRate.ENTERPRISE]: t`Тариф "Enterprise"`,
    [AccountRate.FREE]: t`Базовий`,
    [AccountRate.PRO]: t`Професійний`,
    [AccountRate.PRO_TRIAL]: t`Професійний`,
    [AccountRate.PRO_PLUS_TRIAL]: t`ПРО+`,
    [AccountRate.PRO_PLUS_TRIAL_2022]: t`ПРО+`,
    [AccountRate.PRO_PLUS_TRIAL_2022_12]: t`ПРО+`,
    [AccountRate.PRO_FREE]: t`Професійний`,
    [AccountRate.INTEGRATION]: t`Інтеграція`,
    [AccountRate.INTEGRATION_TRIAL]: t`Інтеграція`,
    [AccountRate.INTEGRATION_WITH_DOCUMENTS]: t`Інтеграція`,
    [AccountRate.FILL_INTEGRATION]: t`Інтеграція`,
    [AccountRate.START]: t`Старт`,
    [AccountRate.START_2022_01]: t`Старт`,
    [AccountRate.START_2022_04]: t`Старт`,
    [AccountRate.START_2022_08]: t`Старт`,
    [AccountRate.START_2023_10]: t`Старт`,
    [AccountRate.START_2024_10]: t`Старт`,
    [AccountRate.START_2025_04]: t`Старт`,
    [AccountRate.PRO_2021]: t`Професійний`,
    [AccountRate.PRO_2022]: t`Професійний`,
    [AccountRate.PRO_2022_01]: t`Професійний`,
    [AccountRate.PRO_2022_04]: t`Професійний`,
    [AccountRate.PRO_2022_12]: t`Професійний`,
    [AccountRate.PRO_2023_07]: t`Професійний`,
    [AccountRate.PRO_2023_10]: t`Професійний`,
    [AccountRate.PRO_2024_10]: t`Професійний`,
    [AccountRate.PRO_2025_04]: t`Професійний`,
    [AccountRate.PRO_PLUS]: t`ПРО+`,
    [AccountRate.PRO_PLUS_2022_04]: t`ПРО+`,
    [AccountRate.PRO_PLUS_2022_12]: t`ПРО+`,
    [AccountRate.ULTIMATE]: t`Максимальний`,
    [AccountRate.ULTIMATE_2022_12]: t`Максимальний`,
    [AccountRate.ARCHIVE_SMALL]: t`Архів +1000`,
    [AccountRate.ARCHIVE_BIG]: t`Архів +5000`,
    [AccountRate.ADD_EMPLOYEE]: t`Співробітники`,
};

export const ALL_START_RATES = [
    AccountRate.START,
    AccountRate.START_2022_01,
    AccountRate.START_2022_04,
    AccountRate.START_2022_08,
    AccountRate.START_2023_10,
    AccountRate.START_2024_10,
    AccountRate.START_2025_04,
];

export const DEPRECATED_PRO_RATES = [AccountRate.PRO];

export const ALL_PRO_RATES = [
    AccountRate.PRO_2021,
    AccountRate.PRO_2022,
    AccountRate.PRO_2022_12,
    AccountRate.PRO_2022_01,
    AccountRate.PRO_2022_04,
    AccountRate.PRO_2022_12,
    AccountRate.PRO_2023_07,
    AccountRate.PRO_2023_10,
    AccountRate.PRO_2024_10,
    AccountRate.PRO_2025_04,
];

export const ALL_UNLIMITS_RATES = [
    AccountRate.PRO,
    AccountRate.ULTIMATE,
    AccountRate.ULTIMATE_2022_12,
];

export const ALL_MAXIMAL_RATES = [
    AccountRate.ULTIMATE,
    AccountRate.ULTIMATE_2022_12,
];

const balanceLeftReduce = (acc, { unitsLeft }) => acc + unitsLeft;
const balanceSentReduce = (acc, { unitsLeft, units }) =>
    units - unitsLeft + acc;

export function countBalance(billingAccounts) {
    // Calculate debit units
    const debitAccounts = billingAccounts.filter(
        ({ type, status }) =>
            type === AccountType.CLIENT_DEBIT &&
            !INACTIVE_RATE_STATUSES_SET.has(status),
    );
    const documentsLeft = debitAccounts.reduce(balanceLeftReduce, 0);
    const documentsSent = debitAccounts.reduce(balanceSentReduce, 0);
    const documentsLeftOldRate = debitAccounts
        .filter(({ rate }) => rate === AccountRate.OLD)
        .reduce(balanceLeftReduce, 0);
    const documentsLeftIntegrationRate = debitAccounts
        .filter(({ rate }) => rate === AccountRate.INTEGRATION)
        .reduce(balanceLeftReduce, 0);

    // Calculate bonus units
    const bonusesAccounts = billingAccounts.filter(
        ({ type, status }) =>
            (type === AccountType.CLIENT_BONUS ||
                type === AccountType.CLIENT_BONUS_CUSTOM) &&
            !INACTIVE_RATE_STATUSES_SET.has(status),
    );
    const bonusesLeft = bonusesAccounts.reduce(balanceLeftReduce, 0);
    const bonusesSent = bonusesAccounts.reduce(balanceSentReduce, 0);
    const bonusesLeftOldRate = bonusesAccounts
        .filter(({ rate }) => rate === AccountRate.OLD)
        .reduce(balanceLeftReduce, 0);
    const bonusesLeftIntegrationRate = bonusesAccounts
        .filter(({ rate }) => rate === AccountRate.INTEGRATION)
        .reduce(balanceLeftReduce, 0);

    return {
        documentsLeft,
        documentsLeftOldRate,
        documentsLeftIntegrationRate,
        bonusesLeft,
        bonusesLeftOldRate,
        bonusesLeftIntegrationRate,
        documentsSent,
        bonusesSent,
        total: documentsLeft + bonusesLeft,
        totalSent: documentsSent + bonusesSent,
    };
}

export async function cancelBilling(data) {
    return await io.request('POST', '/internal-api/billing/debit/cancel', data);
}

export async function cancelBonus(data) {
    return await io.request(
        'POST',
        '/internal-api/billing/bonuses/cancel',
        data,
    );
}

export async function createBillingAccount(data) {
    return await io.request(
        'POST',
        '/internal-api/billing/debit/activate',
        data,
    );
}

export async function createBonusAccount(data) {
    return await io.request(
        'POST',
        '/internal-api/billing/bonuses/activate-custom',
        data,
    );
}

export async function activeDocumentsFromBill(billId) {
    return await io.request('POST', `/internal-api/bills/${billId}/activate`);
}

export const isOneOfTheStartRates = (rate) => ALL_START_RATES.includes(rate);

export const isOneOfTheProRates = (rate) => ALL_PRO_RATES.includes(rate);

export async function createBill(data) {
    const googleAnalyticsSessionData = getGoogleAnalyticsSessionData();
    const googleAnalyticsClientId = getGoogleAnalyticsClientId();

    let newData = {
        email: data.email,
        edrpou: data.edrpou,
        name: data.name,
        agreement: data.agreement,
        is_card_payment: data.is_card_payment,

        ...(!data.is_card_payment && {
            ...googleAnalyticsSessionData,
            ...googleAnalyticsClientId,
        }),
    };

    // convert "rate" and "count_documents" to "services", new format to create bill
    if (data.rate && data.rate === AccountRate.INTEGRATION_WITH_DOCUMENTS) {
        newData.services = [
            { type: 'rate', rate: AccountRate.INTEGRATION },
            { type: 'units', units: data.count_documents },
        ];
    } else if (data.employee_count) {
        newData.services = [
            {
                type: 'extension',
                extension: 'employees',
                units: data.employee_count,
            },
        ];
    } else if (data.rate) {
        newData.services = [{ type: 'rate', rate: data.rate }];
        if (data.selectedArchiveExtend) {
            newData.services.push({
                type: 'rate',
                rate: data.selectedArchiveExtend,
            });
        }
    } else if (!!data.count_documents) {
        newData.services = [{ type: 'units', units: data.count_documents }];
    } else {
        // TODO: add new types of bills:
        //  - web + archive, example: [{ type: 'rate', rate: AccountRate.ARCHIVE_SMALL }, { type: 'rate', rate: AccountRate.START }]
        //  - add extension, example: [{ type: 'extension', extension: 'employee', units: 10 }]
        throw new Error('Cannot create bill without rate or count_documents');
    }
    return await io.post('/internal-api/bills', newData);
}

/**
 * @typedef {object} BillStatusResponseData
 * @property {string} id
 * @property {string} edrpou
 * @property {BillStatus | null} status_id
 * @property {StatusType | null} payment_status
 * @property {string | null} payment_status_description
 * @property {AccountRate | null} rate
 * @property {number | null} count_documents
 * @property {string | null} document_id
 */

/**
 * @param {string} billId
 * @returns {Promise<BillStatusResponseData>}
 */
export async function getBill(billId) {
    return await io.getAsJson(`/internal-api/bills/${billId}`);
}

export const getRatesWithoutSomeRate = (rates, rateToRemove) =>
    rates?.filter((rate) => rate?.rate !== rateToRemove);

export function needRatesBeCombined(activeRates) {
    return activeRates.some((rate) =>
        [
            ...ALL_START_RATES,
            ...ALL_PRO_RATES,
            AccountRate.PRO_PLUS_TRIAL_2022,
            AccountRate.PRO_PLUS_TRIAL_2022_12,
        ].includes(rate),
    );
}

export const getCombinedActiveRates = (activeRates) => {
    const activeRatesWithoutFree = activeRates.filter(
        (rate) => rate !== AccountRate.FREE,
    );
    if (
        activeRatesWithoutFree.includes(AccountRate.PRO_PLUS_TRIAL_2022) ||
        activeRatesWithoutFree.includes(AccountRate.PRO_PLUS_TRIAL_2022_12)
    ) {
        return activeRatesWithoutFree.filter(
            (rate) => ![...ALL_START_RATES, ...ALL_PRO_RATES].includes(rate),
        );
    }
    return activeRatesWithoutFree;
};

export const getBillingAccountsWithoutFree = (billingAccounts) =>
    billingAccounts.filter(({ rate }) => rate !== AccountRate.FREE);

export const isShowFreeRate = (activeRates) => {
    const activeRatesLength = activeRates.reduce(
        (acc, rate) => [...acc, rate],
        [],
    ).length;
    if (
        activeRates.includes(AccountRate.PRO_PLUS_TRIAL_2022) ||
        activeRates.includes(AccountRate.PRO_PLUS_TRIAL_2022_12)
    ) {
        return false;
    }
    return (
        (activeRatesLength === 2 &&
            [...INTEGRATION_RATES_SET].some((apiRates) =>
                activeRates.includes(apiRates),
            )) ||
        activeRatesLength === 1
    );
};

export function getCompanyRatesTitles(currentUser) {
    const activeRates = currentUser.currentCompany.activeRates;

    // Backend doesn't return free rates
    if (activeRates.length === 0)
        return [ACCOUNT_RATE_TITLE_MAP[AccountRate.FREE]];

    const combinedActiveRates = isShowFreeRate(activeRates)
        ? activeRates
        : getCombinedActiveRates(activeRates);
    return combinedActiveRates.reduce((acc, rate) => {
        const rateTitle = ACCOUNT_RATE_TITLE_MAP[rate];
        return rateTitle ? [...acc, rateTitle] : acc;
    }, []);
}

export function isProCompany(currentUser) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some((r) =>
        PRO_RATES_SET.has(r),
    );
}

export function isUltimateCompany(currentUser) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some(
        (r) => AccountRate.ULTIMATE === r || AccountRate.ULTIMATE_2022_12 === r,
    );
}

export function hasPayedRate(currentUser, ignoreIntegration = false) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    if (!ignoreIntegration) {
        return currentUser.currentCompany.activeRates.some(
            (r) => r !== AccountRate.FREE,
        );
    } else {
        return currentUser.currentCompany.activeRates.some(
            (r) => r !== AccountRate.FREE && !INTEGRATION_RATES_SET.has(r),
        );
    }
}

export const hasOnlyStartTariffOrIntegration = (activeRates) => {
    if (!activeRates) {
        return false;
    }

    const allowedTariffs = [AccountRate.FREE, ...INTEGRATION_RATES_SET];

    return activeRates.every((rate) => allowedTariffs.includes(rate));
};

/**
 * @param {IUser} currentUser
 * @returns {boolean}
 */
export function hasActiveTrialRate(currentUser) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some((r) =>
        PRO_TRIALS_SET.has(r),
    );
}

/**
 * @param {string} endDate
 * @returns {number}
 */
export function getRateDaysLeftByEndDate(endDate) {
    const today = moment();
    const endDateMoment = moment(endDate);
    const rateDaysLeft = moment.duration(endDateMoment.diff(today)).asDays();
    return Math.ceil(rateDaysLeft);
}

/**
 * @param {Rate} rate
 * @returns {boolean}
 */
export function getIsRateExpires(rate) {
    if (!rate) {
        return false;
    }

    const endDate = rate?.endDate;
    const isTrial = PRO_TRIALS_SET.has(rate.rate);
    const expiresDateRate = isTrial ? 3 : 30;

    if (!endDate) {
        return false;
    }

    const rateDaysLeft = getRateDaysLeftByEndDate(endDate);

    return rateDaysLeft <= expiresDateRate;
}

/**
 * @param {Object} rate
 */
export function checkIsUnlimitedRate(rate) {
    return UNLIMITED_RATES_SET.has(rate.rate);
}

export function hasUnlimitedRate(currentUser) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some((r) =>
        UNLIMITED_RATES_SET.has(r),
    );
}

export function hasUnlimitedSendDocsRate(currentUser) {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some(
        (r) =>
            UNLIMITED_RATES_SET.has(r) ||
            (isFopCompany({ edrpou: currentUser.currentCompany.edrpou }) &&
                UNLIMITED_RATES_SEND_DOCS_FOR_FOP.has(r)),
    );
}

export const getRateDocumentsLeft = (
    billingAccounts,
    rate,
    rateStatus,
    initialValue = 0,
) => {
    if (!rate) {
        return 0;
    }
    if (checkIsUnlimitedRate(rate)) {
        return 'unlimited';
    }

    const currentBillingAccounts = billingAccounts
        .filter(
            (account) =>
                account.status === 'active' ||
                INTEGRATION_RATES_SET.has(rate.rate),
        )
        .filter((account) => account.rate === rate.rate)
        .filter((account) =>
            rateStatus ? rateStatus === account.status : true,
        );

    const currentBillingAccountsUnitsLeft = currentBillingAccounts?.map(
        (account) => account.unitsLeft,
    );

    return currentBillingAccountsUnitsLeft?.length
        ? currentBillingAccountsUnitsLeft.reduce(
              (previousValue, currentValue) => previousValue + currentValue,
              initialValue,
          )
        : 0;
};

export const getRateDocumentsTotal = (billingAccounts, rate) => {
    if (!rate) {
        return 0;
    }
    if (UNLIMITED_RATES_SET.has(rate.rate)) {
        return 'unlimited';
    }

    const currentBillingAccounts = billingAccounts
        .filter(
            (account) =>
                account.status === 'active' ||
                INTEGRATION_RATES_SET.has(rate.rate),
        )
        .filter((account) => account.rate === rate.rate);

    const currentBillingAccountsTotalUnits = currentBillingAccounts?.map(
        (account) => account.units,
    );

    return currentBillingAccountsTotalUnits?.length
        ? currentBillingAccountsTotalUnits.reduce(
              (previousValue, currentValue) => previousValue + currentValue,
              0,
          )
        : 0;
};

export const getWebRates = (rates) =>
    rates?.filter(
        (rate) =>
            !getIsIntegrationRate(rate.rate) && !getIsArchiveRate(rate.rate),
    );

export const isRateDateTerminate = (rate) =>
    moment(rate?.endDate).isBetween(moment(), moment().add(1, 'month'));

/**
 * @param {CompanyRate[]} activeRates
 */
export const isTrialRatesInActiveRates = (activeRates) =>
    activeRates.some(({ rate }) =>
        [
            AccountRate.INTEGRATION_TRIAL,
            AccountRate.PRO_PLUS_TRIAL_2022,
            AccountRate.PRO_PLUS_TRIAL_2022_12,
        ].includes(rate),
    );

export const isRateDocumentsTerminate = (billingAccounts, rate) => {
    if (!rate || UNLIMITED_RATES_SET.has(rate.rate)) {
        return false;
    }

    const documentsLeft = getRateDocumentsLeft(billingAccounts, rate);

    if (rate.rate === AccountRate.FREE) {
        return documentsLeft <= 10;
    }
    if (
        rate.rate === AccountRate.START_2022_04 ||
        rate.rate === AccountRate.START_2022_08 ||
        rate.rate === AccountRate.START_2023_10 ||
        rate.rate === AccountRate.START_2024_10
    ) {
        return documentsLeft <= 25;
    }
    if (
        rate.rate === AccountRate.START ||
        rate.rate === AccountRate.START_2022_01
    ) {
        return documentsLeft <= 100;
    }
    if (PRO_RATES_SET.has(rate.rate) || PRO_TRIALS_SET.has(rate.rate)) {
        // станом на 2025-05-27 Для ФОП доступно безлімітна кількість документів на тарифі Професійний
        const totalDocuments = getRateTotalDocuments(rate, billingAccounts);

        // Якщо значення 0, то не потрібно перевіряти (Безліміт кількість документів)
        if (!totalDocuments) {
            return false;
        }

        return documentsLeft <= 200;
    }
    if (INTEGRATION_RATES_SET.has(rate.rate)) {
        const totalDocuments = getRateDocumentsTotal(billingAccounts, rate);

        const fifteenPercent = totalDocuments * 0.15;

        return documentsLeft < fifteenPercent;
    }

    return false;
};

export const getActiveRates = (rates) =>
    rates?.filter((rate) => rate?.status === 'active');

// We have 2 different types of rates: "integration" and "web"
export const getTrialIntegrationRate = (rates) =>
    rates?.find((rate) => rate.rate === 'integration_trial');

export const getTrialWebRate = (rates) =>
    rates?.find(
        (rate) =>
            rate.rate.includes('trial') && rate.rate !== 'integration_trial',
    );

export const getActiveRatesWithOldSameRates = (activeRates) => {
    const rates = activeRates ? activeRates.toJS() : [];
    const activeRatesWithOldSameRates = rates.reduce((acc, rate) => {
        if (isOneOfTheStartRates(rate)) {
            return acc.concat(ALL_START_RATES);
        }
        if (isOneOfTheProRates(rate)) {
            return acc.concat(ALL_PRO_RATES);
        }
        return acc;
    }, rates);
    return [...new Set(activeRatesWithOldSameRates)];
};

export const getPayedRates = (rates) =>
    rates?.filter((rate) => rate.rate !== AccountRate.FREE);

export const getFreeRate = (rates) =>
    rates?.find((rate) => rate.rate === AccountRate.FREE);

export const getActiveIntegrationRate = (rates) =>
    rates?.find((rate) => rate?.status === 'active');

export const getPlannedRates = (rates = []) =>
    rates.filter((rate) => rate?.status === 'new');

export const getActiveBillingRates = (rates) =>
    rates
        ?.filter((rate) => rate?.rate !== 'free')
        .filter((rate) => rate?.status === 'active');

export const getIntegrationRates = (rates) =>
    rates?.filter((rate) => rate?.rate === 'integration');

export const getMostCheapRate = (rates) =>
    !!rates?.length &&
    rates.reduce((acc, curr) => (acc.amount < curr.amount ? acc : curr));

export const getRatesForEventTracking = (ratesName) =>
    ratesName?.map((rate) => ACCOUNT_RATE_TITLE_MAP[rate]);

export async function createRate({
    companyId,
    rateType,
    startDate,
    endDate,
    employeeCount,
    price,
}) {
    return await io.post('/internal-api/billing/companies/rates', {
        company_id: companyId,
        rate: rateType,
        employee_amount: employeeCount,
        start_date: formatDate(startDate, QUERY_DATE_FORMAT),
        end_date: formatDate(endDate, QUERY_DATE_FORMAT),
        amount: price * 100,
    });
}

export async function activeRateFromBill(billId, { startDate, endDate }) {
    return await io.post(`/internal-api/bills/${billId}/activate`, {
        start_date: formatDate(startDate, QUERY_DATE_FORMAT),
        end_date: formatDate(endDate, QUERY_DATE_FORMAT),
    });
}

export async function updateRate(rateId, { startDate, endDate }) {
    return await io.patch(`/internal-api/billing/companies/rates/${rateId}`, {
        start_date: formatDate(startDate, QUERY_DATE_FORMAT),
        end_date: formatDate(endDate, QUERY_DATE_FORMAT),
    });
}

export async function increaseEmloyeesAmountForUltimateRate(
    rateId,
    { employeeAmount, pricePerEmployee },
) {
    return await io.patch(
        `/internal-api/billing/companies/rates/${rateId}/employee-amount`,
        {
            employee_amount: employeeAmount,
            price_per_employee: pricePerEmployee,
        },
    );
}

export async function deleteRate(rateId) {
    return await io.request(
        'DELETE',
        `/internal-api/billing/companies/rates/${rateId}`,
    );
}

export async function createTrialRate(rate) {
    return await io.post('/internal-api/billing/companies/rates/trials', {
        rate,
    });
}

export async function createTrialRateWhenEmployeesLimitReached() {
    return await io.post(
        '/internal-api/billing/add-trial-when-employees-limit-reached',
    );
}

export async function createTrialRateWhenTryUseLockedFunction() {
    return await io.post(
        '/internal-api/billing/add-trial-when-used-paid-feature',
    );
}

export async function sendAnalyticsTrialEmployeeLimitReachedPopup(
    eventType,
    dateTime,
) {
    return await io.post('/analytics/cabinet/event', {
        type: eventType,
        data: { dateTimePopupShow: dateTime },
    });
}

/**
 * @param {Object} options
 * @property {string} companyId
 * @property {number} pricePerDocument
 * @returns {Promise<void>}
 */
export async function savePricePerDocToCompanyConfig({
    companyId,
    pricePerDocument,
}) {
    return await io.post('/api/private/billing/price-per-document', {
        company_id: companyId,
        price_per_document: pricePerDocument,
    });
}

export async function getRatesForPayment() {
    return await io.getAsJson('/internal-api/billing/checkout/properties');
}

export async function getRecommendedWebRateForPayment() {
    return await io.getAsJson('/internal-api/billing/recommended-rate');
}
