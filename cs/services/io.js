import Cookies from 'js-cookie';
import { SESSION_EXPIRED_REDIRECT_LINK } from 'lib/constants';
import { isEmptyObject } from 'lib/helpers';
import { redirect } from 'lib/navigation';
import qs from 'qs';
import { broadcastAuthChannel } from 'services/auth';
import { getLoggerSessionId } from 'services/logger-session';
import URL from 'url-parse';
import 'whatwg-fetch';

import { HTTPStatus } from '../services/enums';

import loggerService from '../services/logger';
import { prepareSignSessionOptions } from './sign-session';

const UNHANDLED_ERROR_MESSAGE = `
    Невідома помилка серверу. Спробуйте ще раз, чи зверніться до служби
    підтримки, якщо помилка буде повторюватися`;

const TXID_HEADER = 'x-request-id';
// eslint-disable-next-line no-console
const consoleLogger = console.log;

const logFailResponseMeta = ({
    method,
    status,
    url,
    txid,
    data,
    options,
    getParams,
}) => {
    consoleLogger('='.repeat(20), 'API ERROR', '='.repeat(20));
    consoleLogger(method, url);
    consoleLogger(data);
    consoleLogger(options);
    consoleLogger(getParams);
    consoleLogger(status);
    consoleLogger(TXID_HEADER, txid);
    consoleLogger('session_id', getLoggerSessionId());
    consoleLogger(
        new Date().toLocaleTimeString(),
        new Date().toLocaleDateString(),
    );
    consoleLogger('='.repeat(51));
};

function buildUrl(baseUrl, params = {}, options) {
    // TODO consider using native URL
    const url = new URL(baseUrl);

    url.set('query', { ...qs.parse(url.query.slice(1)), ...params });
    const parser = () => qs.stringify(url.query, options);

    return url.toString(parser);
}

function buildDockerhostUrl(baseUrl, params = {}, options) {
    const url = buildUrl(baseUrl, params, options);
    return url.replace('localhost', 'dockerhost');
}

function prepareApiError(data, status) {
    if (data === null) {
        data = { code: `error_${status}`, reason: UNHANDLED_ERROR_MESSAGE };
    }

    const graphQLMessage =
        data &&
        data.errors &&
        data.errors.map((error) => error.message).join(', ');
    const graphQLCode =
        data &&
        data.errors &&
        data.errors.map((error) => error.extensions.code).join(', ');
    const err = new Error(data.reason || graphQLMessage);
    err.code = graphQLCode || data.code;
    err.details = data.details;
    err.reason = data.reason;
    err.status = status;

    return err;
}

async function prepareError(response) {
    const { status } = response;
    let data = null;
    let txid = '';
    try {
        txid = response.headers.get(TXID_HEADER);
        data = await response.json();
    } catch (err) {
        if (typeof window === 'object') {
            loggerService.error('Unhandled error on preparing human error', {
                err: err.toString(),
                errMessage: err.message,
                txid,
                status,
            });
        }
    }
    return prepareApiError(data, status);
}

async function request(
    method,
    url,
    data,
    isReturnDataAsJSON = false,
    options = {},
    getParams = {},
) {
    let init = { credentials: 'same-origin', method, ...options };
    if (method !== 'GET') {
        let body;

        if (self.FormData && data instanceof self.FormData) {
            body = data;
        } else {
            body = JSON.stringify(data);
        }
        init = { ...init, method, body };
    }

    const xsrfToken = Cookies.get('xsrf');
    if (xsrfToken) {
        if (!init.headers) {
            init.headers = {};
        }
        init.headers['X-XSRF-Token'] = xsrfToken;
    }

    const signSessionOptions = prepareSignSessionOptions();

    if (signSessionOptions.headers) {
        if (!init.headers) {
            init.headers = {};
        }
        init.headers = { ...init.headers, ...signSessionOptions.headers };
    }

    // Prepare get parameters
    if (!isEmptyObject(getParams)) {
        url = buildUrl(url, getParams, { arrayFormat: 'repeat' });
    }

    let response;
    try {
        response = await self.fetch(url, init);
    } catch (err) {
        loggerService.error('Error on fetch', {
            err: err.message,
            method,
            url,
            options,
            onLine: navigator.onLine,
        });
        throw navigator.onLine
            ? err
            : new Error('Проблема з підключенням до Інтернету');
    }

    if (!response.ok) {
        if (response.status === 403) {
            const responseBody = await response.clone().json();
            const responseCode = responseBody.code;

            if (responseCode === 'session_expired') {
                broadcastAuthChannel.sessionExpired();
                // fix redirect current page
                redirect(SESSION_EXPIRED_REDIRECT_LINK);
            }
        }

        logFailResponseMeta({
            method,
            url,
            data,
            options,
            getParams,
            status: response.status,
            txid: response.headers.get(TXID_HEADER),
        });
        throw await prepareError(response);
    }
    return isReturnDataAsJSON ? await response.json() : response;
}

async function get(url, options, isReturnDataAsJSON = false) {
    return await request('GET', url, null, isReturnDataAsJSON, options);
}

async function getAsArrayBuffer(url, options) {
    const response = await get(url, options);
    return await response.arrayBuffer();
}

async function getAsJson(url, options) {
    const response = await get(url, options);
    return await response.json();
}

async function getAsText(url, options) {
    const response = await get(url, options);
    return await response.text();
}

async function patch(url, data, isReturnDataAsJSON = false, options = {}) {
    return await request('PATCH', url, data, isReturnDataAsJSON, options);
}

async function put(url, data, isReturnDataAsJSON = true, options = {}) {
    return await request('PUT', url, data, isReturnDataAsJSON, options);
}

async function reqDelete(url, data, isReturnDataAsJSON = false, options = {}) {
    return await request('DELETE', url, data, isReturnDataAsJSON, options);
}

async function post(
    url,
    data,
    isReturnDataAsJSON = false,
    options = {},
    getParams = {},
) {
    return await request(
        'POST',
        url,
        data,
        isReturnDataAsJSON,
        options,
        getParams,
    );
}

async function xhrPost(url, formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.addEventListener('load', () => {
            const { responseText, status } = xhr;
            const txid = xhr.getResponseHeader(TXID_HEADER);

            let responseData = null;
            try {
                responseData = responseText ? JSON.parse(responseText) : null;
            } catch (err) {
                if (typeof window === 'object') {
                    loggerService.error(
                        'Unhandled error on preparing human error',
                        {
                            err: err.toString(),
                            status,
                        },
                    );
                }
            }

            if (status >= HTTPStatus.OK && status < HTTPStatus.BAD_REQUEST) {
                resolve(responseData);
            } else {
                logFailResponseMeta({
                    method: 'POST',
                    url,
                    status,
                    txid,
                });
                reject(prepareApiError(responseData, status));
            }
        });
        xhr.addEventListener('error', reject);
        xhr.open('POST', url, true);

        const xsrfToken = Cookies.get('xsrf');
        if (xsrfToken) {
            xhr.setRequestHeader('X-XSRF-Token', xsrfToken);
        }

        xhr.send(formData);
    });
}

/**
 *
 * @param {string} url
 * @param {File[]} files
 * @param {Record<string, any>} getParams
 * @param {boolean} isBlob
 * @param {string} dataName
 * @param {Record<string, any>} formParams
 * @return {Promise<any>}
 */
async function postFiles(
    url,
    files,
    getParams = {},
    isBlob = false,
    dataName = 'files',
    formParams = null,
) {
    const formData = new FormData();
    for (const key of Object.keys(files)) {
        if (isBlob) {
            formData.append(dataName, files[key].data, files[key].name);
        } else {
            formData.append(dataName, files[key], files[key].name);
        }
    }

    if (formParams) {
        formData.append('params', JSON.stringify(formParams));
    }

    if (!isEmptyObject(getParams)) {
        url = buildUrl(url, getParams, { arrayFormat: 'repeat' });
    }
    return xhrPost(url, formData);
}

async function postFile(url, file, getParams = {}) {
    return postFiles(url, { file }, getParams, false, 'file');
}

async function download(response) {
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    document.body.appendChild(link);

    try {
        const header = response.headers.get('Content-Disposition');
        link.download = header.match(/filename="(.*)"/)[1];
    } finally {
        if (window.navigator.msSaveBlob) {
            window.navigator.msSaveOrOpenBlob(blob, link.download);
        } else {
            link.click();
        }
        link.parentNode.removeChild(link);
    }
}

const api = {
    buildUrl,
    buildDockerhostUrl,
    download,
    get,
    getAsArrayBuffer,
    getAsJson,
    getAsText,
    patch,
    put,
    delete: reqDelete,
    post,
    postFile,
    postFiles,
    request,
};

export default api;
