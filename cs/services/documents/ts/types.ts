import {
    IR<PERSON>iew,
    IReviewRequest,
} from 'components/reviewHistoryPopup/reviewHistoryPopupTypes';
import {
    DocumentAccess,
    DocumentCategory as DocumentCategoryGraph,
} from 'gql-types';
import { ACCEPT_EXTENSION_LIST } from 'lib/constants';
import { ITag } from 'ui/tags/tagsTypes';

import { Nullable } from '../../../types/general';
import { IR<PERSON>, IUser, SignerReviewerGroup } from '../../../types/user';
import { AntivirusChecks, DraftAntivirusCheck } from '../../antivirus/types';
import { DocumentParameter } from '../../documentFields/types';
import {
    DocumentCategory,
    DocumentReviewStatus,
    DocumentStatus,
} from '../../enums';
import { DocumentSource, SignatureFormat } from '../../enums';
import { SignDocValidationResultType } from './enums';

export interface TitleDoc {
    id: string;
    title: string;
    type?: string;
    number?: string;
}

export interface SignDocValidationResult {
    doc: TitleDoc;
    validation: SignDocValidationResultType;
}

export interface SignedDoc extends TitleDoc {
    remainSignatures: number;
    isInput: boolean;
}

export interface SignDocResult {
    doc: SignedDoc;
    validation: SignDocValidationResultType;
    signed: boolean;
    success: boolean;
    error?: string;
    errorCode?: string;
}

export interface SignDocError extends SignDocResult {
    error: string;
}

interface SignerUser {
    id: string;
    email: string;
    firstName: string;
    secondName: string;
    lastName: string;
}
interface SigrnerRole {
    id: string;
    userId: string;
    user: SignerUser;
}
export interface Signer {
    id: string;
    documentId: string;
    companyId: string;
    roleId: string;
    groupId: Nullable<string>;
    dateCreated: string;
    dateSigned: Nullable<string>;
    order?: Nullable<number>;
    role: IRole;
    assignerId: Nullable<string>;
    group: SignerReviewerGroup;
    groupSignerId: Nullable<string>;
    groupSignedBy: SigrnerRole;
}

export interface ReviewSetting {
    id?: string;
    documentId?: string;
    companyId?: string;
    isRequired: boolean;
    isParallel: boolean;
    dateCreated?: string;
    dateUpdated?: string;
}

export interface Signature {
    id: string;
    documentId: string;
    userId: string;
    roleId: string;
    status: string;
    isInternal: string;
    keyAcsk: string;
    keySerialNumber: string;
    keyTimeMark: string;
    keyCompanyFullName?: string;
    keyOwnerEdrpou: string;
    keyOwnerFullName: string;
    keyIsLegal: boolean;
    dateCreated: string;
    userEmail: string;
    user: IUser;
    keyOwnerPosition?: string;
    stampAcsk?: string;
    stampSerialNumber?: string;
    stampTimeMark?: string;
    stampCompanyFullName?: string;
    stampOwnerEdrpou?: string;
    stampOwnerFullName?: string;
    stampOwnerPosition?: string;
    stampIsLegal?: boolean;
    isValid?: boolean;
}

export interface DocumentLink {
    creatorEdrpou: string;
    documentId: string;
    document?: Document;
}

interface CompanyRecipient {
    name?: string;
    id?: string;
}

interface DocumentUser {
    email: string;
    firstName: string;
    lastName: string;
    secondName: string;
    phone: Nullable<string>;
}

interface Metadata {
    contentHash: string;
    contentLength: number;
}

export interface DocumentRecipientItem {
    id: string;
    emails: string[];
    edrpou: string;
    isEmailsHidden: boolean;
    dateSent: Nullable<string>;
}

interface DocumentFlowReceiverItem {
    edrpou: string;
    emails: string[];
    is_emails_hidden: boolean;
}

export interface DocumentFlowItem {
    id: string;
    canSign: boolean;
    edrpou: string;
    displayCompanyName: string;
    isComplete: boolean;
    dateSent: Nullable<string>;
    order: Nullable<number>;
    pendingSignaturesCount: number;
    signaturesCount: number;
    // TODO: use "recipientId" field instead of "receivers" to avoid redundant fetching
    // of the same data that is already present in the "doc.recipients" field
    receivers: DocumentFlowReceiverItem;
}

export interface DocumentComment {
    id: string;
    accessCompanyId: string;
    text: string;
    isRejection: boolean;
    dateCreated: string;
    dateEdited: Nullable<string>;
    roleId: string;
    role: {
        user: {
            email: string;
            firstName: string;
            lastName: Nullable<string>;
            secondName: Nullable<string>;
        };
    };
}

type DocumentVersionType = 'converted_format' | 'editor_created' | 'new_upload';

export interface DocumentVersion {
    id: string;
    name: string;
    type: DocumentVersionType;
    isSent: boolean;
    dateCreated: unknown;
    contentHash: string;
    contentLength: string;
    antivirusChecks?: Nullable<AntivirusChecks[]>;
    extension: typeof ACCEPT_EXTENSION_LIST[number]; // ex: '.docx'
    role: {
        id: string;
        user: {
            email: string;
        };
        company: {
            id: string;
            name: string;
            edrpou: string;
        };
    };
    reviewStatus: Nullable<DocumentReviewStatus>;
    // values created on front end part
    isIncoming: boolean;
    statusTitle: string;
    statusColor: string;
}

export interface AllDocumentRequiredFields {
    id: string;
    companyId: string;
    documentCategory: DocumentCategory;
    isDateRequired: boolean;
    isNumberRequired: boolean;
    isTypeRequired: boolean;
    isAmountRequired: boolean;
}

interface DeleteRequest {
    id: string;
    documentId: string;
    initiatorRoleId: string;
    recipientsEmails: string[];
    receiverEdrpou: string;
    initiatorEdrpou: string;
    status: string;
    message: string;
    rejectMessage: Nullable<string>;
    isReceiver: boolean;
}

export interface DraftCompany {
    edrpou: string;
    name: string;
}

export interface DraftRole {
    companyId: string;
    company: DraftCompany;
    user: DocumentUser;
}
export interface Draft {
    id: string;
    type: string;
    documentId: string;
    documentVersionId: string;
    dateCreated: string;
    dateUpdated: string;
    creatorRoleId: string;
    creatorRole: DraftRole;
    antivirusCheck: Nullable<DraftAntivirusCheck>;
}

interface ViewerGroupsItemGroupMember {
    id: string;
    role: {
        id: string;
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string | null;
            secondName: string | null;
        };
    };
}

export interface ViewerGroupsItemGroup {
    id: string;
    name: string;
    members: ViewerGroupsItemGroupMember[];
}

interface ViewerGroupsItem {
    id: string;
    group: ViewerGroupsItemGroup;
}

// DOCUMENTS_PRIVATE_ACCESS
export type DocumentAccessLevel = 'private' | 'extended';

// TODO add fields as needed
export interface Document {
    id: string;
    title: string;
    accesses: Array<DocumentAccess>;
    viewerGroups: Array<ViewerGroupsItem>;
    number: Nullable<string>;
    amount: Nullable<number>;
    displayCompanyName: string;
    displayCompanyEdrpou: string;
    displayCompanyEmail: string;
    displayEdrpou: string;
    /**
     * @example '.doc', '.docx' etc
     */
    extension: typeof ACCEPT_EXTENSION_LIST[number];
    /**
     * @example 'doc', 'docx' etc
     */
    ext: string;

    isDeleteLocked: boolean;

    emailOwner: string;
    status:
        | 'UPLOADED'
        | 'READY'
        | 'SENT'
        | 'SIGNED'
        | 'SIGNED_AND_SENT'
        | 'SEEN'
        | 'REJECT'
        | 'APPROVED'
        | 'FINISHED'
        | 'DELETED'
        | 'FLOW_PROCESS';
    statusId: DocumentStatus;
    statusColor: 'Red' | 'Orange' | 'Green' | 'Gray';
    statusTitle: string;
    source: typeof DocumentSource;
    type: Nullable<string>;
    dateCreated: ISODate;
    dateDocument: Nullable<ISODate>;
    remainSignatures?: number;
    category: Nullable<number>;
    categoryDetails: Nullable<DocumentCategoryGraph>;
    user: DocumentUser;
    firstSignBy: 'recipient' | 'owner';

    metadata?: Metadata;
    tags: ITag[];
    parameters?: DocumentParameter[];
    deleteRequest: DeleteRequest;

    recipients: DocumentRecipientItem[];
    flows: DocumentFlowItem[];

    signers: Signer[];
    signatures: Signature[];
    hasEUSignatures?: boolean;
    reviews: IReview[];
    reviewSetting?: ReviewSetting;
    reviewRequests: IReviewRequest[];
    companyRecipient?: CompanyRecipient;
    edrpouOwner?: string;
    edrpouRecipient?: Nullable<string>;
    emailRecipient?: Nullable<string>;
    companyNameOwner?: string;
    companyNameRecipient?: Nullable<string>;

    expectedSignatureFormat?: typeof SignatureFormat;
    expectedOwnerSignatures?: number;
    expectedRecipientSignatures?: number;

    parent?: DocumentLink;
    children: DocumentLink[];

    dateListing: string;
    dateFinished: string;

    /**
     * @description - поле яке показує чи вхоить документ в список тих які доступні до перегляду відповідно до тарифів.
     */
    isViewable: boolean;

    isInternal: boolean;

    accessLevel: DocumentAccessLevel;

    reviewStatus: Nullable<DocumentReviewStatus>;
    isMultilateral?: boolean;

    antivirusChecks?: Nullable<AntivirusChecks[]>;

    isRecipientEmailHidden?: Nullable<boolean>;
    isInput?: boolean;
    isOneSign: boolean;

    kind?: string;

    comments: DocumentComment[];

    isInvalidSigned?: Nullable<boolean>;

    versions: DocumentVersion[];
    drafts: Draft[];
}

export interface RecipientEmail {
    edrpou: string;
    is_hidden: boolean;
    emails: string[];
}
