import { formatDate, isToday } from 'lib/date';
import { isEmptyObject, sortByOrder } from 'lib/helpers';
import { getLocationQuery } from 'lib/url';
import qs from 'qs';
import { t } from 'ttag';

import {
    DocumentFirstSignBy,
    DocumentReviewProcess,
    DocumentReviewStatus,
    DocumentSignProcess,
    DocumentStatus,
    DocumentType,
} from '../enums';
import {
    DocumentFolder,
    DocumentKind,
    DocumentSort,
    DocumentSource,
} from '../enums';

import {
    getCurrentCompanyDocumentFlow,
    getDocumentVersionByLocation,
    getDocumentVersionStatusInfo,
    getIsCurrentCompanyCanAddSignersForBilateralDoc,
    getIsDocHasAllNeededSignatures,
    getIsIncomingVersion,
    getIsVersionedDocument,
    getIsVersionedDocumentFlow,
    isDocumentDeleteLocked,
} from './ts/utils';

import loggerService from '../logger';
import { getLocationId } from '../navigation-structure';
import { getSignSessionId } from '../sign-session';
import { MS_OFFICE_EXTENSIONS, SUPPORTED_EXTENSIONS } from './constants';

const DOCUMENT_STATUS_INFO = {
    // initial
    UPLOADED: {
        color: 'Gray',
    },
    READY: {
        color: 'Gray',
    },

    // pending
    SENT: {
        color: 'Orange',
    },
    SIGNED: {
        color: 'Orange',
    },
    SIGNED_AND_SENT: {
        color: 'Orange',
    },

    // response
    SEEN: {
        color: 'Orange',
    },
    REJECT: {
        color: 'Red',
    },
    APPROVED: {
        color: 'Orange',
    },
    FLOW_PROCESS: {
        color: 'Orange',
    },

    // final
    FINISHED: {
        color: 'Green',
    },
    DELETED: {
        color: 'Red',
    },

    // virtual statuses
    IMPORTED: {
        color: 'Gray',
    },
};

const DOCUMENT_KIND_NAME = {
    recipient_sign_first: t`Перший підпис контрагента`,
    owner_one_sign_to_finish: t`Не потребує підпису контрагента`,
    recipient_one_sign_to_finish: t`Потребує тільки підпис контрагента`,
    internal: t`Підпис тільки всередині компанії`,
    multilateral: t`Багатосторонній документ`,
    default: t`Перший підпис відправника`,
};

const urlViewerMap = {
    pdf: 'pdf-viewer',
    xml: 'xml-viewer',

    jpeg: 'image-viewer',
    jpg: 'image-viewer',
    png: 'image-viewer',

    doc: 'office-viewer',
    docx: 'office-viewer',
    xls: 'office-viewer',
    xlsx: 'office-viewer',
};

const isAlreadySigned = (doc, roleId) => {
    return doc.signatures.some((item) => item.roleId === roleId);
};

export const isSigned = (doc) => doc.signatures.length > 0;

export const isMultilateralDocument = (doc) =>
    doc.kind === DocumentType.MULTILATERAL;
export const getIsInternalDocument = (doc) =>
    doc.kind === DocumentType.INTERNAL || doc.isInternal;

export const isCanEditInternalDocument = (doc) =>
    getIsInternalDocument(doc) ? doc.statusId <= DocumentStatus.READY : true;

export const isCanEditMultilateralDocument = (doc) =>
    doc.kind === DocumentType.MULTILATERAL
        ? doc.statusId <= DocumentStatus.UPLOADED
        : true;

export const isCanBeEditByStatus = (doc) => {
    if (getIsInternalDocument(doc)) {
        return isCanEditInternalDocument(doc);
    }
    if (isMultilateralDocument(doc)) {
        return isCanEditMultilateralDocument(doc);
    }
    return ![
        DocumentStatus.FINISHED,
        DocumentStatus.REJECT,
        DocumentStatus.APPROVED,
        DocumentStatus.DELETED,
    ].includes(doc.statusId);
};

export const isRecipientSignedDocument = (doc) =>
    (doc.signatures || []).some(
        (signature) =>
            (signature.keyOwnerEdrpou &&
                signature.keyOwnerEdrpou !== doc.edrpouOwner) ||
            (signature.stampOwnerEdrpou &&
                signature.stampOwnerEdrpou !== doc.edrpouOwner),
    );

export const isOwnerCompanySignedDocument = (doc) => {
    const { edrpouOwner, signatures } = doc;
    return signatures.some(
        (signature) =>
            signature.keyOwnerEdrpou === edrpouOwner ||
            signature.stampOwnerEdrpou === edrpouOwner,
    );
};

const hasNeededRolesSignatures = (doc, edrpou) => {
    let allSignersSigned = true;
    let allFlowsSigned = true;
    if (doc.signers?.length) {
        allSignersSigned = doc.signers.every((signer) => {
            if (signer.roleId) {
                return isAlreadySigned(doc, signer.roleId);
            } else {
                return isAlreadySigned(doc, signer.groupSignerId);
            }
        });
    }
    if (doc.flows?.length && edrpou) {
        allFlowsSigned = doc.flows
            .filter((flow) => flow.edrpou === edrpou)
            .every((flow) => flow.isComplete);
    }
    return allSignersSigned && allFlowsSigned;
};

const getRemainSideSignaturesCount = (doc, sideName) => {
    const {
        edrpouOwner,
        edrpouRecipient,
        expectedOwnerSignatures,
        expectedRecipientSignatures,
        isInput,
        signatures,
        signers,
    } = doc;

    const isRecipient = sideName === 'recipient' || isInput;

    // Filter only current company signatures
    const edrpou = isRecipient ? edrpouRecipient : edrpouOwner;
    const actualRoleSignatures = signatures.filter(
        (item) =>
            item.keyOwnerEdrpou === edrpou || item.stampOwnerEdrpou === edrpou,
    );

    const actualSeparateRoleSinatures = actualRoleSignatures.filter(
        (signature) => {
            if (
                (signers || []).some(
                    (signer) => signer.roleId === signature.roleId,
                ) ||
                !(signers || []).some(
                    (signer) => signer.groupSignerId === signature.roleId,
                )
            ) {
                return signature;
            }
        },
    );

    const actualGroupSignatures = (signers || []).filter(
        (signer) => signer.groupSignerId !== null,
    );

    const actualSignaturesCount =
        actualSeparateRoleSinatures.length + actualGroupSignatures.length;

    const expectedSignatures = isRecipient
        ? expectedRecipientSignatures
        : expectedOwnerSignatures;

    let delta = expectedSignatures - actualSignaturesCount;

    if (doc.signers?.length) {
        const uniqueRoleSignaturesCount = new Set(
            actualSeparateRoleSinatures.map((s) => s.roleId),
        ).size;
        delta =
            expectedSignatures -
            (uniqueRoleSignaturesCount + actualGroupSignatures.length);
    }

    // If the number is negative, then zero number of signatures are remain
    return delta > 0 ? delta : 0;
};

function is3pDocumentKind(kind) {
    return (
        [
            DocumentKind.RECIPIENT_SIGN_FIRST,
            DocumentKind.RECIPIENT_ONE_SIGN_TO_FINISH,
        ].indexOf(kind) !== -1
    );
}

function isAlredySigned3pDocument(doc) {
    return (
        is3pDocumentKind(doc.kind) &&
        ['SIGNED_AND_SENT', 'APPROVED', 'FINISHED'].indexOf(doc.status) >= 0
    );
}

function isReadyToSignAfterReview(doc, paramVersionId) {
    const isVersionedDoc = getIsVersionedDocument(doc);
    const isReviewRequired = doc.reviewSetting?.isRequired;

    if (!isReviewRequired) {
        return true;
    }

    if (isVersionedDoc) {
        const versionId = paramVersionId || doc.versions?.[0]?.id;
        const version = doc.versions.find(
            (versionItem) => versionItem.id === versionId,
        );

        return (
            // Check if review status is APPROVED if the review is required for signing a document
            version.reviewStatus &&
            version.reviewStatus === DocumentReviewStatus.APPROVED
        );
    }

    return (
        // Check if review status is APPROVED if the review is required for signing a document
        doc.reviewStatus && doc.reviewStatus === DocumentReviewStatus.APPROVED
    );
}

function getIsReadyToSendAfterReview(doc, paramVersionId) {
    const isVersionedDoc = getIsVersionedDocument(doc);
    const isReviewRequired = doc.reviewSetting?.isRequired;

    if (!isReviewRequired || !isVersionedDoc) {
        return true;
    }

    const versionId = paramVersionId || doc.versions[0].id;
    const version = doc.versions.find(
        (versionItem) => versionItem.id === versionId,
    );

    return (
        // Check if review status is APPROVED if the review is required for signing a document
        version.reviewStatus &&
        version.reviewStatus === DocumentReviewStatus.APPROVED
    );
}

function canChangeRecipient(doc) {
    if (doc.isInternal || doc.isInput || doc.isMultilateral) return false;

    let canChange =
        ['UPLOADED', 'READY', 'SIGNED', 'SENT', 'SIGNED_AND_SENT'].indexOf(
            doc.status,
        ) >= 0;

    if (getIsVersionedDocument(doc)) {
        canChange = doc.statusId < DocumentStatus.SENT;
    } else if (is3pDocumentKind(doc.kind)) {
        canChange = ['UPLOADED', 'READY', 'SENT'].indexOf(doc.status) >= 0;
    } else if (doc.isOneSign) {
        canChange =
            ['UPLOADED', 'READY', 'SIGNED', 'FINISHED', 'APPROVED'].indexOf(
                doc.status,
            ) >= 0;
    }

    return canChange;
}

function canSign3PDocument(doc) {
    if (doc.isInput) {
        return doc.isOneSign
            ? ['SENT', 'APPROVED', 'REJECT', 'FINISHED'].includes(doc.status)
            : [
                  'SENT',
                  'SIGNED',
                  'SIGNED_AND_SENT',
                  'REJECT',
                  'FINISHED',
              ].includes(doc.status);
    }
    if (doc.status === 'REJECT')
        return getRemainSideSignaturesCount(doc, 'recipient') === 0;

    return doc.isOneSign
        ? false
        : ['SIGNED_AND_SENT', 'APPROVED', 'FINISHED'].indexOf(doc.status) >= 0;
}

function canChangeFlow(doc) {
    return doc.isMultilateral && doc.status === 'UPLOADED';
}

function findSignerOrGroupSigner(doc, roleId) {
    return (doc.signers || []).find((signer) => {
        if (signer.roleId) {
            return signer.roleId === roleId;
        }

        return signer.group?.members?.some(
            (member) => member.role.id === roleId,
        );
    });
}

function canSignByOrder(doc, roleId, edrpou) {
    // ban sign if document has draft
    if (doc?.drafts?.length) {
        return false;
    }
    // allow adding extra signatures after signing
    if (doc.signatures.some((s) => s.roleId === roleId)) {
        return true;
    }

    if (!doc.signers || doc.signers.length === 0) {
        return true;
    }

    const currentRoleSigner = findSignerOrGroupSigner(doc, roleId);

    // current user is not assigned to this signing flow
    if (!currentRoleSigner || isEmptyObject(currentRoleSigner)) {
        return false;
    }
    // signing flow is unordered
    if (!currentRoleSigner.order) {
        return true;
    }
    const existingCompanySignatures = doc.signatures
        .filter(
            (s) => s.keyOwnerEdrpou === edrpou || s.stampOwnerEdrpou === edrpou,
        )
        .map((s) => s.roleId);
    return (
        currentRoleSigner.order - 1 === new Set(existingCompanySignatures).size
    );
}

function canSignByFlow(doc, email, edrpou) {
    if (!doc.flows || doc.flows.length === 0) {
        return true;
    }

    const isFlowOrdered = getIsOrderedFlowProcess(doc);

    if (!isFlowOrdered) {
        return true;
    }

    const currentSigner = [...doc.flows]
        .sort(sortByOrder)
        .find((f) => f.canSign && f.edrpou === edrpou);
    if (!currentSigner) {
        return false;
    }

    return currentSigner.receivers.emails
        .map((e) => e.toLowerCase())
        .includes(email.toLowerCase());
}

function canSignMultilateral(doc, roleId) {
    if (!doc.flows || doc.flows.length === 0) {
        return false;
    }

    // allow adding extra signatures after signing
    if (doc.signatures.some((s) => s.roleId === roleId)) {
        return true;
    }

    if (getIsOrderedFlowProcess(doc)) {
        // filter not completed flows and order them from 0 to N. If there are not flows, it means that
        // all ordered flows is completed, else check that first (pending) flow can be signed by current user.
        const orderedFlows = doc.flows
            .filter((flow) => !flow.isComplete)
            .toSorted((flow1, flow2) => flow1.order - flow2.order);

        if (orderedFlows.length === 0) {
            return false;
        }

        return orderedFlows[0].canSign;
    }

    // can sign any flow
    return doc.flows.some((flow) => flow.canSign);
}

function canSignIfDocumentIsOneSignType(doc, currentCompanyEdrpou) {
    if (doc.isOneSign) {
        if (doc.firstSignBy === DocumentFirstSignBy.FIRST_SIGN_BY_OWNER) {
            return doc.edrpouOwner === currentCompanyEdrpou;
        }
        if (doc.firstSignBy === DocumentFirstSignBy.FIRST_SIGN_BY_RECIPIENT) {
            return doc.edrpouRecipient === currentCompanyEdrpou;
        }
    }

    return true;
}

function canCurrentCompanySignDocument(doc, roleId, edrpou) {
    const isVersionedDocumentFlow = getIsVersionedDocumentFlow(doc);

    if (doc.isInternal) {
        return true;
    } else if (isVersionedDocumentFlow) {
        const actualVersion = doc.versions[0];
        return actualVersion.isIncoming;
    } else if (doc.isMultilateral) {
        // Multilateral document
        return canSignMultilateral(doc, roleId);
        // 3P document
    } else if (is3pDocumentKind(doc.kind)) {
        return canSign3PDocument(doc);
        // One sign document
    } else if (doc.isOneSign) {
        return (
            ['READY', 'APPROVED', 'FINISHED'].includes(doc.status) &&
            canSignIfDocumentIsOneSignType(doc, edrpou)
        );
        // Input document
    } else if (doc.isInput) {
        return ['SIGNED_AND_SENT', 'APPROVED', 'REJECT', 'FINISHED'].includes(
            doc.status,
        );
    }
    // Standard document
    return true;
}

function canUndoReject(doc, roleId) {
    if (doc.status !== 'REJECT') {
        return true;
    }
    if (!doc.comments) {
        return true;
    }
    if (doc.signers.length === 0) {
        return true;
    }
    if (
        doc.signers.length &&
        doc.signers.some((signer) => signer.roleId === roleId)
    ) {
        return true;
    }

    const rejects = doc.comments
        .filter((c) => c.isRejection)
        .sort((a, b) => a.dateCreated - b.dateCreated);
    if (rejects.length === 0) {
        return true;
    }

    // document is blocked by reject if rejects are present
    // and if current user is not the latest reject initiator
    return rejects[rejects.length - 1].roleId === roleId;
}

function canSignDocument(doc, roleId, currentCompanyEdrpou) {
    return (
        canCurrentCompanySignDocument(doc, roleId, currentCompanyEdrpou) &&
        canSignByOrder(doc, roleId, currentCompanyEdrpou) &&
        canUndoReject(doc, roleId)
    );
}

function canSend3PDocument({ isInput, isOneSign, status }) {
    // Recipient sending
    if (isInput) {
        return status === (isOneSign ? 'APPROVED' : 'SIGNED');
    }
    // Owner sending
    const expectedOwnerStatuses = ['UPLOADED', 'READY'];
    if (!isOneSign) {
        expectedOwnerStatuses.push('APPROVED');
    }
    return expectedOwnerStatuses.indexOf(status) !== -1;
}

function canSendDocument(doc, edrpou) {
    if (doc.isInternal) return false;

    // draft document cannot be sent
    if (doc?.drafts?.length) {
        return false;
    }

    let canSend;
    const { isInput, isOneSign, kind, status } = doc;
    const isSignatureFinishedBySide = ['APPROVED', 'SIGNED'].includes(
        doc.status,
    )
        ? getRemainSideSignaturesCount(doc) === 0 &&
          hasNeededRolesSignatures(doc, edrpou)
        : true;

    // 3P flow
    if (is3pDocumentKind(kind)) {
        canSend = canSend3PDocument(doc);
    } else if (isOneSign) {
        canSend = !isInput && status === 'APPROVED';
    } else if (isInput) {
        // Standard flow, recipient sending
        canSend = status === 'APPROVED';
    } else {
        // Standard flow, owner sending
        canSend = status === 'SIGNED';
    }

    if (doc.flows?.length > 0) {
        const currentCompanyFlow = doc.flows.find(
            (flow) => flow.edrpou === edrpou,
        );

        if (
            currentCompanyFlow &&
            !currentCompanyFlow.isComplete &&
            currentCompanyFlow.pendingSignaturesCount === 0
        ) {
            canSend = true;
        }
    }

    if (doc.isMultilateral && doc.status === 'UPLOADED') {
        // щоб не показувати дії "Надіслати" та "Підписати" одночасно
        // достатньою першого підпису для відправки
        canSend = !(
            getIsOrderedFlowProcess(doc) &&
            !doc.flows[0]?.isComplete &&
            doc.flows[0]?.canSign &&
            doc.flows[0]?.edrpou === edrpou
        );
    }

    return canSend && isSignatureFinishedBySide;
}

function canRejectDocument(doc, currentCompanyEdrpou, roleId) {
    if (isAlreadySigned(doc, roleId)) {
        return false;
    }
    if (doc.isInternal) {
        return ['READY', 'SIGNED', 'APPROVED'].includes(doc.status);
    }
    if (doc.isMultilateral) {
        const isOwnerCompany = doc.edrpouOwner === currentCompanyEdrpou;
        return ['FLOW_PROCESS'].includes(doc.status) && !isOwnerCompany;
    }

    const isRemainSignaturesLeft = getRemainSideSignaturesCount(doc) !== 0;
    if (is3pDocumentKind(doc.kind)) {
        const canRejectStatuses = isRemainSignaturesLeft
            ? ['SENT', 'SIGNED', 'APPROVED', 'SIGNED_AND_SENT']
            : ['SENT'];
        return doc.isInput && canRejectStatuses.includes(doc.status);
    }
    return (
        doc.isInput &&
        isRemainSignaturesLeft &&
        ['SIGNED_AND_SENT', 'SIGNED', 'APPROVED'].includes(doc.status) &&
        doc.versions.length < 2
    );
}

function canMultiChangeRecipient(documents) {
    let canChange = true;
    for (const doc of documents) {
        if (!canChangeRecipient(doc)) {
            canChange = false;
            break;
        }
    }
    return canChange;
}

function canMultiChangeFlow(documents) {
    let canChange = true;
    for (const doc of documents) {
        if (!canChangeFlow(doc)) {
            canChange = false;
            break;
        }
    }
    return canChange;
}

function canMultiSign(documents, roleId, edrpou) {
    let canSign = true;
    let isSignatureByCurrentRoleNeeded;
    let isReadyToSignAfterReviewState;
    for (const doc of documents) {
        isSignatureByCurrentRoleNeeded =
            ['APPROVED', 'SIGNED'].indexOf(doc.status) >= 0
                ? getRemainSideSignaturesCount(doc) > 0 &&
                  !isAlreadySigned(doc, roleId)
                : true;
        isReadyToSignAfterReviewState = isReadyToSignAfterReview(doc);

        // Multilateral document
        if (doc.isMultilateral) {
            canSign = canSignMultilateral(doc, roleId);
            // Internal document
        } else if (doc.isInternal) {
            canSign = ['READY', 'SIGNED', 'REJECT'].includes(doc.status);
            // 3P document
        } else if (is3pDocumentKind(doc.kind)) {
            canSign = canSign3PDocument(doc);
            // Input document
        } else if (doc.isInput) {
            canSign = ['SIGNED_AND_SENT', 'APPROVED', 'REJECT'].includes(
                doc.status,
            );
            // One sign document
        } else if (doc.isOneSign) {
            canSign =
                ['READY', 'APPROVED', 'FINISHED'].includes(doc.status) &&
                canSignIfDocumentIsOneSignType(doc, edrpou);
            // Standard document
        } else {
            canSign = ['SIGNED', 'READY'].includes(doc.status);
        }

        if (
            !canSign ||
            !isSignatureByCurrentRoleNeeded ||
            !isReadyToSignAfterReviewState ||
            !canSignByOrder(doc, roleId, edrpou)
        ) {
            canSign = false;
            break;
        }
    }
    return canSign;
}

function canAddSigners(doc, currentUser) {
    if (Object.getPrototypeOf(currentUser) === Map.prototype) {
        currentUser = Object.entries(currentUser);
    }

    const statusId = doc.statusId;
    const currentEdrpou = currentUser.currentCompany.edrpou;
    const isAdmin = currentUser.currentRole.isAdmin;

    const expectedOwnerSignatures = doc.expectedOwnerSignatures;
    const expectedRecipientSignatures = doc.expectedRecipientSignatures;

    const edrpouOwner = doc.edrpouOwner;
    const isUserDocumentOwner = doc.emailOwner === currentUser.email;
    const firstSignBy = doc.firstSignBy;
    const isBilateral = !doc.isInternal && !doc.isMultilateral;
    const isOnOwnerSide = edrpouOwner === currentEdrpou;
    const is3p = firstSignBy === DocumentFirstSignBy.FIRST_SIGN_BY_RECIPIENT;
    const isVersionedDoc = getIsVersionedDocument(doc);

    const isCurrentCompanyCanAddSignersForBilateralDoc = getIsCurrentCompanyCanAddSignersForBilateralDoc(
        doc,
        currentUser.currentCompany,
    );

    const canRecipientAssign =
        !isOnOwnerSide &&
        expectedRecipientSignatures > 0 &&
        ((is3p && statusId === DocumentStatus.SENT) ||
            (!is3p && statusId === DocumentStatus.SIGNED_AND_SENT));

    const canOwnerAssign =
        isOnOwnerSide &&
        expectedOwnerSignatures > 0 &&
        (statusId <= DocumentStatus.READY ||
            (is3p && statusId === DocumentStatus.SIGNED_AND_SENT));

    if (isVersionedDoc) {
        const isVersionedDocumentFlow = getIsVersionedDocumentFlow(doc);

        if (isVersionedDocumentFlow) {
            return isAdmin || isUserDocumentOwner;
        }
    }

    if (isAdmin) {
        if (doc.isMultilateral) {
            if (!doc.flows.length) {
                return true;
            }

            const currentCompanyFlow = getCurrentCompanyDocumentFlow(
                doc,
                currentUser.currentCompany.edrpou,
            );

            if (!currentCompanyFlow) {
                return false;
            }

            return !currentCompanyFlow.isComplete;
        }

        if (isBilateral && !isCurrentCompanyCanAddSignersForBilateralDoc) {
            return false;
        }

        if (doc.isInternal && doc.statusId === DocumentStatus.FINISHED) {
            return false;
        }

        return true;
    }

    if (isBilateral && !canRecipientAssign && !canOwnerAssign) {
        return false;
    }

    return true;
}

function canMultiSend(documents, edrpou) {
    for (const doc of documents) {
        if (
            (doc.status === 'UPLOADED' && !doc.isMultilateral) ||
            !canSendDocument(doc, edrpou)
        )
            return false;
    }
    return true;
}

function canMultiDelete(documents, currentUser) {
    let canDelete = true;
    for (const doc of documents) {
        if (!getIsUserCanDeleteDocument(doc, currentUser)) {
            canDelete = false;
            break;
        }
    }
    return canDelete;
}

function isDeleteRequestReceiver(documents, currentEdrpou) {
    let isReceiver = true;
    for (const doc of documents) {
        if (
            !doc.deleteRequest ||
            doc.deleteRequest.initiatorEdrpou === currentEdrpou
        ) {
            isReceiver = false;
        }
    }
    return isReceiver;
}

function isDeleteRequestInitiator(documents, edrpou) {
    let isInitiator = true;
    for (const doc of documents) {
        if (
            !doc.deleteRequest ||
            doc.deleteRequest.initiatorEdrpou !== edrpou
        ) {
            isInitiator = false;
        }
    }
    return isInitiator;
}

function getDocumentDisplayDate(doc, dateSortType) {
    let date;
    switch (dateSortType) {
        case DocumentSort.DATE_CREATED:
            // document always has dateCreated
            date = doc.dateCreated;
            break;

        case DocumentSort.DATE_LISTING:
            // document always has dateListing
            date = doc.dateListing;
            break;

        case DocumentSort.DATE_DOCUMENT:
            if (!doc.dateDocument) {
                date = doc.dateListing;
            } else date = doc.dateDocument;
            break;

        case DocumentSort.DATE_FINISHED:
            //date must be equal strictly doc.dateFinished
            date = doc.dateFinished;
            break;
    }

    if (date === null) {
        return ' - ';
    }

    if (isToday(date)) {
        return formatDate(date, 'HH:mm');
    }
    return formatDate(date);
}

function getDocumentsActions({
    documents,
    isSelectedAll,
    location,
    currentUser,
}) {
    const currentCompanyEdrpou = currentUser.currentRole.company.edrpou;
    const roleId = currentUser.currentRole.id;
    const locationId = getLocationId(location);

    if (isSelectedAll) {
        const locationQuery = getLocationQuery(location);
        const availableLocationsToSign = ['ready'];
        const availableLocationsToDelete = [
            'ready',
            'waited',
            'ready_to_send',
            'canceled',
        ];
        return {
            sign: availableLocationsToSign.includes(locationId),
            download: false,
            delete:
                locationId === 'downloaded' ||
                (availableLocationsToDelete.includes(locationId) &&
                    locationQuery.folder_id === DocumentFolder.OUTGOING),
        };
    }
    const actions = {
        changeFlow: false,
        changeRecipient: false,
        comment: true,
        delete: false,
        download: false,
        print: false,
        reject: false,
        reviewAddReviewers: true,
        reviewApprove: true,
        send: false,
        sign: false,
        findRecipients: ['ready_to_send', 'downloaded'].includes(locationId),
    };
    if (documents.length === 1) {
        const doc = documents[0];
        actions.changeFlow = canChangeFlow(doc);
        actions.changeRecipient = canChangeRecipient(doc);
        actions.sign = canSignDocument(doc, roleId, currentCompanyEdrpou);
        actions.send = canSendDocument(doc, currentCompanyEdrpou);
        actions.reject = canRejectDocument(doc, currentCompanyEdrpou, roleId);
        actions.download = true;
        actions.print = true;
        actions.delete = getIsUserCanDeleteDocument(doc, currentUser);
        actions.deleteRequest = canCreateDeleteRequest([doc], currentUser);
    } else {
        actions.changeFlow = canMultiChangeFlow(documents);
        actions.changeRecipient = canMultiChangeRecipient(documents);
        actions.sign = canMultiSign(documents, roleId, currentCompanyEdrpou);
        actions.send = canMultiSend(documents, currentCompanyEdrpou);
        actions.delete = canMultiDelete(documents, currentUser);
        actions.download = config.MULTI_DOWNLOAD;
        actions.deleteRequest = canCreateDeleteRequest(documents, currentUser);
        actions.deleteRequestReceiver = isDeleteRequestReceiver(
            documents,
            currentCompanyEdrpou,
        );
        actions.deleteRequestInitiator = isDeleteRequestInitiator(
            documents,
            currentCompanyEdrpou,
        );
        actions.reject = documents.every((doc) =>
            canRejectDocument(doc, currentCompanyEdrpou, roleId),
        );
    }

    return actions;
}

function getDocumentUrl(
    docId,
    original = true,
    suffix = '',
    signSessionId = '',
    expirable = false,
    versionId,
    isVersionedDoc = false,
    draftId = '',
) {
    suffix = original ? '/original' : suffix;
    const url = draftId
        ? `/internal-api/drafts/${draftId}/content?`
        : docId && `/downloads/${docId}${suffix}?`;

    const ssid = signSessionId || getSignSessionId();
    const optionalQueryParams = {};

    if (suffix === '/archive') {
        const { download_filenames_mode } = getLocationQuery(location);
        // It's a hidden feature that our support team can recommend to our clients
        // when they have issues with shortened filenames on Windows.
        if (download_filenames_mode) {
            optionalQueryParams.filenames_mode = download_filenames_mode;
        }
    }

    if (ssid) {
        optionalQueryParams.ssid = ssid;
    }
    if (expirable) {
        optionalQueryParams.expirable = 'true';
    }

    if (isVersionedDoc && !draftId) {
        optionalQueryParams.version = versionId || 'latest';
    }

    return `${url}${qs.stringify(optionalQueryParams)}`;
}

function getLastSignatureUrl(docId) {
    let url = docId && `/downloads/${docId}/signatures/last`;

    const ssid = getSignSessionId();
    if (ssid) {
        url += `?${qs.stringify({ ssid })}`;
    }

    return url;
}

function getDocumentViewerConfigByCompany(currentUser) {
    const result = {
        renderSignatureAtPage: 'first',
        renderSignatureInInterface: true,
        renderSignatureOnPrintDocument: true,
        renderReviewInInterface: true,
        renderReviewOnPrintDocument: true,
    };
    if (!currentUser) return result;

    // signature render settings are stored in companies table,
    // review render settings are stored in companies_config table
    // TODO: split those settings into companies_config table
    const {
        currentCompany,
        currentCompanyConfig: signatureConfig,
    } = currentUser;
    const companyConfig = currentCompany.config;

    if (signatureConfig) {
        result.renderSignatureInInterface =
            signatureConfig.renderSignatureInInterface;
        result.renderSignatureOnPrintDocument =
            signatureConfig.renderSignatureOnPrintDocument;
    }
    if (companyConfig) {
        (result.renderSignatureAtPage = companyConfig.render_signature_at_page),
            (result.renderReviewInInterface =
                companyConfig.render_review_in_interface);
        result.renderReviewOnPrintDocument =
            companyConfig.render_review_on_print_document;
    }
    return result;
}

function getViewerUrl(url, ext, config = {}, timeStamp = '') {
    const urlViewer = urlViewerMap[ext] || `${ext}-viewer`;

    const configParams = Object.keys(config).reduce(
        (acc, prop) => ({ ...acc, [prop]: config[prop] ? 1 : undefined }),
        {},
    );

    const query = {
        ...configParams,
        file: url,
        expand: config.print ? 1 : undefined,
    };
    const anchor = urlViewer === 'pdf-viewer' ? '#zoom=page-fit' : '';

    const timeStampParam = timeStamp ? `_${timeStamp}` : '';

    return `/${urlViewer}?${qs.stringify(query)}${anchor}${timeStampParam}`;
}

function getDocumentViewerUrl(
    docId,
    ext,
    config = {},
    viewSessionId = '',
    versionConfig = {},
) {
    if (!ext) {
        loggerService.log(
            'Empty ext for document viewer, fallback to PDF viewer.',
            {
                ext,
                doc_id: docId,
                ...config,
            },
        );
        ext = 'pdf';
    }

    const suffix = config.print ? '/print' : '';

    const expirable = MS_OFFICE_EXTENSIONS.includes(ext);

    const url = getDocumentUrl(
        docId,
        false,
        suffix,
        viewSessionId,
        expirable,
        versionConfig.locationVersionId,
        versionConfig.isVersionedDoc,
        versionConfig.currentVersionDraftId,
    );

    let timeStamp;
    //for avoid caching using timestamp
    if (versionConfig.currentVersionDraftId) {
        timeStamp = new Date().getTime();
    }

    return getViewerUrl(url, ext, config, timeStamp);
}

function getDocumentKind(doc) {
    const isOneSignToFinish = doc.isOneSign;

    let kind = DocumentKind.DEFAULT;
    if (isOneSignToFinish) {
        kind = DocumentKind.OWNER_ONE_SIGN_TO_FINISH;
    }
    if (doc.firstSignBy === DocumentFirstSignBy.FIRST_SIGN_BY_RECIPIENT) {
        kind = isOneSignToFinish
            ? DocumentKind.RECIPIENT_ONE_SIGN_TO_FINISH
            : DocumentKind.RECIPIENT_SIGN_FIRST;
    }
    if (doc.isInternal) {
        kind = DocumentType.INTERNAL;
    }
    if (doc.isMultilateral) {
        kind = DocumentType.MULTILATERAL;
    }

    return [kind, DOCUMENT_KIND_NAME[kind]];
}

function getIsOneSign(query) {
    const isOneSign = query.is_one_sign;
    if (isOneSign !== undefined) {
        return isOneSign === '1';
    }
    return isOneSign;
}

function hasDocumentViewer(ext) {
    return SUPPORTED_EXTENSIONS.includes(ext);
}

function formatRelativeDocument(doc) {
    const statusName = doc.isImported
        ? 'IMPORTED'
        : DocumentStatus[doc.statusId];
    const statusInfo = DOCUMENT_STATUS_INFO[statusName] || {};
    const statusTitle = doc.displayStatusText;

    return {
        ...doc,
        statusColor: statusInfo.color,
        statusTitle,
    };
}

function formatDocumentVersions(doc, currentUser) {
    const isVersionedDocumentFlow = getIsVersionedDocumentFlow(doc);

    const versions = doc.versions.map((version) => {
        const updateVersion = {
            ...version,
            isIncoming: getIsIncomingVersion(version, currentUser),
        };

        if (isVersionedDocumentFlow) {
            const statusInfo = getDocumentVersionStatusInfo(updateVersion);

            return {
                ...updateVersion,
                statusColor: statusInfo.color,
                statusTitle: statusInfo.title,
            };
        }
        return updateVersion;
    });

    return versions;
}

function formatDocument(doc, currentUser = {}) {
    const documentLocationVersionId =
        window && window.location.pathname.split('/versions/')[1];

    const companyNameOwner = doc.companyOwner ? doc.companyOwner.name : '';

    let companyNameRecipient;
    if (doc.companyRecipient) {
        companyNameRecipient = doc.companyRecipient.name;
    } else if (doc.contactRecipient) {
        companyNameRecipient = doc.contactRecipient.name;
    }

    const emailOwner = doc.user ? doc.user.email : '';

    const isVersionedDoc = getIsVersionedDocument(doc);

    const isInput = doc.isInput;
    const [kind, kindName] = getDocumentKind(doc);

    const statusName = doc.isImported
        ? 'IMPORTED'
        : DocumentStatus[doc.statusId];

    const documentStatusTitle = doc.displayStatusText;

    const sortedFlows =
        doc.flows &&
        doc.flows.length > 1 &&
        typeof doc.flows[0]?.order === 'number'
            ? doc.flows.sort((a, b) => a.order - b.order)
            : doc.flows;

    let parent = null;
    if (doc.parent && doc.parent.document) {
        parent = {
            ...doc.parent,
            document: formatRelativeDocument(doc.parent.document),
        };
    }

    const children = [];
    if (doc.children) {
        for (const child of doc.children) {
            children.push({
                ...child,
                document: formatRelativeDocument(child.document),
            });
        }
    }

    // we need this, because we get amount from backend in panny format
    const amount = doc.amount ? (+doc.amount / 100).toFixed(2) : doc.amount;

    let versions = doc.versions;
    let versionByLocation = null;
    let actualVersionStatusTitle = '';
    let actualVersionStatusColor = '';

    const isVersionedDocumentFlow = getIsVersionedDocumentFlow(doc);

    if (isVersionedDoc) {
        versions = formatDocumentVersions(doc, currentUser);
        versionByLocation = documentLocationVersionId
            ? getDocumentVersionByLocation(documentLocationVersionId, versions)
            : versions[0];
    }

    if (isVersionedDocumentFlow) {
        actualVersionStatusColor = versions[0].statusColor;
        actualVersionStatusTitle = versions[0].statusTitle;
    }

    const statusInfo = DOCUMENT_STATUS_INFO[statusName] || {};
    const statusColor = isVersionedDocumentFlow
        ? actualVersionStatusColor
        : statusInfo.color;
    const statusTitle = isVersionedDocumentFlow
        ? actualVersionStatusTitle
        : documentStatusTitle;

    const extension = isVersionedDoc
        ? versionByLocation?.extension
        : doc.extension;

    return {
        ...doc,

        amount,

        companyNameOwner,
        companyNameRecipient,

        displayEdrpou: doc.displayCompanyEdrpou,
        displayCompanyName: doc.displayCompanyName,
        displayCompanyEmail: doc.displayCompanyEmail,

        emailOwner,

        ext: extension?.slice(1).toLowerCase() || '',

        flows: sortedFlows,

        isInput,
        kind,
        kindName,

        phoneOwner: doc.user ? doc.user.phone : '',

        source: doc.source ? DocumentSource[doc.source] : '',

        status: statusName,
        statusColor,
        statusTitle,

        versions,

        parent,
        children,
    };
}

function formatDocuments(documents, currentUser) {
    return documents.map((document) => formatDocument(document, currentUser));
}

function getDocumentRecipient(doc) {
    const recipient = doc.recipients.length ? doc.recipients[0] : {};
    const emailRecipient =
        (recipient.emails && recipient.emails.join(', ')) || doc.emailRecipient;
    return {
        edrpou: recipient.edrpou || doc.edrpouRecipient,
        email: emailRecipient,
        isEmailHidden: recipient.isEmailsHidden || doc.isRecipientEmailHidden,
    };
}

function hasDocumentEmptyRequiredParameters(doc) {
    if (!doc.parameters) {
        return false;
    }
    return doc.parameters.some(
        (parameter) => parameter.isRequired && !parameter.value,
    );
}

function isExtraSignature(doc, currentRoleId, currentCompanyEdrpou) {
    const isSignedByCurrentRole = isAlreadySigned(doc, currentRoleId);
    const hasAllNeededSignatures = getIsDocHasAllNeededSignatures(
        doc,
        currentCompanyEdrpou,
    );
    return isSignedByCurrentRole || hasAllNeededSignatures;
}

function isAwaitingForSignatures(doc, email, edrpou, roleId) {
    // checks if document awaits to be signed by another signers
    if (doc.isMultilateral) {
        const currentCompanyFlow = getCurrentCompanyDocumentFlow(doc, edrpou);

        return (
            !canSignByFlow(doc, email, edrpou) &&
            !currentCompanyFlow?.isComplete
        );
    } else {
        const isRemainSignatures = getRemainSideSignaturesCount(doc) !== 0;
        return isAlreadySigned(doc, roleId) && isRemainSignatures;
    }
}

function shouldSendAfterSign(doc, currentRoleId) {
    const remainSignatures = getRemainSideSignaturesCount(doc);

    let willHaveEnoughSideSignatures = remainSignatures === 1;

    if (currentRoleId && doc.signers?.length) {
        const pendingSigners = doc.signers.filter((signer) => {
            if (signer.groupId) {
                return !isAlreadySigned(doc, signer.groupSignerId);
            } else return !isAlreadySigned(doc, signer.roleId);
        });

        const isPendingSignersHasCurrentRoleId = pendingSigners.every(
            (signer) => {
                if (signer.roleId) {
                    return signer.roleId === currentRoleId;
                } else {
                    return signer.group.members.some(
                        (member) => member.role.id === currentRoleId,
                    );
                }
            },
        );
        willHaveEnoughSideSignatures =
            pendingSigners.length === 1 &&
            isPendingSignersHasCurrentRoleId &&
            remainSignatures <= 1;
    }

    return (
        !doc.isInternal && !doc.isMultilateral && willHaveEnoughSideSignatures
    );
}

function shouldOnlySign(doc, currentRoleId, isMultiSign) {
    return !isMultiSign && !shouldSendAfterSign(doc, currentRoleId);
}

function isCurrentRecipientRejectedDocument(doc, edrpou) {
    if (!edrpou || !doc.comments?.length) {
        return false;
    }

    return Boolean(
        doc.comments
            .filter((item) => item.isRejection)
            .find((item) => item.role?.company.edrpou === edrpou),
    );
}

/**
 * @param {DocumentListDocumentItem[]} docs
 * @param {string} currentRoleId
 * @param {string} currentCompanyEdrpou
 * @returns {boolean}
 */
const getIsOnlyExtraSignatureDocuments = (
    docs,
    currentRoleId,
    currentCompanyEdrpou,
) =>
    !docs.some(
        (doc) => !isExtraSignature(doc, currentRoleId, currentCompanyEdrpou),
    );

/**
 * @param {DocumentListDocumentItem[]} docs
 * @param {string} currentRoleId
 * @returns {boolean}
 */
const getIsOnlySignDocuments = (docs, currentRoleId) =>
    !docs.some((doc) => !!shouldSendAfterSign(doc, currentRoleId));

/**
 * @param {DocumentListDocumentItem[]} docs
 * @param {string} currentRoleId
 * @param {string} currentCompanyEdrpou
 * @returns {boolean}
 */
const getIsOnlyCanNotSignDocuments = (
    docs,
    currentRoleId,
    currentCompanyEdrpou,
) =>
    !docs.some((doc) =>
        canSignDocument(doc, currentRoleId, currentCompanyEdrpou),
    );

/**
 * @param {DocumentListDocumentItem[]} docs
 * @returns {boolean}
 */
const getIsOnlyNotReadyToSignAfterReviewDocuments = (docs) =>
    !docs.some((doc) => isReadyToSignAfterReview(doc));

/**
 * @param {boolean} isOrdered
 * @returns {DocumentSignProcess}
 */
const getSignProcess = (isOrdered = false) => {
    return isOrdered
        ? DocumentSignProcess.ORDERED_PROCESS_TYPE
        : DocumentSignProcess.PARALLEL_PROCESS_TYPE;
};

/**
 * @param {boolean} isOrdered
 * @returns {DocumentReviewProcess}
 */
const getReviewProcess = (isOrdered = false) => {
    return isOrdered
        ? DocumentReviewProcess.ORDERED_PROCESS_TYPE
        : DocumentReviewProcess.PARALLEL_PROCESS_TYPE;
};

export const getIsCompanyDocumentOwner = (doc, companyEdrpou) =>
    doc.edrpouOwner === companyEdrpou;

const getIsUserDocumentOwner = (doc, userEmail) => doc.emailOwner === userEmail;

const getIsCompanyDocumentRecipient = (doc, companyEdrpou) =>
    !!doc.recipients?.find(
        (recipient) =>
            recipient.edrpou !== doc.edrpouOwner &&
            recipient.edrpou === companyEdrpou,
    );

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusUploaded = (doc) =>
    doc.statusId === DocumentStatus.UPLOADED;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusReady = (doc) => doc.statusId === DocumentStatus.READY;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
export const getIsDocumentStatusSent = (doc) =>
    doc.statusId === DocumentStatus.SENT;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusSigned = (doc) =>
    doc.statusId === DocumentStatus.SIGNED;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusSignedAndSent = (doc) =>
    doc.statusId === DocumentStatus.SIGNED_AND_SENT;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusReject = (doc) =>
    doc.statusId === DocumentStatus.REJECT;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusApproved = (doc) =>
    doc.statusId === DocumentStatus.APPROVED;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusFinished = (doc) =>
    doc.statusId === DocumentStatus.FINISHED;

/**
 * @param {Pick<DocumentListDocumentItem, 'statusId'>} doc
 * @returns {boolean}
 */
const getIsDocumentStatusFlowProcess = (doc) =>
    doc.statusId === DocumentStatus.FLOW_PROCESS;

/**
 * @param {Pick<DocumentListDocumentItem, 'flows'>} doc
 * @returns {boolean}
 */
const getIsOrderedFlowProcess = (doc) =>
    doc.flows?.some((flow) => typeof flow.order === 'number');

/**
 * @param {DocumentListDocumentItem} doc
 * @returns {boolean}
 */
const getIsParallelFlowProcess = (doc) => !getIsOrderedFlowProcess(doc);

/**
 * @param {Pick<DocumentListDocumentItem, 'flows'>} doc
 * @param {string} currentCompanyEdrpou
 * @returns {DocumentListDocumentItem['flows'][number] | undefined}
 */
const getCurrentCompanyFlow = (doc, currentCompanyEdrpou) =>
    // todo: перевірити бізнес логіку - виглядає, що цей метод не враховує варіанти коли компанія вказана декілька разів у багатосторонньому документі
    doc.flows?.find((flow) => flow.edrpou === currentCompanyEdrpou);

/**
 * @param {Pick<DocumentListDocumentItem, 'flows'>} doc
 * @param {string} currentCompanyEdrpou
 * @returns {boolean}
 */
const getIsAnyCounterpartyCompleteFlow = (doc, currentCompanyEdrpou) =>
    (doc.flows || [])
        .filter((flow) => flow.edrpou !== currentCompanyEdrpou)
        .some((flow) => flow.isComplete);

/**
 * @param {DocumentListDocumentItem} doc
 * @param {string} currentCompanyEdrpou
 * @returns {boolean}
 */
const getFlowReceivedCounterparties = (doc) => {
    const ownerCompanyEdrpou = doc.edrpouOwner; // компанія власник документа
    const flows = doc.flows || [];
    return flows
        .filter((flow) => flow.edrpou !== ownerCompanyEdrpou)
        .some((flow) => !!flow.dateSent);
};

const getIsCurrentUserSignedDocument = (doc, currentUser) =>
    doc.signatures?.some(
        (signature) => signature.roleId === currentUser.roleId,
    );

const getIsCurrentUserDocumentRecipient = (doc, currentUser) =>
    doc.recipients
        .find(
            (recipient) =>
                recipient.edrpou === currentUser.currentCompany.edrpou,
        )
        ?.emails?.some((email) => email === currentUser.email);

export const getIsDocHasRecipientCompanySignatures = (
    doc,
    currentCompanyEdrpou,
) =>
    doc.signatures.some(
        (item) =>
            (item.keyOwnerEdrpou &&
                currentCompanyEdrpou !== item.keyOwnerEdrpou) ||
            (item.stampOwnerEdrpou &&
                currentCompanyEdrpou !== item.stampOwnerEdrpou),
    );

/**
 * @param {DocumentListDocumentItem} doc
 * @returns Boolean
 */
export const getIsDocumentHasRejectedDeleteRequest = (doc) =>
    ['rejected', 'cancelled'].includes(doc.deleteRequest?.status);

/**
 * @param {DocumentListDocumentItem} doc
 * @param {IUser} currentUser
 * @returns {boolean}
 */
const getIsCurrentUserDocumentSignatory = (doc, currentUser) =>
    getIsCurrentUserSignedDocument(doc, currentUser) ||
    getIsCurrentUserDocumentRecipient(doc, currentUser);

/**
 * @param {DocumentListDocumentItem} doc
 * @param {IUser} currentUser
 * @returns {boolean}
 */
const getIsDocSentToRecipients = (doc, currentCompanyEdrpou) => {
    return (doc.recipients || [])
        .filter((r) => r.edrpou !== currentCompanyEdrpou)
        .some((r) => !!r.dateSent);
};

/**
 * @param {DocumentListDocumentItem} doc
 * @param {User} currentUser
 * @returns {boolean}
 *
 * Check if the user can directly delete the document without delete request process.
 *
 * NOTE: do not add any additional checks here, everything should be handled by backend,
 * because we use `doc.canDelete` property also in mobile app.
 *
 * documentation https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/27328529/QA+checklist****
 */
//
const getIsUserCanDeleteDocument = (doc, currentUser) => {
    if (!currentUser) {
        return false;
    }
    return doc.canDelete;
};

/**
 * @param {DocumentListDocumentItem[]} documents
 * @param {User} currentUser
 * @returns {boolean}
 */
// documentation https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/27328529/QA+checklist****
function canCreateDeleteRequest(documents, currentUser) {
    let canDeleteRequest = true;
    for (const doc of documents) {
        if (!getIsCanCreateDeleteRequest(doc, currentUser)) {
            canDeleteRequest = false;
        }
    }
    return canDeleteRequest;
}

/**
 * @param {DocumentListDocumentItem} doc
 * @param {User} currentUser
 * @returns {boolean}
 *
 * NOTE: If the return value is "true", it means that the user can delete the document only through the
 * delete request process. However, if the return value is "false", it means that the user may or may
 * not be able to delete the document directly. Do not use this function to check whether the user can
 * delete the document directly. Use the "getIsUserCanDeleteDocument" function to check if the user
 * can delete the document directly.
 *
 * Check if the user can create delete request for the document.
 *
 * There are tree possible scenarios of the document deletion:
 *  - user can delete the document directly without delete request process
 *  - user can create delete request for the document (covered by this function)
 *  - user can't delete the document nor create delete request for it
 */
const getIsCanCreateDeleteRequest = (doc, currentUser) => {
    if (
        !currentUser ||
        (doc.deleteRequest && !getIsDocumentHasRejectedDeleteRequest(doc)) ||
        (doc.isArchived &&
            !(
                currentUser.currentRole.canDeleteArchivedDocuments ||
                currentUser.currentRole.isAdmin
            )) ||
        doc.isInternal
    ) {
        return false;
    }

    // User can directly delete document without delete request process
    if (getIsUserCanDeleteDocument(doc, currentUser)) {
        return false;
    }

    const currentCompanyEdrpou = currentUser.currentCompany.edrpou;

    const isAdmin = currentUser.currentRole.isAdmin;
    const isCurrentCompanyDocumentOwner = getIsCompanyDocumentOwner(
        doc,
        currentCompanyEdrpou,
    );
    const isDocumentHasRecipientSignatory = getIsDocHasRecipientCompanySignatures(
        doc,
        currentCompanyEdrpou,
    );

    if (
        !isAdmin &&
        !currentUser.currentRole.canDeleteDocument &&
        !currentUser.currentRole.canDeleteDocumentExtended
    ) {
        return false;
    }

    if (isCurrentCompanyDocumentOwner) {
        if (
            isDocumentHasRecipientSignatory ||
            getIsDocumentStatusReject(doc) ||
            getIsDocumentStatusFinished(doc)
        ) {
            return true;
        }

        // дозволяємо видалення через запит - якщо він забраний по АПІ
        if (isDocumentDeleteLocked(doc)) {
            return true;
        }

        return false;
    }

    return true;
};

const getCategoriesNumbersApiArray = (categories) =>
    categories
        ? [categories].flat()?.map((category) => Number(category))
        : null;

export {
    getFlowReceivedCounterparties,
    canAddSigners,
    canChangeRecipient,
    canCurrentCompanySignDocument,
    canSignDocument,
    canSignIfDocumentIsOneSignType,
    canSignByOrder,
    canUndoReject,
    canSendDocument,
    canRejectDocument,
    canMultiChangeRecipient,
    canMultiSign,
    canSignByFlow,
    canMultiSend,
    canMultiDelete,
    canSignMultilateral,
    canCreateDeleteRequest,
    formatDocument,
    formatDocuments,
    formatDocumentVersions,
    formatRelativeDocument,
    getDocumentDisplayDate,
    getDocumentsActions,
    getDocumentUrl,
    getDocumentRecipient,
    getDocumentViewerConfigByCompany,
    getDocumentViewerUrl,
    getIsOneSign,
    getLastSignatureUrl,
    getRemainSideSignaturesCount,
    getViewerUrl,
    hasDocumentViewer,
    hasDocumentEmptyRequiredParameters,
    hasNeededRolesSignatures,
    is3pDocumentKind,
    isAlreadySigned,
    isAlredySigned3pDocument,
    isReadyToSignAfterReview,
    getIsReadyToSendAfterReview,
    isDeleteRequestReceiver,
    isDeleteRequestInitiator,
    isExtraSignature,
    isAwaitingForSignatures,
    shouldOnlySign,
    shouldSendAfterSign,
    getIsOnlyExtraSignatureDocuments,
    getIsOnlySignDocuments,
    getIsOnlyCanNotSignDocuments,
    getIsOnlyNotReadyToSignAfterReviewDocuments,
    isCurrentRecipientRejectedDocument,
    getIsUserCanDeleteDocument,
    getSignProcess,
    getReviewProcess,
    getCategoriesNumbersApiArray,
};
