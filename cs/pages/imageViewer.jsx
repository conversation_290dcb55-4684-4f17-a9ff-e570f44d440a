import React from 'react';

import { isClientRendering } from '../lib/helpers';

import ImageViewer from '../components/imageViewer/imageViewer';
import { mountPageComponent } from '../lib/reactHelpers/client';
import { getLocationQuery } from '../lib/url';
import eventTracking from '../services/analytics/eventTracking';

import '../styles/public/imageViewer.css';

async function bootstrap() {
    const { file, print } = getLocationQuery(location);
    mountPageComponent(<ImageViewer isPrintMode={print === '1'} url={file} />);
}

if (isClientRendering()) {
    bootstrap();
    eventTracking.setCustomDimensions(
        { dimension1: 'imageViewer', dimension2: 'logged_in' },
        true,
    );
}
