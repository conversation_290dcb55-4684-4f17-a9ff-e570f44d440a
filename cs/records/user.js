import I from 'immutable';
import PropTypes from 'prop-types';

import { ROLES_SORTER } from './utils';

import { apiBillData, apiBillingAccountData, apiRatesData } from './billing';

export const Company = new I.Record({
    id: null,
    edrpou: null,
    ipn: null,
    name: null,
    isLegal: false,

    phone: null,

    renderSignatureInInterface: true,
    renderSignatureOnPrintDocument: true,

    emailDomains: null,
    allowedIps: null,
    allowedApiIps: null,
    inactivityTimeout: null,

    config: null,
    billingCompanyConfig: null,
    dateCreated: null,

    roles: new I.List(),

    billingAccounts: new I.List(),
    rates: new I.List(),
    bills: new I.List(),
    trialRates: new I.List(),
    activeRates: new I.List(),
    rateExtensions: new I.List(),

    usedDocumentCount: null,

    hasInvalidSignedDocuments: false,
});
export const CompanyPropType = PropTypes.instanceOf(Company);

export const CompanyConfig = new I.Record({
    master: false,
    adminIsSuperAdmin: false,
    renderSignatureAtPage: 'first',
    renderSignatureInInterface: true,
    renderSignatureOnPrintDocument: true,
    renderReviewInInterface: true,
    renderReviewOnPrintDocument: true,
    syncContactsEnabled: false,
});
export const CompanyConfigPropType = PropTypes.instanceOf(CompanyConfig);

export const EmployeeRole = new I.Record({
    id: null,
    status: null,

    user: null,
    userRole: null,

    canViewDocument: null,
    canViewPrivateDocument: null,
    canCommentDocument: null,
    canUploadDocument: null,
    canDownloadDocument: null,
    canPrintDocument: null,
    canDeleteDocument: null,
    canSignAndRejectDocument: null,
    canRemoveItselfFromApproval: null,
    canInviteCoworkers: null,
    canEditCompany: null,
    canEditRoles: null,
    canCreateTags: null,
    canEditDirectories: null,
    canEditDocumentTemplates: null,
    canEditDocumentFields: null,
    canArchiveDocuments: null,
    canDeleteArchivedDocuments: null,
    canEditTemplates: null,
    canEditRequiredFields: null,
    canEditCompanyContact: null,
    canDownloadActions: null,
    canEditSecurity: null,
    canChangeDocumentSignersAndReviewers: null,
    canDeleteDocumentExtended: null,
    canEditDocumentCategory: null,
    canViewCoworkers: null,

    canReceiveInbox: null,
    canReceiveInboxAsDefault: null,
    canReceiveNewRoles: null,
    canReceiveTokenExpiration: null,
    canReceiveAdminRoleDeletion: null,
    canReceiveEmailChange: null,
    canReceiveComments: null,
    canReceiveRejects: null,
    canReceiveReminders: null,
    canReceiveReviews: null,
    canReceiveFinishedDocs: null,
    canReceiveNotifications: null,
    canReceiveAccessToDoc: null,
    canReceiveDeleteRequests: null,
    canReceiveReviewProcessFinished: null,
    canReceiveReviewProcessFinishedAssigner: null,
    canReceiveSignProcessFinished: null,
    canReceiveSignProcessFinishedAssigner: null,

    canViewClientData: null,
    canEditClientData: null,
    canEditSpecialFeatures: null,

    registrationReferralUrl: null,

    showChildDocuments: null,

    hasFewSignatures: false,

    isAdmin: false,

    isDefaultRecipient: false,

    allowedIps: new I.OrderedSet(),
    allowedApiIps: new I.OrderedSet(),
    tags: new I.List(),
    fields: new I.List(),
    hasToken: false,
    position: null,

    dateCreated: null,

    dateDeleted: null,
    deletedBy: null,

    invitedBy: null,
    dateInvited: null,

    activatedBy: null,
    activationSource: null,
    dateActivated: null,
});

export const EmployeeRolePropType = PropTypes.instanceOf(EmployeeRole);

export const Role = new I.Record({
    id: null,
    status: null,

    companyEdrpou: null,
    company: null,
    userRole: null,

    canViewDocument: null,
    canViewPrivateDocument: null,
    canCommentDocument: null,
    canUploadDocument: null,
    canDownloadDocument: null,
    canPrintDocument: null,
    canDeleteDocument: null,
    canSignAndRejectDocument: null,
    canRemoveItselfFromApproval: null,
    canInviteCoworkers: null,
    canEditCompany: null,
    canEditRoles: null,
    canCreateTags: null,
    canEditDirectories: null,
    canEditDocumentTemplates: null,
    canEditDocumentFields: null,
    canArchiveDocuments: null,
    canDeleteArchivedDocuments: null,
    canEditTemplates: null,
    canEditRequiredFields: null,
    canEditCompanyContact: null,
    canEditSecurity: null,
    canDownloadActions: null,
    canChangeDocumentSignersAndReviewers: null,
    canDeleteDocumentExtended: null,
    canEditDocumentCategory: null,
    canViewCoworkers: null,

    canReceiveInbox: null,
    canReceiveInboxAsDefault: null,
    canReceiveNewRoles: null,
    canReceiveTokenExpiration: null,
    canReceiveAdminRoleDeletion: null,
    canReceiveEmailChange: null,
    canReceiveComments: null,
    canReceiveRejects: null,
    canReceiveReminders: null,
    canReceiveReviews: null,
    canReceiveFinishedDocs: null,
    canReceiveNotifications: null,
    canReceiveAccessToDoc: null,
    canReceiveDeleteRequests: null,
    canReceiveReviewProcessFinished: null,
    canReceiveReviewProcessFinishedAssigner: null,
    canReceiveSignProcessFinished: null,
    canReceiveSignProcessFinishedAssigner: null,

    hasSignedDocuments: false,

    hasFewSignatures: false,

    isAdmin: false,
    isMasterAdmin: false,

    canViewClientData: false,
    canEditClientData: false,
    canEditSpecialFeatures: false,

    sortDocuments: null,
    showInviteTooltip: null,
    showChildDocuments: null,

    dateAgreed: null,
    position: null,
});
export const RolePropType = PropTypes.instanceOf(Role);

export const SignSession = new I.Record({
    id: null,
    documentId: null,

    roleId: null,
    edrpou: null,
    email: null,
    isLegal: null,

    type: null,
    status: null,
    documentStatus: null,
    source: null,

    phone: null,
    isPhoneVerified: false,

    roleStatus: null,

    finishUrl: null,
    cancelUrl: null,
    signParameters: null,
});
export const SignSessionPropType = PropTypes.instanceOf(SignSession);

export const UserMeta = new I.Record({
    mobileUsage: false,
    hasMobileApp: false,
    hasActiveMobileApp: false,
});

export const User = new I.Record({
    id: null,

    email: null,
    phone: null,
    firstName: null,
    secondName: null,
    lastName: null,
    label: null,
    language: null,

    source: null,
    emailConfirmed: false,
    dateCreated: null,
    registrationCompleted: false,
    isAutogeneratedPassword: false,
    registrationMethod: null,
    is2FAEnabledInProfile: false,
    is2FAEnabledByRule: false,
    isPhoneVerified: false,
    isSubscribedEsputnik: false,
    isRegistered: false,
    showKEPAppPopup: false,
    createdBy: null,

    currentCompany: new Company(),
    currentCompanyConfig: new CompanyConfig(),
    currentRole: new Role(),

    roleId: null,
    roles: new I.List(),

    trialAutoEnable: false,

    userMeta: new UserMeta(),

    hasPassword: false,
    activeSurveys: null,
});
export const UserPropType = PropTypes.instanceOf(User);

export const hasPermission = (user, permission) => {
    return (
        user.currentRole &&
        (user.currentRole.isAdmin || user.currentRole.get(permission))
    );
};

export const hasPermissionFactory = (user) => {
    return (permission) => hasPermission(user, permission);
};

const prepareRoleData = (roleData) => {
    return {
        ...roleData,
        allowedIps: new I.OrderedSet(roleData.allowedIps),
        allowedApiIps: new I.OrderedSet(roleData.allowedApiIps),
    };
};

const prepareUserData = (userData) => {
    const { firstName, email } = userData;
    return {
        ...userData,
        label: firstName || email,
    };
};

const apiUser = (userData) => {
    return userData ? new User(prepareUserData(userData)) : null;
};

export const apiEmployeeRole = (roleData) => {
    return new EmployeeRole({
        ...prepareRoleData(roleData),
        user: apiUser(roleData.user),
        hasToken: roleData.hasToken,
    });
};

export const apiCompanyConfig = (company) => {
    const { edrpou } = company;
    const configData = company.config;
    return new CompanyConfig({
        ...configData,
        adminIsSuperAdmin: configData.admin_is_superadmin,
        renderSignatureAtPage: configData.render_signature_at_page,
        renderReviewInInterface: configData.render_review_in_interface,
        renderReviewOnPrintDocument: configData.render_review_on_print_document,
        syncContactsEnabled: config.SYNC_CONTACTS_API.indexOf(edrpou) !== -1,
    });
};

export const apiCompany = (companyData) => {
    return new Company({
        ...companyData,
        config: companyData.config,
        roles: new I.List(
            companyData.roles && companyData.roles.map(apiEmployeeRole),
        ),
        billingAccounts: new I.List(
            companyData.billingAccounts &&
                companyData.billingAccounts.map(apiBillingAccountData),
        ),
        billingCompanyConfig: companyData.billingCompanyConfig,
        rates: new I.List(
            companyData.rates && companyData.rates.map(apiRatesData),
        ),
        bills: new I.List(
            companyData.bills && companyData.bills.map(apiBillData),
        ),
        trialRates: new I.List(
            companyData.trialRates && companyData.trialRates.map(apiRatesData),
        ),
        activeRates: new I.List(companyData.activeRates),
    });
};

export const apiRole = (roleData) => {
    return new Role({
        ...prepareRoleData(roleData),
        company: roleData.company && apiCompany(roleData.company),
    });
};

export const apiCurrentRole = (roleData) => {
    const { company, user } = roleData;
    return new User({
        ...prepareUserData(user),

        currentCompany: apiCompany(company),
        currentCompanyConfig: apiCompanyConfig(company),
        currentRole: apiRole(roleData),

        roleId: roleData.id,
        roles: new I.List(user.roles && user.roles.map(apiRole)).sort(
            ROLES_SORTER,
        ),
    });
};

export const apiCurrentSignSession = (signSessionData) => {
    return new SignSession(signSessionData);
};

export const apiCurrentSignSessionUser = (signSession) => {
    const {
        edrpou: companyEdrpou,
        email,
        isLegal,
        roleId,
        type,
        phone,
        isPhoneVerified,
        language,
    } = signSession;

    const company = new Company({
        edrpou: companyEdrpou,
        isLegal,
    });
    const isSignSession = type === 'sign_session';

    return new User({
        email,
        language,

        emailConfirmed: true,
        registrationCompleted: true,

        // Fields for Mobile ID in sign session
        phone,
        isPhoneVerified,

        currentCompany: company,
        currentRole: new Role({
            canDownloadDocument: true,
            canPrintDocument: true,
            canCommentDocument: isSignSession && Boolean(roleId),
            canSignAndRejectDocument: isSignSession,
            company,
            companyEdrpou,
            id: roleId,
            status: signSession.roleStatus,
        }),

        roleId,
    });
};

export const apiCurrentUser = apiUser;

export const apiUserWithRoles = (userData) => {
    return new User({
        ...prepareUserData(userData),
        roles: new I.List(userData.roles && userData.roles.map(apiRole)).sort(
            ROLES_SORTER,
        ),
    });
};

export const apiUsersWithRoles = (usersData) => {
    return new I.List(usersData.map((userData) => apiUserWithRoles(userData)));
};

export const apiCompanies = (companiesData) => {
    return new I.List(
        companiesData.map((companyData) => apiCompany(companyData)),
    );
};

export default {
    apiCurrentRole,
    apiCurrentSignSession,
    apiCurrentUser,
    apiUserWithRoles,
    apiUsersWithRoles,
    hasPermissionFactory,
};
