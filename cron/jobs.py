from collections.abc import Awaitable, Callable

from aiohttp import web

from app.lib.datetime_utils import utc_now
from app.services import services
from worker import topics


class CronJobTopic:
    """
    Cron job wrapper for Kafka producer
    Behaves like a function that sends a message to <PERSON><PERSON><PERSON>tivation:
    Why not just a function?
    Because we'd like to override __repr__ to make logging more informative,
    especially when we have a lot of cron jobs to endicate which one failed.

    So this class is equivalent to:
    async def cron_job(app: web.Application) -> None:
        await app['kafka'].send_record(topic, value={'start_time': utc_now().timestamp()})

    But in logs it will be displayed as like: <function cron_job at 0x7f7b7b7b7b70>
    while this class: <CronJobTopic: topic>
    """

    def __init__(self, topic: str) -> None:
        self.topic = topic
        self.__name__ = topic

    def __repr__(self) -> str:
        return f'<CronJobTopic: {self.topic}>'

    def __call__(self, app: web.Application) -> Awaitable[None]:
        # NOTE: use "parse_utc_timestamp" to parse timestamp to TZ-aware datetime in UTC timezone
        return app['kafka'].send_record(self.topic, value={'start_time': utc_now().timestamp()})


def _wrap(topic: str) -> Callable[[web.Application], Awaitable[None]]:
    cron_job = CronJobTopic(topic)
    return cron_job


send_notification_about_sign_process = _wrap(topics.SEND_SIGN_PROCESS_NOTIFICATION)
delete_old_trigger_notifications = _wrap(topics.DELETE_OLD_TRIGGER_NOTIFICATION)
cleanup_old_mobile_notifications = _wrap(topics.CLEANUP_OLD_MOBILE_NOTIFICATIONS)
activate_banner = _wrap(topics.ACTIVATE_BANNER)
reminder_to_unregistered = _wrap(topics.SEND_REMINDERS_TO_UNREGISTERED_USERS)
cleanup_unsubscription = _wrap(topics.CLEANUP_UNSUBSCRIPTIONS)
activate_company_rates = _wrap(topics.ACTIVATE_COMPANY_RATES)
deactivate_company_extensions_trial = _wrap(topics.DEACTIVATE_COMPANY_EXTENSIONS_TRIAL)
activate_company_extensions = _wrap(topics.ACTIVATE_COMPANY_EXTENSIONS)
deactivate_company_rates = _wrap(topics.DEACTIVATE_COMPANY_RATES)
start_sending_pending_review_reminders = _wrap(topics.START_SENDING_PENDING_REVIEW_REMINDERS)
daily_notification_about_finished_documents = _wrap(
    topics.SCHEDULE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS
)
send_accepted_documents = _wrap(topics.SEND_ACCEPTED_DOCUMENTS)
send_pending_notifications = _wrap(topics.SEND_PENDING_NOTIFICATIONS)
trigger_low_balance_billing_notification = _wrap(topics.INITIATE_LOW_BALANCE_BILLING_NOTIFICATIONS)
remind_about_employees_extension_bill_when_trial_expires = _wrap(
    topics.REMIND_ABOUT_EMPLOYEES_EXTENSION_BILL_WHEN_TRIAL_EXPIRES
)
gather_reminders_for_unpaid_bills = _wrap(topics.GATHER_REMINDERS_FOR_UNPAID_BILLS)
send_daily_feedbacks = _wrap(topics.SEND_DAILY_FEEDBACKS)
send_reminder_about_new_documents = _wrap(topics.SEND_REMINDER_ABOUT_NEW_DOCUMENTS)
send_about_abandoned_registration = _wrap(topics.INITIATE_REMINDERS_FOR_ABANDONED_REGISTRATION)
send_reminder_about_token_expiration = _wrap(topics.SEND_REMINDER_ABOUT_TOKEN_EXPIRATION)

fetch_privatbank_transactions = _wrap(topics.FETCH_PRIVATBANK_TRANSACTIONS)
fetch_pumb_transactions = _wrap(topics.FETCH_PUMB_TRANSACTIONS)
delete_invalid_accounts = _wrap(topics.DELETE_BILLING_ACCOUNTS)

create_document_actions_table_partition = _wrap(topics.CREATE_DOCUMENT_ACTIONS_TABLE_PARTITION)
create_user_actions_table_partition = _wrap(topics.CREATE_USER_ACTIONS_TABLE_PARTITION)

process_delayed_task = _wrap(topics.PROCESS_DELAYED_TASK)

cleanup_old_drafts = _wrap(topics.CLEANUP_OLD_DRAFTS)

start_vchasno_profile_sync = _wrap(topics.START_VCHASNO_PROFILE_SYNC)

cleanup_old_actions_report_files = _wrap(topics.CLEANUP_OLD_ACTIONS_REPORT_FILES)


async def process_contact_indexation_queue(app: web.Application) -> None:
    """
    Schedule indexation of contact recipients that were never indexed before.
    """
    await services.kafka.send_record(
        topic=topics.INDEX_CONTACT_RECIPIENTS,
        value={
            'min_iteration': 0,
            'max_iteration': 0,
        },
    )


async def process_failed_contact_indexation_queue(app: web.Application) -> None:
    """
    Schedule indexation of contact recipients that were failed to index
    at least once, but not more than 10 times.
    """
    await services.kafka.send_record(
        topic=topics.INDEX_CONTACT_RECIPIENTS,
        value={
            'min_iteration': 1,
            'max_iteration': 10,
        },
    )


async def call_esputnik_tov_trial_rate_expiring_event(_: web.Application, offset: int = 0) -> None:
    await services.kafka.send_record(
        topics.ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT, {'offset': offset}
    )


async def call_esputnik_rate_expiring_event(_: web.Application, offset: int = 0) -> None:
    await services.kafka.send_record(
        topics.ESPUTNIK_GENERATE_RATE_EXPIRING_EVENT, {'offset': offset}
    )


async def unsigned_documents_notification(app: web.Application) -> None:
    """Trigger unsigned documents notification job"""
    await services.kafka.send_record(
        topic=topics.INITIATE_REMINDERS_FOR_UNSIGNED_DOCUMENTS,
        value={'limit': 20_000, 'offset': 0},
    )
