from app.lib.helpers import immutable_dict
from app.lib.types import DataDict
from cron import jobs
from cron.jobs import process_delayed_task

# Frequent tasks
MINUTELY_CONFIG = immutable_dict(
    {
        'minutes': {
            1: (process_delayed_task,),
            2: (
                jobs.process_contact_indexation_queue,
                jobs.start_vchasno_profile_sync,
            ),
            5: (
                jobs.cleanup_unsubscription,
                jobs.send_accepted_documents,
                jobs.process_failed_contact_indexation_queue,
            ),
            15: (
                jobs.send_pending_notifications,
                jobs.fetch_privatbank_transactions,
                jobs.fetch_pumb_transactions,
            ),
            60: (
                jobs.trigger_low_balance_billing_notification,
                jobs.activate_company_rates,
                jobs.deactivate_company_rates,
                jobs.activate_company_extensions,
                jobs.deactivate_company_extensions_trial,
            ),
        }
    }
)

# Daily tasks
_TUESDAY_CONFIG: DataDict = {
    '11:15': (jobs.unsigned_documents_notification, jobs.start_sending_pending_review_reminders),
}
_WEDNESDAY_CONFIG: DataDict = {
    # TODO: remove after all unregistered email receive at least 2 reminders
    # '12:10': (jobs.reminder_to_unregistered, ),
}
_FRIDAY_CONFIG: DataDict = {
    '11:15': (jobs.unsigned_documents_notification, jobs.start_sending_pending_review_reminders),
}

SINGLE_DAY_CONFIG = immutable_dict(
    {
        'tuesday': _TUESDAY_CONFIG,
        'wednesday': _WEDNESDAY_CONFIG,
        'friday': _FRIDAY_CONFIG,
    }
)

_WORKDAY_CONFIG = {
    '00:00': (jobs.delete_invalid_accounts,),
    '10:00': (
        jobs.send_daily_feedbacks,
        jobs.send_reminder_about_new_documents,
    ),
    '12:00': (
        jobs.call_esputnik_rate_expiring_event,
        jobs.call_esputnik_tov_trial_rate_expiring_event,
    ),
    '13:00': (jobs.send_about_abandoned_registration,),
    '14:00': (jobs.send_reminder_about_token_expiration,),
}
WORKDAYS_CONFIG = immutable_dict(
    {
        'monday': _WORKDAY_CONFIG,
        'tuesday': _WORKDAY_CONFIG,
        'wednesday': _WORKDAY_CONFIG,
        'thursday': _WORKDAY_CONFIG,
        'friday': _WORKDAY_CONFIG,
    }
)

_EVERY_DAY_CONFIG = {
    # "*:00" means that every hour at 00 minutes the job will be executed
    '*:00': (jobs.send_notification_about_sign_process,),
    '01:00': (
        jobs.delete_old_trigger_notifications,
        jobs.cleanup_old_mobile_notifications,
        jobs.create_document_actions_table_partition,
        jobs.create_user_actions_table_partition,
        jobs.cleanup_old_drafts,
    ),
    '02:00': (
        jobs.activate_banner,
        jobs.cleanup_old_actions_report_files,
    ),
    '08:30': (
        jobs.daily_notification_about_finished_documents,
        jobs.gather_reminders_for_unpaid_bills,
    ),
    '09:00': (jobs.remind_about_employees_extension_bill_when_trial_expires,),
}

EVERY_DAYS_CONFIG = immutable_dict(
    {
        'monday': _EVERY_DAY_CONFIG,
        'tuesday': _EVERY_DAY_CONFIG,
        'wednesday': _EVERY_DAY_CONFIG,
        'thursday': _EVERY_DAY_CONFIG,
        'friday': _EVERY_DAY_CONFIG,
        'saturday': _EVERY_DAY_CONFIG,
        'sunday': _EVERY_DAY_CONFIG,
    }
)

_WEEKEND_CONFIG = {'00:00': (jobs.delete_invalid_accounts,)}

WEEKENDS_CONFIG = immutable_dict({'saturday': _WEEKEND_CONFIG, 'sunday': _WEEKEND_CONFIG})
