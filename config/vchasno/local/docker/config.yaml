app:
  debug: true
  demo: true
  test: false
  port: 8000
  domain: http://localhost:8000
  static_host: http://localhost:8000/static
  static_prefix: /static
  client_max_size: 100

  request_timeout: 14.5
  delete_s3_timeout: 12.5
  upload_timeout: 29.5
  upload_s3_timeout: 25
  papka24_timeout: 5
  sync_roles_timeout: 7.5
  sign_timeout:
    default: 16
    cesaris: 50
  shutdown_timeout: 60
  slow_urls:
    - /api/private/blackbox/documents
    - /api/v1/documents
    - /api/v2/documents
    - /downloads/actions/documents.csv
    - /internal-api/companies
    - /internal-api/companies/mobile-id
    - /internal-api/contacts/dossier
    - /internal-api/contacts/sync
    - /internal-api/documents
    - /internal-api/documents/import/papka24
    - /internal-api/proxy
    - /internal-api/registration/signers
    - /internal-api/signatures/mobile-id
  slow_urls_patterns:
    - /internal-api/documents/.+/signatures
    - /internal-api/documents/.+/xml-to-pdf

  trusted_origins:
    - http://localhost:5000
    - http://localhost:6060

  brand: Вчасно
  logo_path: images/logos/vchasno.png
  session_max_age: 2592000  # = 60 secs * 60 mins * 24 hrs * 30 days
  cookie_name: vchasno_local_session
  cookie_domain: localhost
  cookie_secure: false
  domain_name: edo-local.vchasno.ua
  info_email: <EMAIL>
  noreply_email: <EMAIL>
  support_email: <EMAIL>
  sales_email: <EMAIL>
  support_phone: '+38 044 392 03 00'
  support_phone_lifecell: '+38 063 460 5 333'
  support_phone_vodafone: '+38 050 416 5 333'
  support_phone_kyivstar: '+38 067 460 5 333'
  support_viber: 'vchasnoedo'
  support_telegram: 'vchasnoedo_bot'
  support_hours: '8:00 - 19:00'

  fernet_key: 1bnLESCOe_L1jynhDxUiBxSdQMzKsWWzleW9YRoPbB4=
  test_edrpou: '11111111'
  sign_widget_iit_url: https://eu.iit.com.ua/sign-widget/v20200922/
  sign_widget_iit_fozzy_url: https://eu.iit.com.ua/sign-widget/v20240301/
  default_language: uk
  available_languages:
    - uk
    - en
theme:
  primary_color: "#FFB200"
auth:
  totp_interval: 180
  totp_secret_salt: IIEGA2G6QPZRGCQJYOLWQGEQBVN7AFZH3VY4SG2YJTYGQ4QJTZLA====
fcm:
  is_enabled: false
  project_id: fake
  config_path: fake
feature_flags:
  pass_sign_info_to_backend: false
  multi_download: true
  new_worker: true
  pro_rate_allow_for_free: false
google_auth:
  # Project "Vchasno-Login-With-Google-LCL" on Google Cloud
  client_id_web_edo: '510033946623-6pgp48t7cea4ub35hd6hndmh2l2tk1or.apps.googleusercontent.com'
  # Projects on Google Cloud:
  # EDO stg (id: edo-stg-90c90)
  # KASA stg (id: kasa-stg-dc85f)
  # KEP stg (id: kep-stg)
  client_id_ios_edo: '443532960258-6o4vo9vlvdf1vcn417ueu1fsm6v75p2s.apps.googleusercontent.com'
  client_id_ios_kasa: '511723122321-q6bu3dqog3kasgoavchp1ekkrboog3n3.apps.googleusercontent.com'
  client_id_ios_kep: '342419402706-q3e75unr9hjck6qbv7md8ml0mp1jqdkc.apps.googleusercontent.com'
  client_id_android_edo: '443532960258-b4su2b3j8bbrrejubfas3mqg92phc0rn.apps.googleusercontent.com'
  client_id_android_kasa: '511723122321-hdm5bahvlm9j1uesafg6dgu9lai3m6d7.apps.googleusercontent.com'
  client_id_android_kep: '342419402706-rmdm64ge6igv5k1t4ce1mg78p36rb6hl.apps.googleusercontent.com'
microsoft_auth:
  client_id: bc93a50a-8a4f-458a-8fac-cca4c2411604
apple_auth:
  team_id: JNSLSBSJH7
  edo_web_client_id: ua.vchasno.dev
  edo_client_id: ua.vchasno.edo.dev
  kep_client_id: ua.vchasno.cap.dev
  kasa_client_id: ua.vchasno.kasa.dev
  key_id: 6ZRPX8JS98
  key: |-
*****************************************************************************************************************************************************************************************************************************************************************************************
tokens:
  public_key: config/vchasno/local/keys/tokens.pub
  private_key: config/vchasno/local/keys/tokens.pem
db:
  url: **************************************/evodoc?client_encoding=utf-8
  url_readonly: **************************************/evodoc?client_encoding=utf-8
  minsize: 1
  maxsize: 5
  timeout: 5.0
  use_aiopg_connection_pool: true
events_db:
  url: *********************************************/evodoc_events?client_encoding=utf-8
  minsize: 1
  maxsize: 5
  timeout: 5.0
  use_aiopg_connection_pool: true
es:
  hosts: [ 'http://elasticsearch:9200' ]
  document_index: documents
  comment_index: comments
  contact_recipient_index: contact_recipients
gzip_original:
  - .txt
  - .xml
kafka:
  bootstrap_servers:
    - 'kafka:9092'
  callback_timeout: 2
  client_id: kafka-vchasno-dev
  group_id: kafka-vchasno-dev
  producer:
    request_timeout_ms: 40000
  consumer:
    session_timeout_ms: 30000
  topic_prefix: 'vchasno'
redis:
  host: redis
  port: 6379
  db: 0
  timeout: 1.0
s3:
  type: minio
  host: http://minio:9000
  access_key: minio
  secret_key: miniosecret
  bucket: edo-2
  region_name: eu-central-1
  read_timeout: 3
  connect_timeout: 0.5
  connections_limit: 1000
  signature_version: s3v4
  encryption_keys:
    - 't5nk+knQXHj9/gc0XtDNXcRXmw2QFQ3EXs9Tv3xdRcc='
bedrock:
  region_name: us-east-1
  access_key: fake
  secret_key: fake
smtp:
  host: maildev
  port: 1025
  timeout: 12.5
  xmailq: vchasno.com.ua
  start_tls: false
  use_tls: false
evo_sender:
  project_id: 'vchasno.com.ua'
  base_url: 'https://sender-dev.vchasno.com.ua/'
sync_contacts_api:
  e36507036: # promua
    secret_key: 35bdf49d-f79b-42c1-abea-86d0242acbcf
    url: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info
    url_by_invoice: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info_by_invoice_id
    param: token
    limit: 100
    prefix: 'ua-'
  e40283641: # zakupki
    secret_key: 89325b62-737e-4210-a406-a2719d4b8f07
    url: https://my.zakupki.prom.ua/remote/public_api/merchant_contact_info
    param: access_token
    limit: 20
contacts:
  # TODO[KG]: merge with `sync_contacts_api` (+ changes in secrets config)
  uploads:
    allowed_extensions:
      - .csv
      - .xlsx
    max_file_size: 5  # MB
url2pdf:
  # "url2pdf.ua" is not a real domain, it's a name of service in docker-compose.yaml
  url: http://url2pdf.ua:8090/
  use_xz: true
  timeout: 30.0
doc2pdf:
  url: http://app.doc2pdf.vchasno.svc.c.evo:8080/
  timeout: 15
vchasno_signer:
  proxy_service_url: /internal-api/proxy
  use_context: true
zookeeper:
  host: 0.0.0.0
  port: 2181
consts:
  gen_archive_ttl: 300 # ttl for gen archive task in seconds
  max_files: 20 # max files in one archive
concierge:
  service: vchasno
  backend_url: http://localhost:8000/~concierge-sandbox
  frontend_url: http://localhost:8000/~concierge-sandbox
  cookie_name: concierge_sandbox
  cookie_domain: localhost
  token: supersecret
edi:
  api_url: http://dockerhost:5000/jsonrpc
  rpc_auth_token: ************************************
  host: http://dockerhost:5000
  landing_url: https://edi.vchasno.com.ua
ttn:
  host: http://0.0.0.0:5060
  landing_url: https://ttn.vchasno.com.ua
  auth_token: supersecret
kassa:
  host: http://0.0.0.0:5050
  landing_url: https://kasa.vchasno.com.ua
  auth_token: supersecret
kep:
  host: http://localhost:6060
  landing_url: https://cap.vchasno.com.ua
  auth_token: fake
zvit:
  # Trailing / is essential for Zvit (/app/)
  host: http://0.0.0.0:5070/app/
  landing_url: https://zvit.vchasno.ua
cloud_signer:
  host: http://dockerhost:5055
  auth_token: kep_cloud_signer_api_token
diia:
  host: https://api2s.diia.gov.ua
  token: vchasno-test-token-hk92es
  offer_id: 78d8b223a604fb8eab33465c4579416227d99b7e23c2f36d9d0263169cddc506f9979eb86c9c774139ca9747b290796f7bc884455cb7c2396687f957d24f1126
  branch_id: 354617329bc229748bcf5316990c25021c98e7e28c4730664caaa0ac5aa1b5ad4a95d6f1f1aef2aea99f0e6f05017aa347baa26954ad5d72b697027f519ab54e
  auth_acquirer_token: fake
integrations:
  header: X-Vchasno-Private-Token
  # token for all projects: "701f62f6-c16d-4a5f-8190-1038533ed923"
  zakupki:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  kep:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  kasa:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  ttn:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  hrs:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  landing:
    token_hash_b64: JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JG9RSS95QlZUUEN0MHh3eXNFTllvWkEkTWZkaDh2aXlhM0NvTzRELy9wNXVRWXgvN1AyazRXS3IxMHdwRERhNFpuWQ==
  creatio:
    token_hash_b64: JGFyZ29uMmkkdj0xOSRtPTE2LHQ9MixwPTEkVEdoMlltaGtaSEE1WWpac00yTmFiUSRzL0orcnYrc21kVnpLVEpsZmJjdC9R
antivirus:
  host: http://dockerhost:5004
crm:
  url: http://dockerhost:9095
  token: EDO
signer_web:
  host: https://signservice.stg.evo/rust
  secret: stg-secret-token
  key_id: d2aa024b-e35f-48e3-bdfa-af636b0d76f8 # ******** key
evopay:
  host: https://wallet-ewdev.rozetkapay.com
  public_key: vchasno_test
  private_key: sy7hhRAPyzVEK4972H3fgXN27lE6m72Y
  evopay_webhook_key: evopay
  purpose: vchasno_edo
privatbank:
  host: https://acp.privatbank.ua
  integration_id: dev_integration_id
  integration_token: dev_integration_token
  account_number: *****************************
pumb:
  auth_host: https://auth.fuib.com/
  api_host: https://service.fuib.com:4103/
  client_id: corpcli
  client_secret: 9160f6d8-d658-4637-9b0e-3bb30524b5d8
  username: volobuiem
  password: FAKE
  account_id: *********
collabora:
  url: http://collabora:9980/collabora
gotenberg:
  url: http://gotenberg:3000
hrs:
  api_url: http://dockerhost:8080/
  token: fake-token
typeform:
  form_id: null
