import logging
import uuid
from datetime import (
    date,
    timedelta,
)

from aiohttp import web

from api.errors import ConflictError, DoesNotExist, InvalidRequest
from app import esputnik
from app.auth.db import (
    count_company_roles_group_by_company,
    select_company,
    select_company_by_edrpou,
    select_company_by_id,
    select_user_by_email,
    select_user_by_role_id,
)
from app.auth.enums import SystemAccountEmail
from app.auth.utils import is_fop
from app.billing import api
from app.billing.api import (
    create_free_rate,
    delete_account,
)
from app.billing.constants import RATES_NAME_MAP
from app.billing.db import (
    insert_bank_transactions_batch,
    select_account_by_id,
    select_active_company_rates,
    select_bank_transaction,
    select_bill_by_id,
    select_employee_extensions_for_activation,
    select_employee_extensions_trial_for_deactivation,
    update_bill,
    update_billing_company_config,
    update_rate_extension,
)
from app.billing.emailing import send_bill
from app.billing.enums import (
    AccountRate,
    BillActivationSource,
    BillingAccountSource,
    BillStatus,
    CompanyLimit,
    CompanyRateStatus,
    RateExtensionStatus,
)
from app.billing.integrations.privat import PrivatbankClient
from app.billing.integrations.pumb import PumbClient
from app.billing.types import BankClient, CompanyRate, RateExtension
from app.billing.utils import (
    activate_company_employees_extension,
    activate_service_by_bill_payment,
    bill_to_pdf,
    get_billing_company_config,
    get_entity_expiring_event,
    is_employee_limit_reached,
    process_extra_roles,
    should_create_free_rate,
)
from app.billing.validators import (
    validate_activate_employee_extension,
    validate_bill_activation,
    validate_transaction_payment,
)
from app.crm.utils import send_rate_to_crm, send_transaction_to_crm
from app.esputnik.enums import Event
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.constants import VCHASNO_COMPANY_EDRPOU
from app.lib.datetime_utils import (
    DAY,
    end_of_day,
    local_now,
    to_local_datetime,
    utc_now,
)
from app.lib.enums import Source, UserRole
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.profile.db import select_company_users
from app.profile.utils import update_esputnik_company_rate
from app.registration.utils import activate_tov_trials_on_registration
from app.services import services
from app.uploads.types import File
from worker import topics
from worker.billing.enums import ChargeType
from worker.billing.utils import (
    apply_bill_signature_on_web,
    filter_already_fetched_transactions,
    is_company_has_another_paid_rate,
    select_admins_and_positions_by_company_id_for_notifications,
    select_billing_accounts_expiring_soon_without_next_paid,
    select_invalid_accounts,
    select_rate_for_activation,
    select_rate_for_deactivation,
    select_tov_trial_billing_accounts_expiring_soon_without_next_paid,
    select_users_emails_for_esputnik,
    send_bill_to_recipient,
    send_event,
    sign_bill_web_signer,
    update_transaction_on_fail,
    update_transaction_on_success,
    upload_bill_to_web,
)
from worker.esputnik.utils import get_esputnik_contact_by_email
from worker.utils import retry_config

DEV_COMPANY_EDRPOU = '********'


async def fetch_privatbank_transactions(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Fetching Privatbank transactions
    and activating rates/adding documents by payment
    """

    if not get_flag(FeatureFlags.ENABLE_PRIVATBANK_PAYMENT):
        logger.info('Privatbank integration is unavailable.')
        return

    client = PrivatbankClient()
    settings = await client.fetch_settings()
    if not settings.can_make_request_to_api:
        logger.info('Cant make request to Privatbank due to volatile API')
        return

    await _process_bank_transactions(client, data, logger)


async def fetch_pumb_transactions(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Fetching PUMB transactions
    and activating rates/adding documents by payment
    """

    if not get_flag(FeatureFlags.ENABLE_PUMB_PAYMENT):
        logger.info('PUMB integration is unavailable.')
        return

    client = PumbClient()

    await _process_bank_transactions(client, data, logger)


async def process_activate_bank_transaction(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Process bank transaction:
     - match with bill (by seqnum or edrpou+amount)
     - Validates bank transaction and bill state
     - Activates rate services by bill payment
     - Updates transaction state and send transaction info to crm
    """
    bill = None
    transaction_id = data.get('transaction_id')

    logger.info('Start processing bank transaction', extra={'transaction_id': transaction_id})

    async with services.db.acquire() as conn:
        transaction = await select_bank_transaction(conn=conn, transaction_id=transaction_id)

        if not transaction:
            logger.error('Transaction not found', extra={'transaction_id': transaction_id})
            return

        try:
            bill = await validate_bill_activation(conn, transaction)

            await validate_transaction_payment(
                conn=conn,
                bill=bill,
                transaction=transaction,
            )
            await activate_service_by_bill_payment(
                conn=conn, bill=bill, source=BillActivationSource.bank
            )

            await update_transaction_on_success(
                conn=conn,
                transaction=transaction,
                bill=bill,
            )

        except (InvalidRequest, DoesNotExist) as error:
            await update_transaction_on_fail(
                conn=conn,
                transaction=transaction,
                bill=bill,
                error=error,
            )
        finally:
            await send_transaction_to_crm(transaction_id=transaction.transaction_id)


async def _process_bank_transactions(
    client: BankClient, data: DataDict, logger: logging.Logger
) -> None:
    start_date = data.get('start_date')
    end_date = data.get('end_date')

    transactions = await client.fetch_transactions(
        start_date=start_date,
        end_date=end_date,
    )

    log_extra = {
        'bank_client': client.__class__.__name__,
        'start_date': start_date,
        'end_date': end_date,
    }
    logger.info(
        'Bank transactions are fetched. Continue to processing',
        extra={
            'transactions_count': len(transactions),
            **log_extra,
        },
    )

    filtered_transactions = await filter_already_fetched_transactions(transactions)
    if not filtered_transactions:
        logger.info(
            'No new transactions on fetch',
            extra={**log_extra, 'transactions_count': len(transactions)},
        )
        return

    logger.info(
        'Transactions have been filtered',
        extra={
            'unprocessed_transactions_count': len(filtered_transactions),
            'filtered_transactions_ids': [t.transaction_id for t in filtered_transactions],
            **log_extra,
        },
    )
    async with services.db.acquire() as conn:
        # Insert new entries in table
        await insert_bank_transactions_batch(conn, filtered_transactions)

    # Send records to Kafka in batch
    await services.kafka.send_records(
        topics.PROCESS_BANK_TRANSACTION,
        [{'transaction_id': transaction.transaction_id} for transaction in filtered_transactions],
    )


async def process_bill(app: web.Application, data: DataDict, logger: logging.Logger) -> None:
    """
    Job to process new bill:
     - convert bill XML to PDF
     - upload to "ТОВ Вчасно Сервіс" company account
     - sign & send to recipient (who requested a bill)
     - send email with an attached bill's PDF to recipient
    """
    from app.es.utils import send_to_indexator

    enable_bill_signing = get_flag(FeatureFlags.ENABLE_BILL_SIGNING)
    bill_id: str = data['bill_id']
    is_card_payment: bool = data.get('is_card_payment') or False

    if not bill_id:
        logger.error('Consumed record is missing bill_id')
        return

    async with services.db.acquire() as conn:
        bill = await select_bill_by_id(conn, bill_id)
        if not bill:
            logger.error('Consumed bill was not found', extra={'bill_id': bill_id})
            return

        # For card payment, we have quite a different flow, where we are waiting when
        # user will pay by credit card and then do the rest of the bill processing in
        # another place
        if is_card_payment:
            logger.info(
                'Bill signing and sending are skipped due to card payment',
                extra={'bill_id': bill.id_},
            )
            bill = bill.model_copy(update={'status_id': BillStatus.skipped.value})
            data = {'status_id': bill.status_id}
            await update_bill(conn=conn, bill_ids=[bill.id_], data=data)
            logger.info(
                'Bill status updated',
                extra={
                    'bill_id': bill.id_,
                    'status_id': bill.status_id,
                },
            )
            return

        if enable_bill_signing:
            system_account = await select_user_by_email(
                conn=conn, email=SystemAccountEmail.billing.value
            )

    bill_file = await bill_to_pdf(bill.id_)
    if not bill_file:
        logger.error('Bill pdf is missing', extra={'bill_id': bill.id_})
        return
    bill = bill.model_copy(update={'status_id': BillStatus.generated.value})

    document_id: str | None = None
    try:
        if enable_bill_signing:
            """Upload & Sign & Send bill to recipient"""

            edrpou = DEV_COMPANY_EDRPOU if services.config.app.debug else VCHASNO_COMPANY_EDRPOU
            signature_encoded_b64: str = await sign_bill_web_signer(bill=bill, bill_file=bill_file)

            document: File = await upload_bill_to_web(
                edrpou_upload=edrpou,
                user=system_account,  # type: ignore[arg-type]
                bill=bill,
                bill_file=bill_file,
            )
            document_id = document.id

            bill = bill.model_copy(update={'status_id': BillStatus.uploaded.value})

            async with services.db.acquire() as conn:
                await apply_bill_signature_on_web(
                    app=app,
                    conn=conn,
                    bill=bill,
                    user=system_account,  # type: ignore[arg-type]
                    sign_b64=signature_encoded_b64,
                    document_id=document_id,
                )
                bill = bill.model_copy(update={'status_id': BillStatus.signed.value})

                await send_bill_to_recipient(
                    edrpou_upload=edrpou,
                    conn=conn,
                    bill=bill,
                    user=system_account,  # type: ignore[arg-type]
                    data={'document_id': document_id},
                    request_source=Source.api_internal,
                )
                bill = bill.model_copy(update={'status_id': BillStatus.signed_and_sent.value})

            logger.info('Bill proceed successfully', extra={'bill_id': bill.id_})
    finally:
        async with services.db.acquire() as conn:
            await send_bill(conn, bill, bill_file)
            logger.info('Bill sent to client via email', extra={'bill_id': bill.id_})

            data = {'status_id': bill.status_id}
            if document_id is not None:
                data['document_id'] = document_id

                await send_to_indexator(app['redis'], [document_id], to_slow_queue=True)

            await update_bill(conn=conn, bill_ids=[bill.id_], data=data)
            logger.info('Bill status updated', extra={'bill_id': bill.id_})


async def delete_billing_accounts(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Delete expired and empty accounts.

    Next accounts will be deleted:
    Delete all accounts, excluded with monthly bonus.
    - expired accounts with ``client_bonus`` or ``client_bonus_custom`` types
    - empty accounts with zero ``units``
    """
    offset = int(data.get('offset', 0))
    logger.info('Starting billing accounts deletion', extra=data)

    async with app['db'].acquire() as conn:
        accounts = await select_invalid_accounts(conn, offset)
        if len(accounts) == 0:
            logger.info('Finishing billing accounts deletion', extra=data)
            return

        for account in accounts:
            try:
                account_data = {'account_id': account.id}
                await delete_account(conn, account_data)

            # Catch exception but does not break iterative process
            except Exception as exception:
                logger.exception(
                    'Unhandled exception on account deleting',
                    extra={'reason': str(exception), 'offset': offset},
                )
                offset += 1

    # Increase offset and continue processing
    # Note: `DELETE_ACCOUNTS_BUCKET_SIZE` must be presented in limit clause of
    # `select_invalid_accounts` query
    await app['kafka'].send_record(topics.DELETE_BILLING_ACCOUNTS, value={'offset': offset})
    logger.info('Next step for billing accounts deleting job', extra=data)


async def activate_company_rates(app: web.Application, _: DataDict, logger: logging.Logger) -> None:
    """Activate new company rates by start_date"""

    async with app['db'].acquire() as conn:
        new_company_rate = await select_rate_for_activation(conn)

        if new_company_rate is None:
            logger.info('Last company rate was activated')
            return

        active_rates = await select_active_company_rates(conn, new_company_rate.company_id)
        await api.activate_rate(conn, new_company_rate, active_rates)
        company_users = await select_company_users(conn, company_id=new_company_rate.company_id)

    await update_esputnik_company_rate(
        kafka=app['kafka'],
        users=company_users,
        rate=new_company_rate.rate,
        status=new_company_rate.status,
        is_on=True,
    )

    if new_company_rate.rate.is_pro or new_company_rate.rate.is_ultimate:
        await services.kafka.send_record(
            topics.ESPUTNIK_SEND_EVENT_TO_USERS,
            {
                'event': Event.rate_added,
                'company_id': new_company_rate.company_id,
                'extra_params': {
                    'rate': RATES_NAME_MAP.get(new_company_rate.rate, new_company_rate.rate.value),
                },
            },
        )

    await send_rate_to_crm(account_id=new_company_rate.id_)

    await app['kafka'].send_record(topics.ACTIVATE_COMPANY_RATES, value={})


async def activate_company_extensions(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Activate paid company extensions with planned_start_date"""
    offset = int(data.get('offset', 0))

    async with app['db'].acquire() as conn:
        extension = await select_employee_extensions_for_activation(conn, offset)
        if extension is None:
            logger.info('Last company rate employee extension was activated')
            return

        assert extension.bill_id  # To avoid mypy error
        bill = await select_bill_by_id(conn, extension.bill_id)
        if bill is None:
            logger.error('Extension bill not found', extra={'extension_id': extension.id})
            await app['kafka'].send_record(
                topics.ACTIVATE_COMPANY_EXTENSIONS, value={'offset': offset + 1}
            )
            return

        try:
            company_rate, old_max_employees_count = await validate_activate_employee_extension(
                conn, extension, bill
            )
            # Activation
            await activate_company_employees_extension(
                conn=conn,
                bill=bill,
                company_id=company_rate.company_id,
                old_max_employees_count=old_max_employees_count,
                rate_extension=extension,
            )
            logger.info('Extension successfully activated', extra={'extension_id': extension.id})

        except (InvalidRequest, DoesNotExist) as err:
            logger.error(
                'Planned extension activation failed',
                extra={
                    'err': err,
                    'extension_id': extension.id,
                },
            )
            offset += 1

    await app['kafka'].send_record(topics.ACTIVATE_COMPANY_EXTENSIONS, value={'offset': offset})


async def deactivate_company_extensions_trial(
    app: web.Application, _: DataDict, logger: logging.Logger
) -> None:
    """
    Deactivate add_employee extensions with expired trial period, which not paid on time
    """

    async with app['db'].acquire() as conn:
        row = await select_employee_extensions_trial_for_deactivation(conn)

        if row is None:
            logger.info('Employee extension trial was deactivated in latest company')
            return

        rate_extension = RateExtension.from_db(row)
        company_config = await get_billing_company_config(conn, company_id=row.company_id)

        # Deactivate extension trial and update config
        async with conn.begin():
            max_employees_count = company_config.max_employees_count - rate_extension.config.units  # type: ignore
            await update_billing_company_config(
                conn=conn,
                company_id=row.company_id,
                config={CompanyLimit.employees.value: max_employees_count},
            )
            await update_rate_extension(
                conn,
                rate_extension_ids=[rate_extension.id],
                update_data={'status': RateExtensionStatus.deactivated_trial},
            )
            await process_extra_roles(conn, company_id=row.company_id)
        logger.info(
            'Change company max_employees_count',
            extra={
                'initiator_type': 'rate_extension',
                'rate_extension_id': rate_extension.id,
                'new_extension_status': RateExtensionStatus.deactivated_trial,
                'company_id': row.company_id,
                'old_max_employees_count': company_config.max_employees_count,
                'new_max_employees_limit': max_employees_count,
            },
        )

    await app['kafka'].send_record(topics.DEACTIVATE_COMPANY_EXTENSIONS_TRIAL)


async def deactivate_company_rates(
    app: web.Application, _: DataDict, logger: logging.Logger
) -> None:
    """Deactivate active company rates after date expiration"""
    async with app['db'].acquire() as conn:
        company_rate = await select_rate_for_deactivation(conn)

        if company_rate is None:
            logger.info('Last company rate was deactivated')
            return

        logger.info(
            'Deactivate rate',
            extra={
                'rate_id': company_rate.id_,
                'company_id': company_rate.company_id,
                'date_expired': company_rate.end_date,
            },
        )
        await api.expire_rate(conn, rate=company_rate)
        company_users = await select_company_users(conn, company_id=company_rate.company_id)

    await update_esputnik_company_rate(
        kafka=app['kafka'],
        users=company_users,
        rate=company_rate.rate,
        status=company_rate.status,
        is_on=False,
    )

    if company_rate.rate not in AccountRate.free_rates():
        async with services.db_readonly.acquire() as conn:
            has_another_paid_rate = await is_company_has_another_paid_rate(
                conn=conn, company_id=company_rate.company_id
            )

            if not has_another_paid_rate:
                await services.kafka.send_record(
                    topics.ESPUTNIK_SEND_EVENT_TO_USERS,
                    {
                        'event': Event.rate_expired,
                        'company_id': company_rate.company_id,
                        'extra_params': {
                            'rate': RATES_NAME_MAP.get(company_rate.rate, company_rate.rate.value)
                        },
                    },
                )

    await app['kafka'].send_record(topics.DEACTIVATE_COMPANY_RATES, value={})


async def sa_activate_trial_company_rate(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    role_id: str = data['role_id']
    days: int = data['days']
    edrpou: str = data['edrpou']

    start_date = local_now()
    end_date = end_of_day(start_date + int(days) * DAY)
    end_date = to_local_datetime(end_date.replace(tzinfo=None))

    async with app['db'].acquire() as conn:
        user = await select_user_by_role_id(conn, role_id)
        company = await select_company_by_edrpou(conn, edrpou)
        if not company:
            logger.warning('Company not found')
            return

        rate = CompanyRate(
            id_=str(uuid.uuid4()),
            company_id=company.id,
            company_edrpou=company.edrpou,
            rate=AccountRate.pro_plus_trial_2022_12,
            status=CompanyRateStatus.active,
            amount=0,
            start_date=start_date,
            end_date=end_date,
        )
        await api.add_company_rate(conn=conn, rate=rate, user=user)  # type: ignore[arg-type]

        company_users = await select_company_users(conn, company_id=rate.company_id)
        await update_esputnik_company_rate(
            kafka=app['kafka'],
            users=company_users,
            rate=rate.rate,
            status=rate.status,
            is_on=True,
        )

        await send_rate_to_crm(account_id=rate.id_)


async def create_initial_free_company_rate(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    try:
        async with redis_lock(f'lock_initial_rate_activation_{data["company_id"]}'):
            company_id: str = data['company_id']
            extra = {'company_id': company_id}
            logger.info('Enabling initial free rate', extra=extra)

            async with services.db.acquire() as conn:
                company = await select_company(conn, company_id=company_id)
                if not company:
                    logger.info('Company is not found', extra={'company_id': company_id})
                    return

                active_rates = await select_active_company_rates(conn, company_id)

                if not should_create_free_rate(active_rates=active_rates):
                    logger.info('Company has active rate, aborting free rate creation', extra=extra)
                    return

                await create_free_rate(conn, company_id=company_id, company_edrpou=company.edrpou)

                company = await select_company_by_id(conn, company_id)  # type: ignore[assignment]
                if not company:
                    logger.info('Company is not found', extra={'company_id': company_id})
                    return

                # Automatically activate trial to a TOV company upon registration
                if get_flag(
                    FeatureFlags.ACTIVATE_TRIAL_AUTOMATICALLY_FOR_TOV_COMPANIES
                ) and not is_fop(company.edrpou):
                    await activate_tov_trials_on_registration(
                        conn=conn,
                        edrpou=company.edrpou,
                    )

            logger.info('Created initial free rate', extra=extra)
    except ConflictError:
        logger.info(
            'Preventing race condition: create_initial_free_company_rate already in progress',
            extra={'company_id': data['company_id']},
        )
        return


async def migrate_to_new_rate(_: web.Application, data: DataDict, logger: logging.Logger) -> None:
    """
    Apply restriction after moving to new rate (if any).

    Переход с тарифа на тариф:
    https://evocompany.atlassian.net/wiki/spaces/vchasno/pages/*********/2022
    """

    company_id: str = data['company_id']

    async with services.db.acquire() as conn:
        await process_extra_roles(conn, company_id=company_id)


@retry_config(max_attempts=5)
async def generate_esputnik_company_registration_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    if get_flag(FeatureFlags.DISABLE_ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT):
        logger.info('Skipping event generation')
        return

    edrpou = data['edrpou']

    async with services.db_readonly.acquire() as conn:
        company = await select_company_by_edrpou(conn, edrpou)
        if not company:
            logger.info('Company is not found for generating company registration event')
            return

    event = Event.company_registered_fop if is_fop(edrpou) else Event.company_registered_tov

    await services.kafka.send_record(
        topic=topics.ESPUTNIK_SEND_EVENT_TO_USERS,
        value={'event': event, 'company_id': company.id},
    )


@retry_config(max_attempts=5)
async def generate_esputnik_tov_trial_expiring_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Generates esputnik events on following filters

    EDO_Welcome_TOV_1_days_of_testing:
    - If TOV company has trial rate
    - and it ends in 1 day
    - and company doesn't have paid rates
    - has a two or more employees

    EDO_Welcome_TOV_3_days_of_testing:
    - If TOV company has trial rate
    - and it ends in 3 days
    - and company doesn't have paid rates
    - has a two or more employees

    EDO_Welcome_TOV_test_period_is_over:
    - If TOV company had trial rate
    - and it's over
    - and company doesn't have paid rates
    """

    if get_flag(FeatureFlags.DISABLE_ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT):
        logger.info('Skip esputnik trial rate expiring event generation for TOV companies')
        return

    logger.info(
        'Start esputnik trial rate expiring event generation for TOV companies',
        extra=data,
    )

    offset = data.get('offset', 0)
    limit = 100

    async with services.db_readonly.acquire() as conn:
        billing_accounts = await select_tov_trial_billing_accounts_expiring_soon_without_next_paid(
            conn=conn, limit=limit, offset=offset
        )
        companies_ids = {ba.company_id for ba in billing_accounts}
        count_roles_mapping = await count_company_roles_group_by_company(conn, companies_ids)

    expires_3_days = utc_now().date() + timedelta(days=3)
    expires_1_day = utc_now().date() + timedelta(days=1)
    expires_past_1_day = utc_now().date() - timedelta(days=1)

    for ba in billing_accounts:
        expiring_date: date = ba.date_expired.date()
        count_roles = count_roles_mapping[ba.company_id]

        events_map = {
            expires_3_days: {
                BillingAccountSource.employees_limit_reach_ab_testing: (
                    Event.tov_employees_limit_trial_expires_3_days
                ),
                'default': Event.tov_trial_expires_3_days,
            },
            expires_1_day: {
                BillingAccountSource.employees_limit_reach_ab_testing: (
                    Event.tov_employees_limit_trial_expires_1_days
                ),
                'default': Event.tov_trial_expires_1_days,
            },
            expires_past_1_day: {
                BillingAccountSource.employees_limit_reach_ab_testing: (
                    Event.tov_employees_limit_trial_rate_is_over
                ),
                'default': Event.tov_trial_rate_is_over,
            },
        }

        if count_roles >= 2 and expiring_date in events_map:
            event = events_map[expiring_date].get(ba.source, events_map[expiring_date]['default'])
        else:
            logger.info(
                'No event to send',
                extra={'expiring_date': expiring_date, 'account_id': ba.id},
            )
            return

        if event:
            await services.kafka.send_record(
                topics.ESPUTNIK_SEND_EVENT_TO_USERS,
                {
                    'event': event,
                    'company_id': ba.company_id,
                    'extra_params': {'rate': RATES_NAME_MAP[ba.rate]},
                },
            )

    # maybe there are more accounts
    if len(billing_accounts) == limit:
        await services.kafka.send_record(
            topics.ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT, {'offset': offset + limit}
        )

    logger.info(
        'End esputnik trial rate expiring event generation for TOV companies',
        extra=data,
    )


@retry_config(max_attempts=5)
async def generate_esputnik_rate_expiring_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    logger.info('Start esputnik rate expiring event generation', extra=data)

    offset = data.get('offset', 0)
    limit = 100

    async with services.db_readonly.acquire() as conn:
        billing_accounts = await select_billing_accounts_expiring_soon_without_next_paid(
            conn=conn, limit=limit, offset=offset
        )

    expires_30_days = utc_now().date() + timedelta(days=30)
    expires_14_days = utc_now().date() + timedelta(days=14)
    expires_5_days = utc_now().date() + timedelta(days=5)
    for ba in billing_accounts:
        # free rate renews automatically after expiring date
        # so don't need to send such notifications
        if ba.rate == AccountRate.free:
            continue

        event = None

        expiring_date: date = ba.date_expired.date()
        if expiring_date == expires_30_days:
            event = Event.rate_expires_30_days
        elif expiring_date == expires_14_days:
            event = Event.rate_expires_14_days
        elif expiring_date == expires_5_days:
            event = Event.rate_expires_5_days

        if event:
            await services.kafka.send_record(
                topics.ESPUTNIK_SEND_EVENT_TO_USERS,
                {
                    'event': event,
                    'company_id': ba.company_id,
                    'extra_params': {'rate': RATES_NAME_MAP[ba.rate]},
                },
            )

    # maybe there are more accounts
    if len(billing_accounts) == limit:
        await services.kafka.send_record(
            topics.ESPUTNIK_GENERATE_RATE_EXPIRING_EVENT, {'offset': offset + limit}
        )

    logger.info('Stop esputnik rate expiring event generation', extra=data)


@retry_config(max_attempts=5)
async def generate_employees_limit_reached_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    company_id = data['company_id']

    async with services.db_readonly.acquire() as conn:
        rates = await select_active_company_rates(conn, company_id)
        is_limit_reached = await is_employee_limit_reached(conn=conn, company_id=company_id)

    web_rates = [rate for rate in rates if rate.rate.is_web]

    if is_limit_reached and web_rates:
        await services.kafka.send_record(
            topics.ESPUTNIK_SEND_EVENT_TO_USERS,
            {
                'event': Event.employees_over,
                'company_id': company_id,
                'extra_params': {'rate': RATES_NAME_MAP[web_rates[0].rate]},
            },
        )


@retry_config(max_attempts=5)
async def send_esputnik_event_to_emails(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Sends esputnik event to users.

    Usually to ALL ADMINS of the company, EXCEPT cases:
    - event is tov_trial_expires_3_days or tov_trial_expires_1_days -
        send also to a specific positions in company
    - event is tov_trial_enabled or fop_trial_enabled - send to all users
    """
    company_id: str = data['company_id']
    event = esputnik.Event(data.get('event'))
    extra_params = data.get('extra_params', {})

    async with services.db_readonly.acquire() as conn:
        if event in (Event.tov_trial_expires_3_days, Event.tov_trial_expires_1_days):
            emails = await select_admins_and_positions_by_company_id_for_notifications(
                conn, company_id=company_id
            )
        elif event in (Event.tov_trial_enabled, Event.fop_trial_enabled):
            emails = await select_users_emails_for_esputnik(
                conn=conn,
                company_id=company_id,
            )
        else:
            emails = await select_users_emails_for_esputnik(
                conn=conn,
                company_id=company_id,
                role=UserRole.admin,
            )
        company = await select_company_by_id(conn=conn, company_id=company_id)

    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled', extra=data)
        return

    extra_params = {
        'company_name': company.name if company else '',
        'company_edrpou': company.edrpou if company else '',
        **extra_params,
    }

    for admin in emails:
        contact = await get_esputnik_contact_by_email(admin.email)
        if not contact:
            logger.info('Contact was not found', extra={'email': admin.email})
            return

        await client.generate_event(
            contact=contact,
            event=event,
            **extra_params,
        )
        logger.info(
            'Generated ESputnik event',
            extra={
                'email': admin.email,
                'event': event,
                'company_id': company_id,
                **extra_params,
            },
        )


async def on_document_charge_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Handle on document charge event.
    Currently, generate event for esputnik on low balance

    charge_type: ['web', 'api']
    """

    company_id: str = data['company_id']
    charge_type = ChargeType(data['charge_type'])
    billing_account_id: str = data['billing_account_id']

    async with services.db_readonly.acquire() as conn:
        account = await select_account_by_id(conn, account_id=billing_account_id)

        if not account:
            logger.info(
                'Billing account does not exists',
                extra={
                    'billing_account_id': billing_account_id,
                },
            )
            return

        # do not send when free expiring and another paid rate exists
        if account.rate == AccountRate.free and is_company_has_another_paid_rate(
            conn=conn, company_id=company_id
        ):
            return

        event, percentage = await get_entity_expiring_event(
            conn=conn,
            charge_type=charge_type,
            company_id=company_id,
        )

    if event:
        await send_event(
            logger=logger,
            company_id=company_id,
            billing_account_id=billing_account_id,
            event=event,
            account=account,
        )
    else:
        logger.info(
            'Event is not generated',
            extra={'percentage': percentage, 'billing_account_id': billing_account_id},
        )


async def send_trial_enabled_event(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    company_id = data['company_id']
    trial_type = data.get('trial_type')

    async with services.db_readonly.acquire() as conn:
        company = await select_company_by_id(conn, company_id)
        if not company:
            logger.info('Company is not found for generating trial enabled event')
            return

    if trial_type == BillingAccountSource.employees_limit_reach_ab_testing.value:
        event = Event.employees_limit_trial_enabled
    else:
        event = Event.fop_trial_enabled if is_fop(company.edrpou) else Event.tov_trial_enabled

    await services.kafka.send_record(
        topics.ESPUTNIK_SEND_EVENT_TO_USERS,
        {
            'event': event,
            'company_id': company_id,
        },
    )
