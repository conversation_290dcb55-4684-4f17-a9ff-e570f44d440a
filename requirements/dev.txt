# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml requirements/base.in requirements/dev.in -o requirements/dev.txt --emit-index-url
--index-url https://pypi.org/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/65/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/64/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/66/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/126/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/69/packages/pypi/simple

aiobotocore==2.21.1
    # via -r requirements/base.in
aiodns==3.4.0
    # via aiohttp
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.2
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   aiohttp-middlewares
    #   aiohttp-session
    #   conciergelib
    #   google-auth
    #   pytest-aiohttp
    #   vchasno-crm
aiohttp-middlewares==2.4.0
    # via -r requirements/base.in
aiohttp-session==2.12.1
    # via -r requirements/base.in
aioitertools==0.10.0
    # via aiobotocore
aiokafka==0.12.0
    # via -r requirements/base.in
aiopg==1.5.0a1
    # via -r requirements/base.in
aiosignal==1.2.0
    # via aiohttp
aiosmtplib==4.0.0
    # via -r requirements/base.in
alembic==1.12.0
    # via -r requirements/base.in
annotated-types==0.6.0
    # via pydantic
anyio==3.6.1
    # via
    #   httpx
    #   watchfiles
argon2-cffi==23.1.0
    # via -r requirements/base.in
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
asttokens==2.0.5
    # via stack-data
async-timeout==4.0.3
    # via
    #   aiohttp-middlewares
    #   aiokafka
    #   aiopg
attrs==21.4.0
    # via aiohttp
babel==2.14.0
    # via -r requirements/base.in
backcall==0.2.0
    # via ipython
bcrypt==3.2.0
    # via -r requirements/base.in
botocore==1.37.1
    # via aiobotocore
botocore-stubs==1.29.29
    # via types-aiobotocore-lite
brotli==1.1.0
    # via aiohttp
cachetools==5.2.0
    # via google-auth
cbor2==5.6.5
    # via conciergelib
certifi==2021.10.8
    # via
    #   elastic-transport
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via
    #   -r requirements/base.in
    #   argon2-cffi-bindings
    #   bcrypt
    #   cryptography
    #   pycares
charset-normalizer==2.1.1
    # via
    #   -r requirements/base.in
    #   requests
ciso8601==2.2.0
    # via -r requirements/base.in
conciergelib==0.5.0
    # via edo (pyproject.toml)
coverage==7.6.12
    # via
    #   -r requirements/dev.in
    #   pytest-cov
cryptography==41.0.4
    # via
    #   -r requirements/base.in
    #   types-pyopenssl
    #   types-redis
decorator==5.1.1
    # via ipython
deprecated==1.2.14
    # via pikepdf
dicttoxml2==2.1.0
    # via -r requirements/base.in
dnspython==2.6.1
    # via email-validator
elastic-transport==8.4.0
    # via elasticsearch
elasticmagic==0.1.0b2
    # via -r requirements/base.in
elasticsearch==8.12.1
    # via
    #   -r requirements/base.in
    #   elasticmagic
email-validator==2.1.1
    # via -r requirements/base.in
et-xmlfile==1.1.0
    # via openpyxl
evo-featureflags-client==0.3.3
    # via -r requirements/base.in
evo-featureflags-protobuf==0.2.1
    # via evo-featureflags-client
execnet==2.1.1
    # via pytest-xdist
executing==0.9.1
    # via stack-data
fakeredis==2.27.0
    # via -r requirements/dev.in
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
google-auth==2.15.0
    # via -r requirements/base.in
graphql-core-next==1.1.1
    # via persistql
grpclib==0.4.2
    # via -r requirements/base.in
h11==0.14.0
    # via httpcore
h2==4.1.0
    # via grpclib
hiku==0.6.0
    # via
    #   -r requirements/base.in
    #   evo-featureflags-client
hiredis==2.2.1
    # via redis
hpack==4.0.0
    # via h2
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via -r requirements/base.in
hyperframe==6.0.1
    # via h2
idna==3.3
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
inflection==0.5.1
    # via -r requirements/base.in
iniconfig==1.1.1
    # via pytest
ipython==8.4.0
    # via
    #   -r requirements/dev.in
    #   ipython-sql
ipython-genutils==0.2.0
    # via
    #   -r requirements/dev.in
    #   ipython-sql
ipython-sql==0.4.1
    # via -r requirements/dev.in
itsdangerous==1.1.0
    # via -r requirements/base.in
jedi==0.18.1
    # via ipython
jinja2==3.1.1
    # via -r requirements/base.in
jmespath==0.10.0
    # via
    #   aiobotocore
    #   botocore
jsonrpcclient==4.0.2
    # via -r requirements/base.in
logevo==4.2.1
    # via
    #   edo (pyproject.toml)
    #   vchasno-crm
lupa==2.2
    # via
    #   -r requirements/dev.in
    #   fakeredis
lxml==5.2.2
    # via
    #   -r requirements/base.in
    #   pikepdf
    #   python-docx
mako==1.2.0
    # via alembic
markupsafe==2.1.1
    # via
    #   jinja2
    #   mako
matplotlib-inline==0.1.3
    # via ipython
multidict==6.1.0
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   aiohttp
    #   grpclib
    #   yarl
mypy==1.15.0
    # via -r requirements/dev.in
mypy-extensions==1.0.0
    # via mypy
openpyxl==3.0.9
    # via -r requirements/base.in
orjson==3.10.11
    # via mypy
packaging==23.0
    # via
    #   aiokafka
    #   pikepdf
    #   pytest
    #   pytest-sugar
parso==0.8.3
    # via jedi
pebble==5.1.1
    # via -r requirements/base.in
persistql==0.3.1
    # via edo (pyproject.toml)
pexpect==4.8.0
    # via ipython
phonenumbers==8.13.47
    # via -r requirements/base.in
pickleshare==0.7.5
    # via ipython
pikepdf==9.4.0
    # via -r requirements/base.in
pillow==10.4.0
    # via
    #   -r requirements/base.in
    #   pikepdf
    #   reportlab
pluggy==1.5.0
    # via pytest
prettytable==0.7.2
    # via ipython-sql
prometheus-client==0.14.1
    # via -r requirements/base.in
prompt-toolkit==3.0.30
    # via ipython
propcache==0.2.1
    # via
    #   aiohttp
    #   yarl
protobuf==3.20.1
    # via -r requirements/base.in
psutil==5.9.5
    # via -r requirements/dev.in
psycopg2-binary==2.9.10
    # via
    #   -r requirements/base.in
    #   aiopg
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.2
    # via stack-data
py==1.11.0
    # via -r requirements/dev.in
py-eusign==1.3.1
    # via edo (pyproject.toml)
pyasn1==0.4.8
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.2.8
    # via google-auth
pycares==4.1.2
    # via aiodns
pycparser==2.21
    # via cffi
pycryptodome==3.20.0
    # via
    #   stream-unzip
    #   stream-zip
pydantic==2.9.2
    # via -r requirements/base.in
pydantic-core==2.23.4
    # via pydantic
pydevd==2.8.0
    # via -r requirements/dev.in
pygments==2.12.0
    # via ipython
pyjwt==2.8.0
    # via -r requirements/base.in
pymupdf==1.24.12
    # via -r requirements/base.in
pytest==8.3.4
    # via
    #   -r requirements/dev.in
    #   pytest-aiohttp
    #   pytest-asyncio
    #   pytest-cov
    #   pytest-mock
    #   pytest-sugar
    #   pytest-timeout
    #   pytest-xdist
pytest-aiohttp==1.1.0
    # via -r requirements/dev.in
pytest-asyncio==0.25.3
    # via
    #   -r requirements/dev.in
    #   pytest-aiohttp
pytest-cov==6.0.0
    # via -r requirements/dev.in
pytest-mock==3.14.0
    # via -r requirements/dev.in
pytest-sugar==1.0.0
    # via -r requirements/dev.in
pytest-timeout==2.3.1
    # via -r requirements/dev.in
pytest-xdist==3.6.1
    # via -r requirements/dev.in
python-dateutil==2.8.2
    # via
    #   aiobotocore
    #   botocore
    #   elasticmagic
python-docx==1.1.2
    # via -r requirements/base.in
python-mimeparse==2.0.0
    # via -r requirements/base.in
pytz==2022.6
    # via -r requirements/base.in
pyyaml==6.0.1
    # via -r requirements/base.in
qrcode==6.1
    # via -r requirements/base.in
redis==5.0.6
    # via
    #   -r requirements/base.in
    #   fakeredis
reportlab==3.6.13
    # via -r requirements/base.in
requests==2.28.1
    # via google-auth
rsa==4.9
    # via google-auth
ruff==0.9.9
    # via -r requirements/dev.in
schedule==1.2.2
    # via -r requirements/base.in
sentry-sdk==1.34.0
    # via -r requirements/base.in
setuptools==75.3.0
    # via
    #   -r requirements/base.in
    #   ipython
six==1.16.0
    # via
    #   asttokens
    #   bcrypt
    #   google-auth
    #   ipython-sql
    #   python-dateutil
    #   qrcode
sniffio==1.2.0
    # via
    #   anyio
    #   httpx
sortedcontainers==2.4.0
    # via fakeredis
sqlalchemy==1.3.24
    # via
    #   -r requirements/base.in
    #   alembic
    #   ipython-sql
    #   sqlalchemy-citext
sqlalchemy-citext==1.8.0
    # via -r requirements/base.in
sqlparse==0.4.2
    # via ipython-sql
stack-data==0.3.0
    # via ipython
stream-inflate==0.0.14
    # via stream-unzip
stream-unzip==0.0.91
    # via -r requirements/base.in
stream-zip==0.0.83
    # via -r requirements/base.in
termcolor==2.3.0
    # via pytest-sugar
toml==0.10.2
    # via vulture
trafaret @ https://github.com/oleksandr-kuzmenko/trafaret/archive/py310.zip
    # via
    #   -r requirements/base.in
    #   trafaret-validator
trafaret-validator==0.4.0
    # via -r requirements/base.in
traitlets==5.3.0
    # via
    #   ipython
    #   matplotlib-inline
translitua==1.3.1
    # via -r requirements/base.in
types-aiobotocore-bedrock-runtime==2.19.0
    # via types-aiobotocore-lite
types-aiobotocore-lite==2.19.0
    # via -r requirements/dev.in
types-aiobotocore-s3==2.19.0
    # via types-aiobotocore-lite
types-aiobotocore-sesv2==2.19.0
    # via types-aiobotocore-lite
types-awscrt==0.16.1
    # via botocore-stubs
types-babel==********
    # via -r requirements/dev.in
types-chardet==5.0.4
    # via -r requirements/dev.in
types-pyopenssl==********
    # via types-redis
types-python-dateutil==2.8.19
    # via -r requirements/dev.in
types-pytz==2022.6.0.1
    # via
    #   -r requirements/dev.in
    #   types-babel
types-pyyaml==6.0.11
    # via -r requirements/dev.in
types-redis==*******
    # via -r requirements/dev.in
types-ujson==5.4.0
    # via -r requirements/dev.in
typing-extensions==4.12.2
    # via
    #   aiokafka
    #   alembic
    #   mypy
    #   pydantic
    #   pydantic-core
    #   python-docx
ua-parser==0.10.0
    # via -r requirements/base.in
ujson==5.8.0
    # via -r requirements/base.in
urllib3==1.26.12
    # via
    #   botocore
    #   elastic-transport
    #   requests
    #   sentry-sdk
uvloop==0.21.0
    # via -r requirements/base.in
vchasno-crm==0.3.17
    # via edo (pyproject.toml)
vulture==2.5
    # via -r requirements/dev.in
watchfiles==0.16.1
    # via -r requirements/dev.in
wcwidth==0.2.5
    # via prompt-toolkit
wrapt==1.14.0
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   deprecated
xlwt==1.3.0
    # via -r requirements/base.in
xmltodict==0.12.0
    # via -r requirements/base.in
yarl==1.18.3
    # via
    #   -r requirements/base.in
    #   aiohttp
    #   aiohttp-middlewares
    #   vchasno-crm
