# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml requirements/base.in -o requirements/base.txt --emit-index-url
--index-url https://pypi.org/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/65/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/64/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/66/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/126/packages/pypi/simple
--extra-index-url https://gitlab.vchasno.com.ua/api/v4/projects/69/packages/pypi/simple

aiobotocore==2.21.1
    # via -r requirements/base.in
aiodns==3.4.0
    # via aiohttp
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.2
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   aiohttp-middlewares
    #   aiohttp-session
    #   conciergelib
    #   google-auth
    #   vchasno-crm
aiohttp-middlewares==2.4.0
    # via -r requirements/base.in
aiohttp-session==2.12.1
    # via -r requirements/base.in
aioitertools==0.10.0
    # via aiobotocore
aiokafka==0.12.0
    # via -r requirements/base.in
aiopg==1.5.0a1
    # via -r requirements/base.in
aiosignal==1.2.0
    # via aiohttp
aiosmtplib==4.0.0
    # via -r requirements/base.in
alembic==1.12.0
    # via -r requirements/base.in
annotated-types==0.6.0
    # via pydantic
anyio==3.6.1
    # via httpx
argon2-cffi==23.1.0
    # via -r requirements/base.in
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
async-timeout==4.0.3
    # via
    #   aiohttp-middlewares
    #   aiokafka
    #   aiopg
attrs==21.4.0
    # via aiohttp
babel==2.14.0
    # via -r requirements/base.in
bcrypt==3.2.0
    # via -r requirements/base.in
botocore==1.37.1
    # via aiobotocore
brotli==1.1.0
    # via aiohttp
cachetools==5.2.0
    # via google-auth
cbor2==5.6.5
    # via conciergelib
certifi==2021.10.8
    # via
    #   elastic-transport
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via
    #   -r requirements/base.in
    #   argon2-cffi-bindings
    #   bcrypt
    #   cryptography
    #   pycares
charset-normalizer==2.1.1
    # via
    #   -r requirements/base.in
    #   requests
ciso8601==2.2.0
    # via -r requirements/base.in
conciergelib==0.5.0
    # via edo (pyproject.toml)
cryptography==41.0.4
    # via -r requirements/base.in
deprecated==1.2.14
    # via pikepdf
dicttoxml2==2.1.0
    # via -r requirements/base.in
dnspython==2.6.1
    # via email-validator
elastic-transport==8.4.0
    # via elasticsearch
elasticmagic==0.1.0b2
    # via -r requirements/base.in
elasticsearch==8.12.1
    # via
    #   -r requirements/base.in
    #   elasticmagic
email-validator==2.1.1
    # via -r requirements/base.in
et-xmlfile==1.1.0
    # via openpyxl
evo-featureflags-client==0.3.3
    # via -r requirements/base.in
evo-featureflags-protobuf==0.2.1
    # via evo-featureflags-client
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
google-auth==2.15.0
    # via -r requirements/base.in
graphql-core-next==1.1.1
    # via persistql
grpclib==0.4.2
    # via -r requirements/base.in
h11==0.14.0
    # via httpcore
h2==4.1.0
    # via grpclib
hiku==0.6.0
    # via
    #   -r requirements/base.in
    #   evo-featureflags-client
hiredis==2.2.1
    # via redis
hpack==4.0.0
    # via h2
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via -r requirements/base.in
hyperframe==6.0.1
    # via h2
idna==3.3
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
inflection==0.5.1
    # via -r requirements/base.in
itsdangerous==1.1.0
    # via -r requirements/base.in
jinja2==3.1.1
    # via -r requirements/base.in
jmespath==0.10.0
    # via
    #   aiobotocore
    #   botocore
jsonrpcclient==4.0.2
    # via -r requirements/base.in
logevo==4.2.1
    # via
    #   edo (pyproject.toml)
    #   vchasno-crm
lxml==5.2.2
    # via
    #   -r requirements/base.in
    #   pikepdf
    #   python-docx
mako==1.2.0
    # via alembic
markupsafe==2.1.1
    # via
    #   jinja2
    #   mako
multidict==6.1.0
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   aiohttp
    #   grpclib
    #   yarl
openpyxl==3.0.9
    # via -r requirements/base.in
packaging==23.0
    # via
    #   aiokafka
    #   pikepdf
pebble==5.1.1
    # via -r requirements/base.in
persistql==0.3.1
    # via edo (pyproject.toml)
phonenumbers==8.13.47
    # via -r requirements/base.in
pikepdf==9.4.0
    # via -r requirements/base.in
pillow==10.4.0
    # via
    #   -r requirements/base.in
    #   pikepdf
    #   reportlab
prometheus-client==0.14.1
    # via -r requirements/base.in
propcache==0.2.1
    # via
    #   aiohttp
    #   yarl
protobuf==3.20.1
    # via -r requirements/base.in
psycopg2-binary==2.9.10
    # via
    #   -r requirements/base.in
    #   aiopg
py-eusign==1.3.1
    # via edo (pyproject.toml)
pyasn1==0.4.8
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.2.8
    # via google-auth
pycares==4.1.2
    # via aiodns
pycparser==2.21
    # via cffi
pycryptodome==3.20.0
    # via
    #   stream-unzip
    #   stream-zip
pydantic==2.9.2
    # via -r requirements/base.in
pydantic-core==2.23.4
    # via pydantic
pyjwt==2.8.0
    # via -r requirements/base.in
pymupdf==1.24.12
    # via -r requirements/base.in
python-dateutil==2.8.2
    # via
    #   aiobotocore
    #   botocore
    #   elasticmagic
python-docx==1.1.2
    # via -r requirements/base.in
python-mimeparse==2.0.0
    # via -r requirements/base.in
pytz==2022.6
    # via -r requirements/base.in
pyyaml==6.0.1
    # via -r requirements/base.in
qrcode==6.1
    # via -r requirements/base.in
redis==5.0.6
    # via -r requirements/base.in
reportlab==3.6.13
    # via -r requirements/base.in
requests==2.28.1
    # via google-auth
rsa==4.9
    # via google-auth
schedule==1.2.2
    # via -r requirements/base.in
sentry-sdk==1.34.0
    # via -r requirements/base.in
setuptools==75.3.0
    # via -r requirements/base.in
six==1.16.0
    # via
    #   bcrypt
    #   google-auth
    #   python-dateutil
    #   qrcode
sniffio==1.2.0
    # via
    #   anyio
    #   httpx
sqlalchemy==1.3.24
    # via
    #   -r requirements/base.in
    #   alembic
    #   sqlalchemy-citext
sqlalchemy-citext==1.8.0
    # via -r requirements/base.in
stream-inflate==0.0.14
    # via stream-unzip
stream-unzip==0.0.91
    # via -r requirements/base.in
stream-zip==0.0.83
    # via -r requirements/base.in
trafaret @ https://github.com/oleksandr-kuzmenko/trafaret/archive/py310.zip
    # via
    #   -r requirements/base.in
    #   trafaret-validator
trafaret-validator==0.4.0
    # via -r requirements/base.in
translitua==1.3.1
    # via -r requirements/base.in
typing-extensions==4.12.2
    # via
    #   aiokafka
    #   alembic
    #   pydantic
    #   pydantic-core
    #   python-docx
ua-parser==0.10.0
    # via -r requirements/base.in
ujson==5.8.0
    # via -r requirements/base.in
urllib3==1.26.12
    # via
    #   botocore
    #   elastic-transport
    #   requests
    #   sentry-sdk
uvloop==0.21.0
    # via -r requirements/base.in
vchasno-crm==0.3.17
    # via edo (pyproject.toml)
wrapt==1.14.0
    # via
    #   -r requirements/base.in
    #   aiobotocore
    #   deprecated
xlwt==1.3.0
    # via -r requirements/base.in
xmltodict==0.12.0
    # via -r requirements/base.in
yarl==1.18.3
    # via
    #   -r requirements/base.in
    #   aiohttp
    #   aiohttp-middlewares
    #   vchasno-crm
