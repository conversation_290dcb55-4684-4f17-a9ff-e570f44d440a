import logging
from datetime import datetime, timedelta
from http import HTTPStatus

import pydantic
from aiohttp import web

from api.errors import Code, DoesNotExist, Error, InvalidRequest, Object
from api.private.integrations.schemas import CreateVchasnoUserSchema
from app.auth.db import select_company_by_edrpou
from app.documents.validators import validate_document_exists
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.helpers import check_allowed, validate_spam
from app.lib.types import DataDict, StrDict
from app.registration.types import RegistrationCtx
from app.registration.validators import validate_registration
from app.services import services
from app.signatures.db import select_signatures
from app.signatures.types import Signature

logger = logging.getLogger(__name__)


class InviteUserSchema(pydantic.BaseModel):
    email: pv.Email


class CompanyEDRPOUSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU


class GetCompaniesSchema(pydantic.BaseModel):
    date_from: pv.LeftDatetime
    date_to: pv.RightDatetime


class GetActiveRolesSchema(pydantic.BaseModel):
    vchasno_id: pv.UUID | None = None

    # deprecated field, use vchasno_id instead
    email: pv.Email | None = None


class GetDocumentSignaturesSchema(pydantic.BaseModel):
    ids: list[pv.UUID] | None
    document_id: pv.UUID


class SendPhoneAuthCodeSchema(pydantic.BaseModel):
    phone: pv.Phone


class ProcessPhoneAuth(pydantic.BaseModel):
    phone: pv.Phone
    code: str = pydantic.Field(min_length=6, max_length=6)


async def validate_send_phone_auth(request: web.Request) -> SendPhoneAuthCodeSchema:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SendPhoneAuthCodeSchema, raw_data)

    await validate_spam(services.redis, key=f'phone_auth:send:{data.phone}')

    return data


async def validate_process_phone_auth(request: web.Request) -> ProcessPhoneAuth:
    raw_data = await validators.validate_json_request(request)

    data = validators.validate_pydantic(ProcessPhoneAuth, raw_data)

    if not await check_allowed(
        key=f'phone_auth:process:{data.phone}',
        limit=5,
        period=timedelta(minutes=15),
    ):
        raise Error(Code.too_many_request, status=HTTPStatus.TOO_MANY_REQUESTS)

    return data


def validate_get_companies(request: web.Request) -> tuple[datetime, datetime]:
    data = validators.validate_pydantic(GetCompaniesSchema, dict(request.rel_url.query))
    date_from, date_to = data.date_from, data.date_to
    if date_to < date_from:
        raise InvalidRequest(reason=_('Параметр "date_from" має бути менший ніж "date_to"'))

    return date_from, date_to


async def validate_get_company_billing_accounts(conn: DBConnection, raw_data: DataDict) -> str:
    data = validators.validate_pydantic(CompanyEDRPOUSchema, raw_data)
    company = await select_company_by_edrpou(conn, edrpou=data.edrpou)
    if company is None:
        raise DoesNotExist(Object.company, edrpou=data.edrpou)
    return company.id


async def validate_get_document_signatures(request: web.Request) -> list[Signature]:
    data = validators.validate_pydantic(
        GetDocumentSignaturesSchema,
        {
            'ids': request.rel_url.query.getall('ids', None),
            'document_id': request.match_info['document_id'],
        },
    )
    async with services.db.acquire() as conn:
        document = await validate_document_exists(conn, {'document_id': data.document_id})
        signs = await select_signatures(
            conn,
            document_ids=[document.id],
            signatures_ids=data.ids,
        )

    return signs


async def validate_registration_kasa(
    conn: DBConnection,
    raw_data: StrDict,
) -> RegistrationCtx:
    """
    Validation for registration from Kasa.

    Phone is required, so we need to check it before pass the raw_data to
     the regular validate_registration
    """
    if not raw_data.get('phone'):
        raise Error(Code.invalid_phone_number)

    return await validate_registration(conn, raw_data)


async def validate_create_vchasno_user_common(request: web.Request) -> CreateVchasnoUserSchema:
    return await validate_json_request(request, CreateVchasnoUserSchema)


async def validate_json_request[JsonSchemaType: pydantic.BaseModel](
    request: web.Request,
    schema: type[JsonSchemaType],
) -> JsonSchemaType:
    raw_data = await validators.validate_json_request(request)
    return validators.validate_pydantic(schema, raw_data)
