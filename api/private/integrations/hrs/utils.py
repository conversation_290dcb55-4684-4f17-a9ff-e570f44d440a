import logging
from datetime import datetime, timedelta

import sqlalchemy as sa
from aiohttp import web
from sqlalchemy.sql import ClauseElement

from api.downloads import utils as downloads_utils
from api.downloads import validators as downloads_validators
from api.downloads import viewer as downloads_viewer
from api.private.integrations.hrs import enums as hrs_enums
from api.private.integrations.hrs import schemas as hrs_schemas
from api.private.integrations.hrs import validators as hrs_validators
from app.auth import db as auth_db
from app.auth import enums as auth_enums
from app.auth import types as auth_types
from app.auth import utils as auth_utils
from app.auth.tables import user_table
from app.lib import datetime_utils as core_dt
from app.lib.database import DBConnection, DBRow
from app.mobile.tables import mobile_auth_refresh_tokens_table
from app.models import select_all, select_one
from app.registration import enums as registration_enums
from app.registration import schemas as registration_schemas
from app.registration import types as registration_types
from app.registration import utils as registration_utils
from app.services import services

logger = logging.getLogger(__name__)


def _get_has_app_db_filter(activity_from: datetime) -> ClauseElement:
    return sa.exists(
        sa.select([mobile_auth_refresh_tokens_table.c.id])
        .select_from(mobile_auth_refresh_tokens_table)
        .where(
            sa.and_(
                mobile_auth_refresh_tokens_table.c.user_id == user_table.c.id,
                mobile_auth_refresh_tokens_table.c.date_updated > activity_from,
            )
        )
    ).label('has_app')


async def get_user_info(
    *,
    conn: DBConnection,
    email: str | None = None,
    vchasno_id: str | None = None,
    mobile_activity_days: int = 30,
) -> hrs_schemas.GetUserOutput | None:
    """
    Retrieve user information from db.
    """
    assert any((email, vchasno_id)), 'Provide at least one filter'

    activity_from = core_dt.utc_now() - timedelta(days=mobile_activity_days)
    has_app_selectable = _get_has_app_db_filter(activity_from)

    query = sa.select(
        [
            user_table.c.id,
            user_table.c.email,
            user_table.c.email_confirmed,
            has_app_selectable,
        ]
    ).select_from(user_table)

    if email:
        query = query.where(user_table.c.email == email)

    if vchasno_id:
        query = query.where(user_table.c.id == vchasno_id)

    row = await select_one(conn, query)

    return (
        hrs_schemas.GetUserOutput(
            email=row.email,
            status=(
                hrs_enums.UserStatus.active
                if row.email_confirmed
                else hrs_enums.UserStatus.inactive
            ),
            app_status=(
                hrs_enums.UserMobileStatus.has_app
                if row.has_app
                else hrs_enums.UserMobileStatus.not_invited
            ),
            vchasno_id=row.id,
        )
        if row
        else None
    )


async def get_users_info(
    *,
    conn: DBConnection,
    emails: list[str] | None = None,
    vchasno_ids: list[str] | None = None,
    mobile_activity_days: int = 30,
) -> list[hrs_schemas.GetUserOutput]:
    """
    Retrieve and return user information from database.
    """
    assert any((emails, vchasno_ids)), 'Provide at least one filter'

    # Time since user should have at least one active session
    activity_from = core_dt.utc_now() - timedelta(days=mobile_activity_days)
    has_app_selectable = _get_has_app_db_filter(activity_from)

    query = (
        sa.select(
            [
                user_table.c.id,
                user_table.c.email,
                user_table.c.email_confirmed,
                has_app_selectable,
            ]
        )
        .select_from(user_table)
        .order_by(user_table.c.email)
    )

    if emails:
        query = query.where(user_table.c.email.in_(emails))

    if vchasno_ids:
        query = query.where(user_table.c.id.in_(vchasno_ids))

    rows = await select_all(conn, query)

    return [
        hrs_schemas.GetUserOutput(
            email=row.email,
            status=(
                hrs_enums.UserStatus.active
                if row.email_confirmed
                else hrs_enums.UserStatus.inactive
            ),
            app_status=(
                hrs_enums.UserMobileStatus.has_app
                if row.has_app
                else hrs_enums.UserMobileStatus.not_invited
            ),
            vchasno_id=row.id,
        )
        for row in rows
    ]


def build_role_permissions(
    permissions: hrs_schemas.RolePermissionsSchema | None,
) -> registration_schemas.InviteCoworkerPermissionSchema:
    """
    Build permissions before inviting or adding an HRS role.

    If permissions are specified in the add HRS role request,
    merge them with the default permissions, giving priority to the provided ones.
    """
    if not permissions:
        return registration_types.InviteCoworkerPermissionSchema.model_validate(
            auth_utils.HRS_ROLE_PERMISSIONS.__dict__
        )

    # Merge default with provided ones
    return registration_schemas.InviteCoworkerPermissionSchema(
        **{
            **auth_utils.HRS_ROLE_PERMISSIONS.__dict__,
            **permissions.model_dump(exclude_unset=True),
        }
    )


async def process_add_role(
    *,
    conn: DBConnection,
    options: hrs_validators.AddRoleCtx,
) -> auth_types.RoleDB:
    """
    Process adding HRS role.

    1. If user already has role in a company, just update has_hrs_role paramteter.
    2. If user already exists, just invite him as coworker.
    3. If user do not exist, autogenerate the user and invite as coworker.
    """

    permissions = build_role_permissions(options.permissions)

    # Case 1
    # Don't update permissions, since they have already been set before.
    # Count this role for billing, since it existed in EDO before HRS.
    if options.role:
        await auth_db.update_role(
            conn=conn,
            role_id=options.role.id,
            data={
                'position': options.position,
                'has_hrs_role': True,
            },
        )
        return options.role

    # Case 2
    # Update permissions for HRS requirements.
    # Don't count this role for billing limit.
    if options.user:
        return await registration_utils.process_invite_coworker(
            conn=conn,
            options=registration_types.InviteCoworkerOptions(
                email=options.user.email,
                edrpou=options.company.edrpou,
                company_id=options.company.id,
                is_legal=True,
                has_hrs_role=True,
                is_counted_in_billing_limit=False,
                existed_role=None,
                company_name=options.company.name,
                permissions=permissions,
                position=options.position,
                existed_user=options.user,
            ),
            invited_user=options.user,
        )

    # Case 3
    # Update permissions for HRS requirements.
    # Don't count this role for billing limit.
    invited_user = await auth_utils.insert_autogenerated_user(
        conn=conn,
        email=options.email,
        is_email_confirmed=False,
        is_registration_completed=False,
        source=registration_enums.RegistrationSource.hrs,
    )

    return await registration_utils.process_invite_coworker(
        conn=conn,
        options=registration_types.InviteCoworkerOptions(
            email=options.email,
            edrpou=options.company.edrpou,
            company_id=options.company.id,
            is_legal=True,
            has_hrs_role=True,
            is_counted_in_billing_limit=False,
            existed_role=None,
            company_name=options.company.name,
            permissions=permissions,
            position=options.position,
            existed_user=invited_user,
        ),
        invited_user=invited_user,
    )


async def process_remove_role(
    *,
    conn: DBConnection,
    options: hrs_validators.RemoveRoleCtx,
) -> None:
    """
    Process removing HRS role.

    1. Role has HRS role and regular EDO role, which means we need just to disable HRS role.
    2. Role has only HRS role, which means we can delete if from the company.
    """

    if options.role.has_hrs_role and options.role.is_counted_in_billing_limit:
        await auth_db.update_role(
            conn=conn,
            role_id=options.role.id,
            data={'has_hrs_role': False},
        )
        return

    await auth_utils.delete_role(
        conn=conn,
        role=options.role,
        status=auth_enums.RoleStatus.deleted,
        by_user=None,
    )


async def generate_signed_file(
    request: web.Request,
    user: DBRow,
    *,
    with_details: bool = False,
) -> web.StreamResponse:
    user_obj = auth_types.User.from_row(user)

    async with services.db.acquire() as conn:
        original_options = await downloads_validators.validate_download_original(request, conn)
        options = await downloads_viewer.prepare_viewer_file(
            request=request,
            conn=conn,
            company_id=user_obj.company_id,
            original_options=original_options,
        )

        file_name = original_options.file_name

        content = await downloads_utils.download_file_as_bytes(options.original_options)
        user_company_config = await auth_utils.get_company_config(
            conn=conn,
            company_edrpou=user_obj.company_edrpou,
        )

        if with_details:
            buffer = await downloads_viewer.generate_print_file(
                content=content,
                user_company_config=user_company_config,
                options=options,
            )
        else:
            buffer = await downloads_viewer.generate_signed_file(
                content=content,
                user_company_config=user_company_config,
                options=options,
            )

    return await downloads_utils.stream_file_buffer(request, file_name, buffer)
