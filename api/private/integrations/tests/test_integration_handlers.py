from datetime import datetime
from http import HTTPStatus
from unittest.mock import ANY

import pytest
import ujson

from app import vchasno_profile
from app.app import create_app
from app.auth.db import select_base_user
from app.auth.phone_auth.utils import PHONE_AUTH_REDIS_KEY_TEMPLATE
from app.auth.tests.utils import get_base_user
from app.billing.enums import AccountRate, AccountStatus
from app.registration.enums import RegistrationSource
from app.services import services
from app.tests.common import (
    PRIVATE_CHECK_CREDENTIALS_KASA_URL,
    PRIVATE_CHECK_CREDENTIALS_KEP_URL,
    PRIVATE_CHECK_CREDENTIALS_TTN_URL,
    PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
    PRIVATE_GET_COMPANIES_URL,
    PRIVATE_GET_COMPANY_BILLING_ACCOUNTS_URL,
    PRIVATE_GET_DOCUMENT_SIGNATURES_KEP_URL,
    PRIVATE_INVITE_USER_TTN_URL,
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    TEST_USER_PHONE,
    prepare_app_client,
    prepare_billing_account,
    prepare_client,
    prepare_company_data,
    prepare_document_data,
    prepare_signature_data,
    prepare_user_data,
)
from app.vchasno_profile import select_vchasno_user
from app.vchasno_profile.tests.utils import get_vchasno_user

USER_EMAIL_1 = '<EMAIL>'
USER_EMAIL_2 = '<EMAIL>'
USER_EMAIL_3 = '<EMAIL>'
USER_EMAIL_4 = '<EMAIL>'

SIGNATURE_ID_1 = '********-0000-0000-0000-************'
SIGNATURE_ID_2 = '********-0000-0000-0000-************'
SIGNATURE_ID_3 = '********-0000-0000-0000-************'
SIGNATURE_ID_4 = '********-0000-0000-0000-************'

DOCUMENT_ID_1 = '********-0000-0000-0000-************'
DOCUMENT_ID_2 = '********-0000-0000-0000-************'


@pytest.mark.parametrize('email', ['<EMAIL>'])
async def test_get_active_roles_unexisted_email(aiohttp_client, email):
    app, client, _ = await prepare_client(aiohttp_client)

    integration_config = services.config.integrations
    header = integration_config.header
    response = await client.post(
        PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
        headers={header: 'fake-token'},
        data=ujson.dumps({'email': email}),
    )
    assert response.status == 404


@pytest.mark.parametrize('vchasno_id', ['********-0000-0000-0000-************'])
async def test_get_active_roles_unexisted_vchasno_id(aiohttp_client, vchasno_id):
    app, client, _ = await prepare_client(aiohttp_client)

    integration_config = services.config.integrations
    header = integration_config.header
    response = await client.post(
        PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
        headers={header: 'fake-token'},
        data=ujson.dumps({'vchasno_id': vchasno_id}),
    )
    assert response.status == 404


async def test_get_active_roles_success_with_email(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    integration_config = services.config.integrations
    header = integration_config.header
    response = await client.post(
        PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
        headers={header: 'fake-token'},
        data=ujson.dumps({'email': user.email}),
    )
    assert response.status == 200
    response_data = await response.json()
    assert isinstance(response_data, list)
    assert len(response_data) == 1


async def test_get_active_roles_success_with_vchasno_id(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    integration_config = services.config.integrations
    header = integration_config.header
    response = await client.post(
        PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
        headers={header: 'fake-token'},
        data=ujson.dumps({'vchasno_id': str(user.id)}),
    )
    assert response.status == 200
    response_data = await response.json()
    assert isinstance(response_data, list)
    assert len(response_data) == 1


async def test_get_active_roles_no_params_provided(aiohttp_client):
    app, client, _ = await prepare_client(aiohttp_client)

    integration_config = services.config.integrations
    header = integration_config.header
    response = await client.post(
        PRIVATE_GET_ACTIVE_ROLES_KEP_URL,
        headers={header: 'fake-token'},
        json={},
    )
    assert response.status == 400


@pytest.mark.parametrize(
    'companies, params, expected',
    [
        # handler returns only companies in given date range
        (
            [
                {
                    'edrpou': '12121212',
                    'date_created': datetime(2021, 11, 11),
                },
                {
                    'edrpou': '23232323',
                    'date_created': datetime(2020, 3, 3),
                },
                {
                    'edrpou': '34343434',
                    'date_created': datetime(2016, 10, 10),
                },
                {
                    'edrpou': '45454545',
                    'date_created': datetime(2021, 4, 20),
                },
            ],
            {'date_from': '2017-01-01', 'date_to': '2021-11-01'},
            ['23232323', '45454545'],
        ),
        # handler returns companies including date_from/date_to dates
        (
            [
                {
                    'edrpou': '12121212',
                    'date_created': '2021-01-01',
                },
                {
                    'edrpou': '23232323',
                    'date_created': '2021-02-02',
                },
                {
                    'edrpou': '34343434',
                    'date_created': '2021-03-03',
                },
            ],
            {'date_from': '2021-01-01', 'date_to': '2021-02-02'},
            ['12121212', '23232323'],
        ),
    ],
)
async def test_get_companies(aiohttp_client, companies, params, expected):
    app = create_app()
    client = await aiohttp_client(app)
    for company in companies:
        await prepare_company_data(app, **company)

    integration_config = services.config.integrations
    header = integration_config.header
    res = await client.get(PRIVATE_GET_COMPANIES_URL, headers={header: 'fake-token'}, params=params)
    assert res.status == 200
    assert sorted((await res.json())['companies']) == sorted(expected)


@pytest.mark.parametrize(
    'params',
    [
        {
            # missing date_to
            'date_from': '2021-01-01',
        },
        {
            # missing date_from
            'date_to': '2021-01-01',
        },
        {
            # date_to < date_from
            'date_from': '2021-01-01',
            'date_to': '2020-01-01',
        },
        {
            # invalid date value
            'date_from': '21-12-12',
            'date_to': '21-12-21',
        },
    ],
)
async def test_get_companies_invalid_query(aiohttp_client, params):
    client = await aiohttp_client(create_app())

    integration_config = services.config.integrations
    header = integration_config.header
    res = await client.get(PRIVATE_GET_COMPANIES_URL, headers={header: 'fake-token'}, params=params)
    assert res.status == 400


async def test_get_company_billing_accounts(aiohttp_client):
    app = create_app()
    client = await aiohttp_client(app)
    company_id = await prepare_company_data(app)

    rate = AccountRate.start
    status = AccountStatus.active

    async with services.db.acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=company_id,
            rate=rate,
            status=AccountStatus.canceled,
        )
        await prepare_billing_account(
            conn,
            company_id=company_id,
            rate=rate,
            status=status,
        )

    integration_config = services.config.integrations
    header = integration_config.header
    res = await client.post(
        PRIVATE_GET_COMPANY_BILLING_ACCOUNTS_URL,
        headers={header: 'fake-token'},
        json={'edrpou': TEST_COMPANY_EDRPOU},
    )
    assert res.status == 200, await res.text()
    data = (await res.json())['accounts']

    # response only with active accounts
    assert len(data) == 1

    _account = data[0]
    assert _account['rate'] == rate.value
    assert _account['status'] == status.value


@pytest.mark.parametrize(
    'url',
    [
        PRIVATE_CHECK_CREDENTIALS_KEP_URL,
        PRIVATE_CHECK_CREDENTIALS_KASA_URL,
        PRIVATE_CHECK_CREDENTIALS_TTN_URL,
    ],
)
async def test_check_credentials(aiohttp_client, url):
    app = create_app()
    client = await aiohttp_client(app)

    user = await prepare_user_data(app)

    integration_config = services.config.integrations
    headers = {integration_config.header: 'fake-token'}

    # wrong password
    res = await client.post(
        url,
        headers=headers,
        json={'email': user.email, 'password': 'wrong'},
    )
    assert res.status == 400, await res.text()

    # ok
    res = await client.post(
        url,
        headers=headers,
        json={'email': user.email, 'password': TEST_USER_PASSWORD},
    )
    assert res.status == 200, await res.text()


@pytest.mark.parametrize(
    'request_data, response_code, response_field',
    [
        (
            {
                'email': TEST_USER_EMAIL,
                'password': TEST_USER_PASSWORD,
                'phone': TEST_USER_PHONE,
            },
            HTTPStatus.CREATED,
            'id',
        ),
        (
            {'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD},
            HTTPStatus.BAD_REQUEST,
            'code',
        ),
    ],
)
async def test_registration_kasa(aiohttp_client, request_data, response_code, response_field):
    app, client = await prepare_app_client(aiohttp_client)

    integration_config = services.config.integrations
    headers = {integration_config.header: 'fake-token'}

    response = await client.post(
        '/api/private/integrations/kasa/registration',
        headers=headers,
        json=request_data,
    )
    assert response.status == response_code
    assert response_field in await response.text()


@pytest.mark.parametrize(
    'document_id, signs_ids, params, expected_sign_ids, response_code',
    [
        pytest.param(
            DOCUMENT_ID_1,
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            {
                'document_id': DOCUMENT_ID_1,
                'ids': [SIGNATURE_ID_1, SIGNATURE_ID_3],
            },
            [SIGNATURE_ID_1],
            200,
            id='not_existed_sign',
        ),
        pytest.param(
            DOCUMENT_ID_1,
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            {
                'document_id': DOCUMENT_ID_1,
                'ids': [SIGNATURE_ID_1, SIGNATURE_ID_2, SIGNATURE_ID_3],
            },
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            200,
            id='not_existed_sign',
        ),
        pytest.param(
            DOCUMENT_ID_1,
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            {
                'document_id': DOCUMENT_ID_2,
                'ids': [SIGNATURE_ID_1, SIGNATURE_ID_2],
            },
            [],
            404,
            id='not_existed_doc',
        ),
        pytest.param(
            DOCUMENT_ID_1,
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            {
                'document_id': DOCUMENT_ID_1,
            },
            [SIGNATURE_ID_1, SIGNATURE_ID_2],
            200,
            id='all_signs',
        ),
        pytest.param(
            DOCUMENT_ID_1,
            [],
            {
                'document_id': DOCUMENT_ID_1,
                'ids': [SIGNATURE_ID_1, SIGNATURE_ID_2],
            },
            [],
            200,
            id='no_signs',
        ),
    ],
)
async def test_get_document_signatures(
    aiohttp_client,
    document_id,
    signs_ids,
    params,
    expected_sign_ids,
    response_code,
):
    app = create_app()
    client = await aiohttp_client(app)
    user = await prepare_user_data(app)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app, user, id=document_id, another_recipients=[recipient]
    )
    signatures_map = {}
    for sign_id in signs_ids:
        signature = await prepare_signature_data(app, user, document, id=sign_id)
        signatures_map[sign_id] = signature

    integration_config = services.config.integrations
    header = integration_config.header
    res = await client.get(
        PRIVATE_GET_DOCUMENT_SIGNATURES_KEP_URL.format(document_id=params.pop('document_id')),
        headers={header: 'fake-token'},
        params=params,
    )
    assert res.status == response_code
    if response_code == 200:
        response_json = await res.json()
        assert isinstance(response_json, list)

        assert len(response_json) == len(expected_sign_ids)
        response_signatures = sorted(response_json, key=lambda x: x['id'])

        expected_signatures = []
        for expected_id in expected_sign_ids:
            signature = signatures_map[expected_id]
            expected_signatures.append(
                {
                    'id': expected_id,
                    'edrpou': '11111111',
                    'company_name': 'Test Company',
                    'is_internal': False,
                    'role_id': user.role_id,
                    'signer_name': 'Test User',
                    'signer_position': None,
                    'serial_number': signature.key_serial_number,
                    'timestamp': signature.key_timemark.isoformat(),
                    'has_stamp': True,
                    'stamp': {
                        'acsk': 'stamp_acsk',
                        'company_name': 'Test Company',
                        'edrpou': '11111111',
                        'signature_power_type': None,
                        'certificate_power_type': None,
                        'serial_number': signature.stamp_serial_number,
                        'signer_name': 'Test User',
                        'signer_position': None,
                        'timestamp': signature.stamp_timemark.isoformat(),
                    },
                    'signature_content': 'VGVzdEV4dGVybmFsS2V5U2lnbmF0dXJlQ29udGVudA==',
                    'stamp_content': 'VGVzdEV4dGVybmFsU3RhbXBTaWduYXR1cmVDb250ZW50',
                }
            )

        expected_signatures = sorted(expected_signatures, key=lambda x: x['id'])
        assert response_signatures == expected_signatures


async def test_ttn_invite_user(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    data = {'email': '<EMAIL>'}
    integration_config = services.config.integrations
    header = integration_config.header
    headers = {header: 'fake-token'}
    resp = await client.post(PRIVATE_INVITE_USER_TTN_URL, json=data, headers=headers)
    assert resp.status == 201
    resp_data_1 = await resp.json()
    assert resp_data_1 == {
        'user_id': ANY,
        'url': ANY,
        'vchasno_id': ANY,
    }
    assert resp_data_1['user_id'] is not None
    assert resp_data_1['url'] is not None

    resp = await client.post(PRIVATE_INVITE_USER_TTN_URL, json=data, headers=headers)
    assert resp.status == 200, await resp.json()
    resp_data_2 = await resp.json()
    assert resp_data_2 == {
        'user_id': ANY,
        'vchasno_id': ANY,
    }
    assert resp_data_1['user_id'] == resp_data_2['user_id']
    assert resp_data_1['vchasno_id'] == resp_data_2['vchasno_id']

    async with services.db.acquire() as conn:
        invited_user = await select_base_user(conn, email=data['email'])
        vchasno_user = await select_vchasno_user(conn, email=data['email'])

    assert invited_user.id == vchasno_user.id
    assert resp_data_1['user_id'] == vchasno_user.id
    assert invited_user.is_autogenerated_password
    assert not invited_user.email_confirmed
    assert invited_user.source == RegistrationSource.ttn.value


async def test_create_vchasno_user_not_exists(aiohttp_client):
    """
    Test we can create user by email by one of our integration API
    """
    app, client = await prepare_app_client(aiohttp_client)

    response = await client.post(
        path='/api/private/integrations/kep/create-vchasno-user',
        headers={services.config.integrations.header: 'fake-token'},
        json={
            'email': USER_EMAIL_1,
            'first_name': 'user_first_name_1',
            'second_name': 'user_second_name_1',
            'last_name': 'user_last_name_1',
        },
    )

    assert response.status == HTTPStatus.OK

    user = await get_base_user(email=USER_EMAIL_1)
    assert user is None  # EDO user is not registered on this request

    vchasno_user = await get_vchasno_user(email=USER_EMAIL_1)
    assert vchasno_user is not None
    assert vchasno_user.email == USER_EMAIL_1

    response_data = await response.json()
    assert response_data == {
        'is_created': True,
        'user': {
            'id': vchasno_user.id,
            'email': vchasno_user.email,
            'phone': None,
            'first_name': 'user_first_name_1',
            'second_name': 'user_second_name_1',
            'last_name': 'user_last_name_1',
            'date_created': ANY,
            'date_updated': ANY,
            'date_deleted': None,
        },
    }


async def test_create_vchasno_user_exists(aiohttp_client):
    """
    Test we can create or just return existing user by email by one of our integration
    API
    """
    app, client = await prepare_app_client(aiohttp_client)

    # This will create user and vchasno user as well
    await prepare_user_data(
        app=app,
        email=USER_EMAIL_2,
        source=RegistrationSource.kasa.value,
        is_autogenerated_password=False,
        registration_completed=True,
        email_confirmed=True,
        first_name='user_first_name_1',
        second_name='user_second_name_1',
        last_name='user_last_name_1',
    )

    response = await client.post(
        path='/api/private/integrations/kep/create-vchasno-user',
        headers={services.config.integrations.header: 'fake-token'},
        json={
            'email': USER_EMAIL_2,
            'first_name': 'user_first_name_2',
            'second_name': 'user_second_name_2',
            'last_name': 'user_last_name_2',
        },
    )

    assert response.status == HTTPStatus.OK

    user = await get_base_user(email=USER_EMAIL_2)

    # Everything should stay the same
    assert user is not None
    assert user.source == RegistrationSource.kasa.value
    assert user.is_autogenerated_password is False
    assert user.registration_completed is True
    assert user.email_confirmed is True
    assert user.password is not None

    vchasno_user = await get_vchasno_user(email=USER_EMAIL_2)
    assert vchasno_user is not None
    assert vchasno_user.email == USER_EMAIL_2
    assert vchasno_user.id == user.id

    response_data = await response.json()
    assert response_data == {
        'is_created': False,
        'user': {
            'id': vchasno_user.id,
            'email': vchasno_user.email,
            'phone': None,
            # not updated, because user already exists
            'first_name': 'user_first_name_1',
            'second_name': 'user_second_name_1',
            'last_name': 'user_last_name_1',
            'date_created': ANY,
            'date_updated': ANY,
            'date_deleted': None,
        },
    }


async def test_send_phone_auth_code_ttn(aiohttp_client, evo_sender_mock):
    """
    Check what we return when phone is valid
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    integration_config = services.config.integrations
    headers = {integration_config.header: 'fake-token'}
    response = await client.post(
        path='/api/private/integrations/ttn/send-phone-auth-code',
        headers=headers,
        json={'phone': phone},
    )

    assert response.status == HTTPStatus.OK
    redis_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    code = await services.redis.get(redis_key)
    assert code is not None
    assert len(code) == 6 and code.isdigit()

    assert evo_sender_mock.message_count == 1
    assert evo_sender_mock.only_sms is True
    assert evo_sender_mock.message == f'{code} - код підтвердження http://localhost:8000'
    assert evo_sender_mock.phone == phone


async def test_process_phone_auth_ttn_new_user(aiohttp_client, evo_sender_mock):
    """
    Check what we return when user is created
    """
    app, client = await prepare_app_client(aiohttp_client)

    integration_config = services.config.integrations
    headers = {integration_config.header: 'fake-token'}
    phone = '+380991234567'

    response = await client.post(
        path='/api/private/integrations/ttn/send-phone-auth-code',
        headers=headers,
        json={'phone': phone},
    )
    assert response.status == HTTPStatus.OK
    code = await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone))
    assert code is not None
    assert len(code) == 6 and code.isdigit()
    assert evo_sender_mock.message == f'{code} - код підтвердження http://localhost:8000'
    assert evo_sender_mock.phone == phone
    assert evo_sender_mock.only_sms is True
    assert evo_sender_mock.message_count == 1
    evo_sender_mock.reset()

    response = await client.post(
        path='/api/private/integrations/ttn/process-phone-auth',
        headers=headers,
        json={'phone': phone, 'code': code},
    )
    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert data['is_created'] is True
    assert data['user']['phone'] == phone

    async with services.db.acquire() as conn:
        user = await select_vchasno_user(conn, phone=phone)
        assert user is not None
        assert user.email is None
        assert user.first_name is None
        assert user.last_name is None
        assert user.second_name is None

    assert await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)) is None
    assert evo_sender_mock.message_count == 0


async def test_process_phone_auth_ttn_existing_user(aiohttp_client, evo_sender_mock):
    """
    Check what we return when user already exists
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    async with services.db.acquire() as conn:
        existing_user = await vchasno_profile.create_vchasno_user(
            conn=conn,
            data={
                'phone': phone,
                'email': None,
                'first_name': 'Test',
                'last_name': 'User',
                'second_name': None,
            },
        )

    response = await client.post(
        path='/api/private/integrations/ttn/send-phone-auth-code',
        headers={services.config.integrations.header: 'fake-token'},
        json={'phone': phone},
    )
    assert response.status == HTTPStatus.OK

    code = await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone))
    assert code is not None
    assert evo_sender_mock.message_count == 1
    evo_sender_mock.reset()

    integration_config = services.config.integrations
    headers = {integration_config.header: 'fake-token'}
    response = await client.post(
        path='/api/private/integrations/ttn/process-phone-auth',
        headers=headers,
        json={'phone': phone, 'code': code},
    )

    assert response.status == HTTPStatus.OK
    data = await response.json()
    assert data['is_created'] is False
    assert data['user']['id'] == existing_user.id

    assert await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)) is None
    assert evo_sender_mock.message_count == 0
