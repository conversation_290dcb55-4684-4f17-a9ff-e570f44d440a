from hiku.graph import Field, <PERSON>, Op<PERSON>, Root
from hiku.types import <PERSON><PERSON><PERSON>, Integer, Optional, Sequence, String, TypeRef

from api.graph.constants import (
    DEFAULT_DOCUMENTS_DIRECTION,
    DEFAULT_DOCUMENTS_ORDER,
    DEFAULT_DOCUMENTS_SORT_DATE,
    DEFAULT_LIST_PAGE_LIMIT,
    DEFAULT_LIST_PAGE_OFFSET,
)
from api.graph.low_level import app_resolvers, sa_app_resolvers
from app.archive.resolvers import resolve_archive_items
from app.banner import resolvers as banner_resolvers
from app.contacts import resolvers as contact_resolvers
from app.directories.resolvers import resolve_directories, resolve_directory
from app.document_automation import resolvers as document_automation_resolvers
from app.document_categories import resolvers as document_categories_resolvers
from app.documents import resolvers as document_resolvers
from app.documents_fields import resolvers as documents_fields_resolvers
from app.drafts.resolvers import resolve_drafts
from app.groups.resolvers import resolve_group, resolve_groups
from app.lib.constants import (
    MAX_DOCUMENT_TEMPLATES_NUMBER,
    MAX_GROUPS_NUMBER,
)
from app.signatures import resolvers as signatures_resolvers
from app.tags import resolvers as tags_resolvers
from app.templates.resolvers import resolve_templates
from app.trigger_notifications import resolvers as trigger_notifications

DOCUMENTS_OPTIONS = [
    Option('ids', Optional[Sequence[String]], default=None),
    Option('search', Optional[String], default=None),
    Option('searchTitle', Optional[Sequence[String]], default=None),
    Option('searchNumber', Optional[Sequence[String]], default=None),
    Option('searchCompanyName', Optional[Sequence[String]], default=None),
    Option('searchCompanyEdrpou', Optional[Sequence[String]], default=None),
    Option('searchUserEmail', Optional[Sequence[String]], default=None),
    Option('searchParameter', Optional[Sequence[String]], default=None),
    Option('searchTag', Optional[Sequence[String]], default=None),
    Option('firstSignBy', Optional[String], default=None),
    Option('folderId', Optional[Integer], default=None),
    Option('folderIds', Optional[Sequence[Integer]], default=None),
    Option('reviewFolder', Optional[String], default=None),
    Option('statusId', Optional[Integer], default=None),
    Option('statusIds', Optional[Sequence[Integer]], default=None),
    Option('isOneSign', Optional[Boolean], default=None),
    Option('hasDateDelivered', Optional[Boolean], default=None),
    Option('sortDate', Optional[String], default=DEFAULT_DOCUMENTS_SORT_DATE),
    Option('gte', Optional[String], default=None),
    Option('lte', Optional[String], default=None),
    Option('amountEq', Optional[Integer], default=None),
    Option('amountGte', Optional[Integer], default=None),
    Option('amountLte', Optional[Integer], default=None),
    Option('condition', Optional[String], default=None),
    Option('conditions', Optional[Sequence[String]], default=None),
    Option('condition2', Optional[String], default=None),
    Option('conditions2', Optional[Sequence[String]], default=None),
    Option('tag', Optional[String], default=None),
    Option('tags', Optional[Sequence[String]], default=None),
    Option('withoutTags', Optional[Boolean], default=None),
    Option('categories', Optional[Sequence[Integer]], default=None),
    Option('hasComments', Optional[Boolean], default=None),
    Option('isWaitMySign', Optional[Boolean], default=None),
    Option('isMyInvalidSigned', Optional[Boolean], default=None),
    Option('isPartnerInvalidSigned', Optional[Boolean], default=None),
    Option('invalidSignedRoles', Optional[Sequence[String]], default=None),
    Option('isArchived', Optional[Boolean], default=False),
    Option('parentDirectoryId', Optional[Integer], default=None),
    Option('accessLevel', Optional[String], default=None),
]
LIST_DOCUMENT_OPTIONS = [
    Option('order', Optional[String], default=DEFAULT_DOCUMENTS_ORDER),
    Option('orderField', Optional[String], default=None),
    Option('direction', Optional[String], default=DEFAULT_DOCUMENTS_DIRECTION),
    Option('limit', Optional[Integer], default=DEFAULT_LIST_PAGE_LIMIT),
    Option('offset', Optional[Integer], default=DEFAULT_LIST_PAGE_OFFSET),
    Option('seqnumOffset', Optional[Integer], default=None),
]

OPTIONS_ID = [Option('id', String)]

ROOT = Root(
    [
        Link(
            name='allContacts',
            type_=TypeRef['ContactsList'],
            func=app_resolvers.resolve_contacts,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
                Option('isRegistered', Optional[Boolean], default=None),
            ],
            description='All contacts added to current company.',
        ),
        Link(
            name='allContactPersons',
            type_=TypeRef['ContactPersonsList'],
            func=app_resolvers.resolve_contact_persons,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('limit', Optional[Integer], default=25),
                Option('offset', Optional[Integer], default=0),
                Option('isRegisteredCompany', Optional[Boolean], default=None),
            ],
            description='All persons added to contacts of current company.',
        ),
        Link(
            name='allDocuments',
            type_=TypeRef['DocumentsList'],
            func=app_resolvers.resolve_documents,
            requires=None,
            options=[
                *DOCUMENTS_OPTIONS,
                *LIST_DOCUMENT_OPTIONS,
            ],
            description='All documents accessible by current user.',
        ),
        # Deprecated field
        Field(
            'countDocuments',
            Integer,
            app_resolvers.resolve_count_documents,
            options=DOCUMENTS_OPTIONS,
            description='Count documents accessible by current user.',
        ),
        Link(
            'allTags',
            Sequence[TypeRef['Tag']],
            tags_resolvers.resolve_tags,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=0),
                Option('hasRoles', Optional[Boolean], default=None),
            ],
            description='All tags for current user.',
        ),
        Link(
            'allTagsForDocumentFilter',
            Sequence[TypeRef['Tag']],
            app_resolvers.resolve_tags_for_document_filter,
            requires=None,
            description='All tags visible in document filter',
        ),
        Link(
            'company',
            Optional[TypeRef['Company']],
            app_resolvers.resolve_company,
            requires=None,
            options=OPTIONS_ID,
            description='Retrieve company info accessible by current user by its ID.',
        ),
        Link(
            'currentRole',
            Optional[TypeRef['Role']],
            app_resolvers.resolve_current_role,
            requires=None,
            description='Current logged in role. Role connected company & user.',
        ),
        Link(
            'currentCompanyRoles',
            Sequence[TypeRef['CoworkerRole']],
            app_resolvers.resolve_roles,
            requires=None,
            options=[
                Option('id', Optional[String], default=None),
                Option('search', Optional[String], default=None),
                Option('canSignAndRejectDocument', Optional[Boolean], default=None),
            ],
            description='All roles of current company.',
        ),
        Link(
            'currentSignSession',
            Optional[TypeRef['SignSession']],
            app_resolvers.resolve_current_sign_session,
            requires=None,
            description='Current sign session if any.',
        ),
        Link(
            'currentUser',
            Optional[TypeRef['User']],
            app_resolvers.resolve_current_user,
            requires=None,
            description='Current logged in user.',
        ),
        Link(
            'document',
            Optional[TypeRef['Document']],
            app_resolvers.resolve_document,
            requires=None,
            options=[
                Option('id', String),
                Option('withError', Optional[Boolean], default=False),
            ],
            description='Fetch document accessible by current user by its ID.',
        ),
        Link(
            name='documentAvailableRoles',
            type_=Sequence[TypeRef['DocumentAvailableRole']],
            func=document_resolvers.resolve_document_available_roles,
            requires=None,
            options=[
                Option('documentId', String),
            ],
            description='Get all roles from which document is available for given user',
        ),
        Field(
            'countUnregisteredContacts',
            Integer,
            app_resolvers.resolve_count_unregistered_contacts,
            description='Return number of unregistered companies in contacts.',
        ),
        Link(
            'triggerNotifications',
            Sequence[TypeRef['TriggerNotification']],
            trigger_notifications.resolve_trigger_notifications,
            requires=None,
            options=[
                Option('status', Optional[String], default=None),
                Option('limit', Optional[Integer], default=5),
                Option('offset', Optional[Integer], default=0),
            ],
            description='Show notification for current role',
        ),
        Link(
            'documentAutomationTemplates',
            Sequence[TypeRef['DocumentAutomationTemplate']],
            document_automation_resolvers.resolve_document_automation_templates,
            requires=None,
            options=[
                Option('is_active', Optional[Boolean], default=None),
                Option('limit', Optional[Integer], default=MAX_DOCUMENT_TEMPLATES_NUMBER),
                Option('offset', Optional[Integer], default=0),
            ],
            description='Select all document templates for given company',
        ),
        Link(
            'activeBanner',
            Optional[TypeRef['Banner']],
            banner_resolvers.resolve_active_banner,
            requires=None,
            description='Select active banner',
        ),
        Link(
            'documentsFields',
            Sequence[TypeRef['DocumentsField']],
            documents_fields_resolvers.resolve_fields,
            requires=None,
            options=[
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
            ],
            description='Select all documents fields for given company',
        ),
        Link(
            name='allDocumentCategories',
            type_=TypeRef['DocumentCategoriesList'],
            func=document_categories_resolvers.resolve_document_categories,
            requires=None,
            options=[
                Option('ids', Optional[Sequence[Integer]], default=None),
                Option('onlyPublic', Optional[Boolean], default=None),
                Option('onlyInternal', Optional[Boolean], default=None),
                Option('title', Optional[String], default=None),
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
            ],
            description='Select all documents categories available for company',
        ),
        Link(
            'allDocumentRequiredFields',
            Sequence[TypeRef['DocumentRequiredField']],
            app_resolvers.resolve_document_required_fields,
            requires=None,
            options=[
                Option('companies_ids', Optional[Sequence[String]], default=None),
                Option('edrpous', Optional[Sequence[String]], default=None),
            ],
            description='Get all required fields for company. '
            'For current company if no companies_ids nor edrpous are passed',
        ),
        Link(
            'allCloudSigners',
            Sequence[TypeRef['CloudSigner']],
            signatures_resolvers.resolve_cloud_signers,
            requires=None,
            description='Get list of operation ids and its document ids for current user',
        ),
        # Nodes for super admins
        Link(
            'saAllCompanies',
            Sequence[TypeRef['Company']],
            sa_app_resolvers.resolve_companies,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
            ],
            description='All available companies. Only for super admin.',
        ),
        Link(
            'saAllUsers',
            Sequence[TypeRef['User']],
            sa_app_resolvers.resolve_users,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('gte', Optional[String], default=None),
                Option('lte', Optional[String], default=None),
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
            ],
            description='All available users. Only for super admin.',
        ),
        Link(
            'saCompany',
            Optional[TypeRef['Company']],
            sa_app_resolvers.resolve_company,
            requires=None,
            options=OPTIONS_ID,
            description='Retrieve company info by its ID. Only for super admin.',
        ),
        Link(
            'saUser',
            Optional[TypeRef['User']],
            sa_app_resolvers.resolve_user,
            requires=None,
            options=OPTIONS_ID,
            description='Retrieve user info by its ID. Only for super admin.',
        ),
        Link(
            'saBanner',
            Sequence[TypeRef['Banner']],
            banner_resolvers.resolve_banners,
            requires=None,
            options=[
                Option('limit', Optional[Integer], default=None),
                Option('offset', Optional[Integer], default=None),
            ],
        ),
        Link(
            name='allGroups',
            type_=TypeRef['GroupsList'],
            func=resolve_groups,
            requires=None,
            options=[
                Option('limit', Integer, default=MAX_GROUPS_NUMBER),
                Option('offset', Integer, default=0),
                Option('name', Optional[String], default=None),
            ],
        ),
        Link(
            'group',
            Optional[TypeRef['Group']],
            resolve_group,
            requires=None,
            options=OPTIONS_ID,
        ),
        Link(
            name='allContactRecipients',
            type_=Sequence[TypeRef['ContactRecipient']],
            func=contact_resolvers.resolve_contact_recipients,
            requires=None,
            options=[
                Option('search', Optional[String], default=None),
                Option('limit', Optional[Integer], default=100),
                Option('offset', Optional[Integer], default=0),
            ],
        ),
        Link(
            name='allDrafts',
            type_=TypeRef['DraftsList'],
            func=resolve_drafts,
            requires=None,
            options=[
                Option('types', Optional[Sequence[String]], default=None),
                Option('limit', Optional[Integer], default=100),
                Option('offset', Optional[Integer], default=0),
            ],
        ),
        Link(
            name='allTemplates',
            type_=TypeRef['TemplatesList'],
            func=resolve_templates,
            requires=None,
            options=[
                Option('limit', Optional[Integer], default=20),
                Option('offset', Optional[Integer], default=0),
                Option('categories', Optional[Sequence[Integer]], default=None),
                Option('search', Optional[String], default=None),
                Option('is_public', Optional[Boolean], default=None),
                Option('is_favorite', Optional[Boolean], default=None),
            ],
        ),
        Link(
            'directory',
            Optional[TypeRef['DocumentDirectory']],
            resolve_directory,
            requires=None,
            options=[Option(name='id', type_=Integer)],
        ),
        Link(
            name='allDirectories',
            type_=TypeRef['DirectoriesList'],
            func=resolve_directories,
            requires=None,
            options=[
                Option('limit', Optional[Integer], default=20),
                Option('offset', Optional[Integer], default=0),
                Option('parentId', Optional[Integer], default=None),
                Option('search', Optional[String], default=None),
            ],
        ),
        Link(
            name='allArchiveItems',
            type_=TypeRef['ArchiveList'],
            func=resolve_archive_items,
            requires=None,
            options=[
                *DOCUMENTS_OPTIONS,
                *LIST_DOCUMENT_OPTIONS,
            ],
            description='All archive documents and directories accessible by current user.',
        ),
    ]
)
