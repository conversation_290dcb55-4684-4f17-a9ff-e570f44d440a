from enum import Enum, unique


@unique
class Action(Enum):
    blackbox_upload_document = 'Завантаження документу через BlackBox'

    create_role = 'Створення ролі'
    create_edi_role = 'Створення ролі в EDI'
    create_role_already_exists = 'Вибірка ролі при створення ролі'

    create_token = 'Створення токену'
    create_token_already_exists = 'Вибірка токену при створенні токену'
    create_user = 'Створення користувача'
    create_user_already_exists = 'Вибірка користувача при створенні користувача'

    delete_document = 'Видалення документу'
    delete_role = 'Видалення ролі'
    delete_edi_role = 'Видалення ролі в EDI'

    list_documents = 'Лістинг документів'
    list_incoming_documents = 'Лістинг вхідних документів'

    retrieve_document = 'Вибірка документу'
    retrieve_recipient = 'Вибірка отримувача'
    retrieve_flows = 'Вибірка процесу підписання'
    retrieve_signatures = 'Вибірка підписів документа'

    # Sign session
    create_sign_session = 'Створення сесії підписання'
    create_sign_session_already_exists = 'Вибірка сесії підписання при створенні сесії підписання'
    retrieve_sign_session = 'Вибірка сесії підписання'
    resend_sign_session = 'Пересилання сесії підписання'

    update_role = 'Оновлення ролі'

    update_config = 'Оновлення налаштувань'

    # Rates
    add_company_rate = 'Додавання нового тарифу'
    update_company_rate = 'Редагування тарифу'
    delete_company_rate = 'Відхилення тарифу'

    update_rate_permissions = 'Оновлення можливостей тарифу'


@unique
class Vendor(Enum):
    api = 'API'
    onec = '1C'
    super_admin = 'super_admin'

    # Additional values for doc quantity tracking
    mastakov = 'Mastakov'


@unique
class APIVersion(Enum):
    v1 = 'v1'
    v2 = 'v2'
