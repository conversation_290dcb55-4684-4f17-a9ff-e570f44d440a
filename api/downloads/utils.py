from __future__ import annotations

import io
import logging
import os
import string
from collections.abc import AsyncIterator
from contextlib import suppress
from lzma import LZ<PERSON><PERSON>rror
from typing import (
    TYPE_CHECKING,
)

from aiobotocore.response import StreamingBody
from aiohttp import web
from multidict import MultiDict

from api.downloads import pdf
from api.downloads.constants import BUFFER_CHUNK_SIZE
from api.downloads.types import (
    Document,
    FileOptions,
    to_document,
)
from api.errors import (
    Code,
    ConflictError,
    DoesNotExist,
    Error,
    Object,
)
from api.uploads.constants import ARCHIVE_DECOMPRESSORS_MAPPING
from app.auth.constants import AUTH_METHOD_APP_KEY
from app.auth.enums import AuthMethod
from app.auth.types import AuthUser
from app.auth.utils import (
    get_company_config,
    get_sign_session_id_from_request,
)
from app.document_versions import utils as document_versions
from app.document_versions.types import DocumentVersion
from app.documents import types as documents_types
from app.documents.db import select_document_by_id, update_document
from app.documents.utils import (
    convert_xml_to_pdf,
    get_asic_s3_key,
    get_document_s3_key,
    get_external_key_signature_s3_key,
    get_external_stamp_signature_s3_key,
    get_internal_signature_s3_key,
    get_xml_to_pdf_key,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.i18n.utils import get_current_locale_code
from app.lib import (
    s3_utils,
    tracking,
)
from app.lib.chunks import iter_bytes_by_chunks, read_from_bytes_chunks
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import (
    soft_parse_raw_datetime,
    soft_to_local_datetime,
)
from app.lib.helpers import (
    ensure_mimetype,
    string_to_bool,
    translit,
)
from app.lib.s3_utils import Decrypter
from app.lib.xlsx import XLSXWriter
from app.reviews.types import DownloadReviewHistoryCtx
from app.reviews.utils import select_review_history
from app.services import services
from app.sign_sessions import utils as sign_sessions_utils
from app.signatures.db import select_last_internal_signature
from app.signatures.types import Signature

if TYPE_CHECKING:
    from types_aiobotocore_s3.type_defs import GetObjectOutputTypeDef

logger = logging.getLogger(__name__)

PRINTABLE_SET = set(f'{string.ascii_letters}{string.digits} ._-/@')


def filter_unprintable(text: str) -> str:
    return ''.join([s for s in text if s in PRINTABLE_SET])


async def download_file(options: FileOptions) -> io.BytesIO:
    """High order function to download document file from S3 as buffer.

    The function downloads file from S3 (archived or unarchived) and unarchive
    it if necessary. Function also returns buffer (``io.BytesIO``) instead of
    plain bytes object.
    """
    return io.BytesIO(await download_file_as_bytes(options))


def expirable(request: web.Request) -> bool:
    return bool(string_to_bool(request.rel_url.query.get('expirable', '')))


async def download_file_as_bytes(options: FileOptions) -> bytes:
    """High order function to download document file from S3 as bytes."""
    content, _ = await s3_utils.download(options.download_file)
    return await unarchive_content(options.raw_file_name, content)


async def get_pdf_content_from_xml(
    user: AuthUser,
    document_id: str,
    s3_xml_to_pdf_key: str | None,
    *,
    force: bool = False,
    render_signatures_table: bool = False,
    render_signatures_visualisation: bool = True,
) -> bytes:
    if not s3_xml_to_pdf_key or force:
        upload_file = await convert_xml_to_pdf(
            user=user,
            document_id=document_id,
            s3_xml_to_pdf_key=s3_xml_to_pdf_key,
            render_signatures_table=render_signatures_table,
            render_signatures_visualisation=render_signatures_visualisation,
        )
        s3_key = upload_file.key
        content = upload_file.body
        return await unarchive_content(s3_key, content)

    s3_key = get_xml_to_pdf_key(document_id, s3_xml_to_pdf_key)
    try:
        content, _ = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
        return await unarchive_content(s3_key, content)
    except LZMAError:
        # TODO: cleaup, that kind of processing error not actual for new S3
        # In that case we got encrypted content, it may happen for some old
        # .pdf xml-previews which was trying to decrypt by encryption_key from wrong edrpou.
        # For such cases we simply remove s3_xml_to_pdf_key from document_table
        # and ask user to retry request. For all new requests created pdf-preview
        # encrypted & decrypted by document_owner encryption_key.
        async with services.db.acquire() as conn:
            with suppress(AttributeError):
                await update_document(
                    conn,
                    {
                        'document_id': document_id,
                        's3_xml_to_pdf_key': None,
                    },
                )
            await s3_utils.delete(s3_key)
        logger.error(
            'Unexpected attempt to unarchive content of encrypted S3 file.',
            extra={'s3_key': s3_key},
        )
        raise ConflictError() from None


async def stream_buffer(
    buffer: io.BytesIO,
    response: web.StreamResponse,
    chunk_size: int = BUFFER_CHUNK_SIZE,
) -> None:
    """Stream given buffer into response.

    By default, stream by ``BUFFER_CHUNK_SIZE``, but you can use larger or
    smaller chunks.
    """
    # Rewind buffer
    if buffer.tell():
        buffer.seek(0)

    # Stream buffer before next read not resulted in empty bytes
    try:
        while True:
            chunk = buffer.read(chunk_size)
            if not chunk:
                break
            await response.write(chunk)
    finally:
        buffer.close()


@tracking.stream_file_buffer.time()
async def stream_file_buffer(
    request: web.Request,
    file_name: str,
    raw_buffer: io.BytesIO,
    *,
    status: int = 200,
    encoding: str | None = None,
    content_type: str | None = None,
) -> web.StreamResponse:
    buffer = raw_buffer
    file_name = filter_unprintable(translit(file_name))
    headers = {'Content-Disposition': f'Attachment; filename="{file_name}"'}

    if encoding:
        headers['X-Evo-Content-Encoding'] = encoding

    response = web.StreamResponse(headers=MultiDict(headers), status=status)
    response.content_type = content_type if content_type else ensure_mimetype(file_name)
    response.content_length = buffer.seek(-1, 2) + 1

    await response.prepare(request)
    await stream_buffer(buffer, response)
    await response.write_eof()

    return response


async def stream_file_original(
    request: web.Request,
    file_name: str,
    file_item: s3_utils.DownloadFile,
    status: int = 200,
    content_type: str | None = None,
) -> web.StreamResponse:
    """
    Stream file from S3 to response by chunks (decrypt every chunk if needed)
    """

    file_name = filter_unprintable(translit(file_name))
    headers = {'Content-Disposition': f'Attachment; filename="{file_name}"'}

    decrypter = Decrypter(file_item=file_item, s3_config=services.config.s3)
    await decrypter.init()

    if encoding := await decrypter.get_content_encoding():
        headers['X-Evo-Content-Encoding'] = encoding

    response = web.StreamResponse(headers=MultiDict(headers), status=status)
    response.content_type = content_type if content_type else ensure_mimetype(file_name)
    response.content_length = decrypter.content_length

    await response.prepare(request)
    await decrypter.stream_to_response(response)
    await response.write_eof()

    return response


@tracking.stream_s3_object.time()
async def stream_s3_object(
    request: web.Request,
    file_name: str,
    s3_object: GetObjectOutputTypeDef,
    status: int = 200,
    content_type: str | None = None,
) -> web.StreamResponse:
    """Stream response with s3 object without uploading it to RAM"""

    headers = {'Content-Disposition': f'Attachment; filename="{file_name}"'}

    response = web.StreamResponse(headers=MultiDict(headers), status=status)
    response.content_type = content_type if content_type else ensure_mimetype(file_name)
    response.content_length = s3_object['ContentLength']

    await response.prepare(request)
    await stream_object(stream=s3_object['Body'], response=response)
    await response.write_eof()

    return response


async def stream_object(
    stream: StreamingBody,
    response: web.StreamResponse,
    chunk_size: int = BUFFER_CHUNK_SIZE,
) -> None:
    """Stream aiobotocore StreamingBody object to aiohttp StreamResponse"""

    try:
        chunk = await stream.read(chunk_size)
        while len(chunk) > 0:
            await response.write(chunk)
            chunk = await stream.read(chunk_size)
    finally:
        stream.close()


@tracking.unarchive_content.time()
async def unarchive_content(
    file_name: str | None,
    content: bytes,
) -> bytes:
    """
    Used for
    - unarchive signature_containers uploaded via Blackbox API
    - unarchive pdf content received from Url2Pdf with 'xz=1' url parameter

    If you want unarchive data stream - consider using api.downloads.utils.unarchive_content_gen
    """

    return await read_from_bytes_chunks(
        unarchive_content_gen(
            file_name=file_name, content=iter_bytes_by_chunks(content, chunk_size=BUFFER_CHUNK_SIZE)
        )
    )


async def unarchive_content_gen(
    file_name: str | None,
    content: AsyncIterator[bytes],
) -> AsyncIterator[bytes]:
    """
    Unarchive async bytes generator, for better processing large files

    Used for
    - unarchive signature_containers uploaded via Blackbox API
    - unarchive pdf content received from Url2Pdf with 'xz=1' url parameter
    """

    decompress_cls = None
    decompressor = None

    if file_name:
        _, ext = os.path.splitext(file_name)
        decompress_cls = ARCHIVE_DECOMPRESSORS_MAPPING.get(ext)

    if decompress_cls is not None:
        decompressor = decompress_cls()

    async for chunk in content:
        yield decompressor.decompress(chunk) if decompressor else chunk


async def use_xml_to_json(conn: DBConnection, document: Document) -> bool:
    """Use XML to JSON as viewer content URL or not?

    XML to JSON used when,

    1. Company config has enabled ``use_xml_to_json`` flag or current level is
       one of list items
    2. Document is XML
    3. Document uploaded after ``xml_to_json_min_document_date`` if specified
    """
    if not document.is_xml:
        return False

    config = await get_company_config(conn, company_edrpou=document.created_by_edrpou)
    downloads_config = config.downloads
    use_xml_to_json_option = downloads_config.use_xml_to_json

    if not use_xml_to_json_option:
        return False

    min_document_datetime = soft_parse_raw_datetime(downloads_config.xml_to_json_min_document_date)
    min_document_date = min_document_datetime.date() if min_document_datetime else None
    document_datetime = soft_to_local_datetime(document.date_created)
    document_date = document_datetime.date() if document_datetime else None
    if min_document_date and document_date and min_document_date > document_date:
        return False

    ignore_types = downloads_config.xml_to_json_ignore_document_types
    if ignore_types and document.type_ in ignore_types:
        return False

    return True


async def download_internal_signature(
    *,
    document_id: str,
    signature_id: str,
) -> bytes:
    """
    Download internal signature file from S3
    """
    s3_key = get_internal_signature_s3_key(document_id, signature_id=signature_id)

    # Download signature file
    file = s3_utils.DownloadFile(key=s3_key)
    content, _ = await s3_utils.download(file)
    return content


def get_external_signature_key_download_file(
    document_id: str,
    signature_id: str,
) -> s3_utils.DownloadFile:
    """
    Get external signature key download file
    """
    s3_key = get_external_key_signature_s3_key(document_id, signature_id=signature_id)

    return s3_utils.DownloadFile(key=s3_key)


def get_external_signature_stamp_download_file(
    document_id: str,
    signature_id: str,
) -> s3_utils.DownloadFile:
    """
    Get external signature stamp download file
    """
    s3_key = get_external_stamp_signature_s3_key(document_id, signature_id=signature_id)

    return s3_utils.DownloadFile(key=s3_key)


async def download_external_key_signature(signature: Signature) -> bytes:
    """
    Download external signature key file from S3
    """
    file = get_external_signature_key_download_file(
        document_id=signature.document_id,
        signature_id=signature.id,
    )
    content, _ = await s3_utils.download(file)
    return content


async def download_external_stamp_signature(signature: Signature) -> bytes:
    """
    Download external signature stamp file from S3
    """
    file = get_external_signature_stamp_download_file(
        document_id=signature.document_id,
        signature_id=signature.id,
    )
    content, _ = await s3_utils.download(file)
    return content


async def stream_signature_content(
    request: web.Request,
    document_id: str,
    signature_id: str,
    filename: str = 'signature.p7s',
) -> web.StreamResponse:
    content = await download_internal_signature(
        document_id=document_id,
        signature_id=signature_id,
    )

    buffer = io.BytesIO(content)
    return await stream_file_buffer(request, filename, buffer)


async def download_document_content(
    conn: DBConnection,
    document: Document,
    version_id: str | None,
) -> bytes:
    """Download original or version from S3"""
    options = await get_document_download_options(
        conn=conn,
        document_id=document.id_,
        version_id=version_id,
    )
    file_options = FileOptions(
        document=document,
        download_file=options,
        file_name=document.file_name,
        raw_file_name=document.raw_file_name,
    )
    return await download_file_as_bytes(file_options)


async def download_latest_document_version(
    conn: DBConnection,
    document: Document,
) -> bytes:
    version = await document_versions.get_latest_document_version(
        conn=conn,
        document_id=document.id_,
    )
    return await download_document_content(
        conn=conn,
        document=document,
        version_id=version.id if version else None,
    )


async def download_asic_container_content(document_id: str) -> bytes | None:
    s3_key = get_asic_s3_key(document_id=document_id)

    file_exists = await s3_utils.exists(
        key=s3_key,
    )
    if not file_exists:
        return None

    content, _ = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
    return content


async def download_wrapped_signature_content(conn: DBConnection, document: Document) -> bytes:
    signature = await select_last_internal_signature(conn, document.id_)
    if signature is None:
        return await download_latest_document_version(conn, document)

    return await download_internal_signature(
        document_id=document.id_,
        signature_id=signature.id,
    )


async def download_review_history_pdf(
    conn: DBConnection, user: DBRow, ctx: DownloadReviewHistoryCtx
) -> web.StreamResponse:
    history = await select_review_history(conn, user, ctx.document)

    # TODO[SK]: Stuck op prod, need to investigate
    # content = await pdf.generate_review_history_file(history)

    content = pdf.generate_review_history_file_sync(
        history=history,
        locale_code=get_current_locale_code(),
        use_pdf_17=get_flag(FeatureFlags.SET_MIN_17_PDF_VERSION),
    )
    filename = _('ІсторіяПогодження-{document_id}.pdf').bind(document_id=history.document_id)
    return await stream_file_buffer(
        request=ctx.request,
        raw_buffer=content,
        file_name=filename.value,
    )


async def download_review_history_xlsx(
    conn: DBConnection, user: DBRow, ctx: DownloadReviewHistoryCtx
) -> web.StreamResponse:
    history = await select_review_history(conn, user, ctx.document)

    writer = XLSXWriter()
    writer.set_title(_('Погодженя'))

    writer.append_row(row=[_('Документ')], bold=True)
    writer.append_row(row=[_('ID'), history.document_id])
    writer.append_row(row=[_('Назва'), history.document_title])
    writer.append_row(row=[_('Номер'), history.document_number or ''])
    writer.append_row(row=[_('Дата отримання'), history.document_date_str])

    writer.append_empty_row()

    writer.append_row(
        row=[_('Співробітник'), _('Електронна адреса'), _('Дія'), _('Дата')], bold=True
    )

    for item in history.items:
        writer.append_row([item.user_name, item.user_email, item.action, item.date_str])

    content = writer.to_bytes()

    filename = _('ІсторіяПогодження-{document_id}.xlsx').bind(document_id=history.document_id)
    return await stream_file_buffer(
        request=ctx.request,
        raw_buffer=io.BytesIO(content),
        file_name=filename.value,
    )


async def download_review_history_content(
    conn: DBConnection, user: DBRow, ctx: DownloadReviewHistoryCtx
) -> web.StreamResponse:
    mapping = {'pdf': download_review_history_pdf, 'xlsx': download_review_history_xlsx}

    download_history = mapping.get(ctx.format_)
    if not download_history:
        raise Error(Code.not_implemented)

    return await download_history(conn, user, ctx)


async def get_document_download_options(
    conn: DBConnection,
    *,
    document_id: str,
    version_id: str | None,
    version: DocumentVersion | None = None,
) -> s3_utils.DownloadFile:
    """
    Get proper download options for a document, which takes into account
    document versioning.
    """

    s3_key = get_document_s3_key(document_id=document_id, version_id=version_id)

    if version:
        assert version.id == version_id, 'Version ID mismatch'
    elif version_id:
        # check that version exists
        await document_versions.get_expected_document_version(
            conn=conn,
            document_id=document_id,
            version_id=version_id,
        )

    return s3_utils.DownloadFile(key=s3_key)


async def get_download_document(conn: DBConnection, document_id: str) -> Document:
    """
    Get a document object required for downloading a document original content.
    """
    row = await select_document_by_id(conn, document_id)
    if not row:
        raise DoesNotExist(Object.document, document_id=document_id)

    return to_document(row)


def get_download_document_from_db_document(document: documents_types.Document) -> Document:
    """
    Convert "app.documents.types.Document" to "api.downloads.types.Document"
    """
    return to_document(document._row)


def get_documents_archive_s3_key(*, archive_id: str) -> str:
    return f'documents_archives/{archive_id}'


async def finalize_sign_session_on_viewer_download(
    conn: DBConnection,
    request: web.Request,
) -> None:
    """
    We can finalize sign session when a user downloads a document from viewer
    """
    if request[AUTH_METHOD_APP_KEY] != AuthMethod.sign_session:
        return

    if not expirable(request):
        return

    if not get_flag(FeatureFlags.USE_EXPIRABLE_URLS_FOR_MS_VIEWER):
        return

    if sign_session_id := get_sign_session_id_from_request(request):
        await sign_sessions_utils.finish_sign_session_base(
            conn=conn,
            sign_session_id=sign_session_id,
        )
