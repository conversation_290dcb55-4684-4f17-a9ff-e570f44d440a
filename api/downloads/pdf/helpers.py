from __future__ import annotations

import contextvars
import io
from collections.abc import Iterator, Sequence
from contextlib import contextmanager
from functools import partial
from typing import Any, Literal, cast

import pikepdf
from reportlab.lib.colors import Color, black
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import Paragraph, SimpleDocTemplate, Table

from api.downloads.pdf import constants
from api.downloads.pdf.types import StyleCommand
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib.helpers import set_contextvar

bold_header = partial(Paragraph, style=constants.BOLD_HEADER_STYLE)
bold = partial(Paragraph, style=constants.BOLD_STYLE)
regular = partial(Paragraph, style=constants.REGULAR_STYLE)
regular_sign = partial(Paragraph, style=constants.REGULAR_SIGN_STYLE)
regular_stamp = partial(Paragraph, style=constants.REGULAR_STAMP_STYLE)
small = partial(Paragraph, style=constants.SMALL_STYLE)
hint = partial(Paragraph, style=constants.HINT_STYLE)


def register_fonts() -> None:
    """Register fonts for reportlab."""

    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.rl_config import TTFSearchPath

    TTFSearchPath.append(constants.FONTS_DIR)

    # Register regular and bold Roboto fonts
    roboto_regular = TTFont(
        name='Roboto',
        filename=f'{constants.FONTS_DIR}/RobotoRegular.ttf',
    )
    roboto_bold = TTFont(
        name='Roboto-Bold',
        filename=f'{constants.FONTS_DIR}/RobotoBold.ttf',
    )
    # Register DejaVuSans font
    # DejaVuSans is used for displaying non-latin characters
    # such as ✕ (2715)
    # can be found in system in /usr/share/fonts/truetype/dejavu/DejaVuSans.ttf
    # or with command fc-list
    # copied to static folder to avoid dependency on system fonts in case of update
    # or change of a system
    dejavu_sans = TTFont(
        name='DejaVuSans',
        filename=f'{constants.FONTS_DIR}/DejaVuSans.ttf',
    )
    pdfmetrics.registerFont(roboto_regular)
    pdfmetrics.registerFont(roboto_bold)
    pdfmetrics.registerFont(dejavu_sans)


class TableStyleCommandBuilder:
    """Builder for table style commands."""

    def __init__(self, command: str, args: list[Any]) -> None:
        self._command: str = command
        self._start: tuple[int, int] = (0, 0)
        self._end: tuple[int, int] = (-1, -1)
        self._args: list[Any] = args

    def start(self, *, col: int, row: int) -> TableStyleCommandBuilder:
        self._start = (col, row)
        return self

    def end(self, *, col: int, row: int) -> TableStyleCommandBuilder:
        self._end = (col, row)
        return self

    def whole_table(self) -> TableStyleCommandBuilder:
        self._start = (0, 0)
        self._end = (-1, -1)
        return self

    def build(self) -> StyleCommand:
        return self._command, self._start, self._end, *self._args


class TableStyleBuilder:
    """Builder for table styles commands."""

    def __init__(self) -> None:
        self.styles: list[TableStyleCommandBuilder] = []

    def vertical_align(
        self,
        position: Literal['TOP', 'MIDDLE', 'BOTTOM'],
    ) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='VALIGN', args=[position])
        self.styles.append(builder)
        return builder

    def horizontal_align(
        self,
        position: Literal['LEFT', 'RIGHT', 'CENTER'],
    ) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='ALIGN', args=[position])
        self.styles.append(builder)
        return builder

    def left_padding(self, size: float) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='LEFTPADDING', args=[size])
        self.styles.append(builder)
        return builder

    def right_padding(self, size: float) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='RIGHTPADDING', args=[size])
        self.styles.append(builder)
        return builder

    def top_padding(self, size: float) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='TOPPADDING', args=[size])
        self.styles.append(builder)
        return builder

    def bottom_padding(self, size: float) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='BOTTOMPADDING', args=[size])
        self.styles.append(builder)
        return builder

    def box(self, size: float, color: Color) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='BOX', args=[size, color])
        self.styles.append(builder)
        return builder

    def span(self) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='SPAN', args=[])
        self.styles.append(builder)
        return builder

    def grid(
        self,
        size: int,
        color: Color | None = None,
    ) -> TableStyleCommandBuilder:
        builder = TableStyleCommandBuilder(command='GRID', args=[size, color])
        self.styles.append(builder)
        return builder

    def reset_padding(self) -> None:
        self.left_padding(size=0).whole_table()
        self.right_padding(size=0).whole_table()
        self.top_padding(size=0).whole_table()
        self.bottom_padding(size=0).whole_table()

    def debug_grid(
        self,
        size: int = 1,
        color: Color | None = None,
    ) -> TableStyleCommandBuilder:
        return self.grid(size=size or 1, color=color or black).whole_table()

    def build(self) -> list[StyleCommand]:
        return [style.build() for style in self.styles]


class TableBuilder:
    """Builder for tables."""

    def __init__(self, columns: int) -> None:
        self.rows: list[list[Any]] = []
        self.style = TableStyleBuilder()
        self.columns: int = columns
        self.columns_sizes: Sequence[float] | None = None
        self.rows_sizes: Sequence[float] | None = None

    def add_row(self, *cols: Any) -> None:
        """Add row to table."""

        _cols = list(cols)

        if len(_cols) > self.columns:
            raise ValueError(f'Number of columns should be less or equal to {self.columns}')

        # fill remaining columns with empty values
        if len(_cols) < self.columns:
            _cols += [''] * (self.columns - len(_cols))

        self.rows.append(_cols)

    def build(self) -> Table:
        return Table(
            self.rows,
            style=self.style.build(),
            colWidths=self.columns_sizes,
            rowHeights=self.rows_sizes,
        )


def whitespace_paragraph() -> Paragraph:
    """Return paragraph with whitespace"""
    return regular('&nbsp;')


def merge_pdf_page(merge_page: pikepdf.Page, input_page_bytes: io.BytesIO) -> None:
    """
    Merge given PDF page to given page

    WARNING: this function will modify given page in-place
    """
    pdf = pikepdf.Pdf.open(input_page_bytes)
    input_page = pdf.pages[0]

    merge_page.add_overlay(input_page, rect=None)
    pdf.close()


def normalize_pdf(content: io.BytesIO) -> io.BytesIO:
    """Re-save a file using pikepdf to fix PDF structure and convert it to 1.7 version"""
    content.seek(0)

    old_pdf = pikepdf.Pdf.open(content, attempt_recovery=True)
    new_pdf = pikepdf.Pdf.new()
    new_pdf.pages.extend(old_pdf.pages)
    old_pdf.close()
    return save_pikepdf_document(new_pdf)


_TEMP_USE_PDF_17 = contextvars.ContextVar[bool]('use_pdf_17')


@contextmanager
def set_pdf_version_temp_ctx(use_pdf_17: bool) -> Iterator[None]:
    with set_contextvar(_TEMP_USE_PDF_17, value=use_pdf_17):
        yield


def save_pikepdf_document(pdf: pikepdf.Pdf) -> io.BytesIO:
    """Save PDF to BytesIO stream"""
    content = io.BytesIO()

    if not _TEMP_USE_PDF_17.get():
        pdf.save(content)
        return content

    pdf.save(
        content,
        # We use ReportLab to generate additional pages and visual elements, which are then
        # merged into our original PDF. Since ReportLab can only generate PDFs of versions 1.3 or
        # 1.4, without any parameters, the final PDF will also be in either 1.3 or 1.4 version.
        # Both versions are quite old (from 2001), and not all PDF readers support opening them.
        # That's why we try to set the version to at least 1.7, which is a more common version
        # supported by most PDF readers. Ideally, in the future, we should aim to set the version
        # to 2.0 or higher, as it is the latest version of the PDF standard published
        # by ISO in 2017
        min_version='1.7',
    )
    return content


def build_simple_doc(
    mixed: Any,
    *,
    page_size: tuple[float, float] = A4,
    left_margin: float = cm,
    right_margin: float = cm,
    top_margin: float = cm,
    bottom_margin: float = cm,
) -> io.BytesIO:
    """
    Build PDF file via reportlab's SimpleDocTemplate.

    This function is quite slow, so if you are building a lot of PDFs as overlays for
    other PDFs, consider a caching result of this function.
    """
    buffer = io.BytesIO()

    doc = SimpleDocTemplate(
        buffer,
        pagesize=page_size,
        leftMargin=left_margin,
        rightMargin=right_margin,
        topMargin=top_margin,
        bottomMargin=bottom_margin,
    )
    doc.build(mixed)

    return buffer


def code_label(is_legal: bool) -> LazyI18nString:
    """Label for legal or natural person sign due to given flag."""
    return _('ЄДРПОУ/ІПН') if is_legal else _('Ідентифікаційний код')


def get_page_size(page: pikepdf.Page) -> tuple[float, float]:
    """Return page size and ensure that width & height are in floats."""
    width, height = cast(int, page.mediabox[2]), cast(int, page.mediabox[3])

    if '/Rotate' in page:
        rotation = cast(int, page.Rotate)
        if (rotation // 90) % 2 != 0:
            return float(height), float(width)

    return float(width), float(height)


def build_table(data: list[list[LazyI18nString | str]]) -> Table:
    cell_style = ParagraphStyle(name='Regular', fontName='Roboto', fontSize=10)
    table_style = [
        ('LEFTPADDING', (0, 0), (-1, -1), 0),
        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
        ('TOPPADDING', (0, 0), (-1, -1), 0),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]
    cell = partial(Paragraph, style=cell_style)
    return Table(
        [[cell(str(item or '')) for item in items] for items in data],
        rowHeights=23,
        style=table_style,
    )
