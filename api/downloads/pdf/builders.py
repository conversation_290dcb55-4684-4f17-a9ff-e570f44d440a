import io
import logging
from math import (
    ceil,
    floor,
)
from typing import (
    Any,
)

import pikepdf
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import (
    Image,
    Paragraph,
    Table,
)

from api.downloads.pdf import constants
from api.downloads.pdf.helpers import (
    bold,
    bold_header,
    build_simple_doc,
    build_table,
    code_label,
    get_page_size,
    hint,
    merge_pdf_page,
    normalize_pdf,
    regular,
    regular_sign,
    regular_stamp,
    save_pikepdf_document,
    small,
    whitespace_paragraph,
)
from api.downloads.types import (
    Document,
    Review,
    SignaturesMatrix,
    ViewerSignature,
    ViewerSignatureEusign,
)
from app.config import is_test_environ
from app.document_revoke.types import DocumentRevoke
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib.enums import RenderSignatureAtPage, SignatureType
from app.reviews.enums import ReviewType
from app.reviews.types import ReviewHistory

logger = logging.getLogger(__name__)


class PrintFileBuilder:
    """Class for building document PDF for print."""

    def __init__(
        self,
        document: Document,
        original_content: bytes,
        *,
        signatures: list[ViewerSignature],
        reviews: list[Review],
        render_at_page: RenderSignatureAtPage,
        render_signature: bool,
        render_review: bool,
        revoke_signatures: list[ViewerSignature],
        revoke: DocumentRevoke | None,
    ):
        self.document = document
        self.original_content = original_content
        self.signatures = signatures
        self.reviews = reviews
        self.render_at_page = render_at_page
        self.render_signature = render_signature
        self.render_review = render_review
        self.revoke_signatures = revoke_signatures
        self.revoke = revoke

    def _build_watermark(self, content: io.BytesIO) -> pikepdf.Pdf:
        """
        Add a watermark to the bottom of the last page of the original PDF.
        """

        # Add all but last pages from original as is
        content_writer = pikepdf.Pdf.new()

        content_reader = pikepdf.Pdf.open(content)
        total = len(content_reader.pages)
        last_page_idx = total - 1
        content_writer.pages.extend(content_reader.pages[:last_page_idx])

        # Add watermark to last page of original content
        last_page = content_reader.pages[last_page_idx]
        width, height = get_page_size(last_page)

        # Prepare page with watermark
        watermark = build_simple_doc(
            [
                bold(_('Документ підписано у сервісі Вчасно (початок)')),
                regular(self.document.file_name),
            ],
            page_size=(width, height),
            left_margin=width - 10 * cm,
            top_margin=height - 2.5 * cm,
        )

        merge_pdf_page(last_page, watermark)
        content_writer.pages.append(last_page)
        content_reader.close()
        return content_writer

    def _build_signed_file(self, content: io.BytesIO) -> io.BytesIO:
        try:
            content = normalize_pdf(content)

            builder = SignedFileBuilder(
                document=self.document,
                original=content,
                signatures=self.signatures,
                reviews=self.reviews,
                render_at_page=self.render_at_page,
                render_signature=self.render_signature,
                render_review=self.render_review,
            )
            content = builder.build_base(content=content)
        except Exception:
            if is_test_environ():
                raise  # to catch error in tests, but ignore on production

            logger.warning(
                'Error during generation file with signatures images',
                extra={'document_id': self.document.id_},
                exc_info=True,
            )

        return content

    def _build_signature_details(self) -> pikepdf.Pdf:
        builder = SignatureDetailsBuilder(
            document=self.document,
            signatures=self.signatures,
            revoke_signatures=self.revoke_signatures,
            revoke=self.revoke,
            is_separate_file=False,
        )
        signatures = builder.build()

        return pikepdf.Pdf.open(signatures)

    def build(self) -> io.BytesIO:
        """Build print file."""

        # non-PDF file - nothing to add
        content = io.BytesIO(self.original_content)
        if not self.document.is_pdf:
            return content

        # If necessary add signatures images even in print mode
        content = self._build_signed_file(content)

        # Merge original content with watermark and signature details
        merger = pikepdf.Pdf.new()
        try:
            watermark = self._build_watermark(content)
            signature_details = self._build_signature_details()

            merger.pages.extend(watermark.pages)
            merger.pages.extend(signature_details.pages)

            return save_pikepdf_document(merger)

        except Exception:
            if is_test_environ():
                raise  # to catch error in tests, but ignore on production

            logger.warning(
                'Error during generation watermark file',
                extra={'document_id': self.document.id_},
                exc_info=True,
            )
            # Just return signed file
            return content


class SignatureDetailsBuilder:
    """
    Generate A4 page with text details about document signatures.

    This page will later be appended to the original content on printing or saved
    as a separate file on archive downloading.

    NOTE: In our team, it is also known as "Квитанція" or "Протокол підписання"
    """

    def __init__(
        self,
        *,
        document: Document,
        signatures: list[ViewerSignature],
        revoke_signatures: list[ViewerSignature],
        revoke: DocumentRevoke | None = None,
        is_separate_file: bool = False,
    ):
        self.document = document
        self.signatures = signatures
        self.is_separate_file = is_separate_file
        self.revoke_signatures = revoke_signatures
        self.revoke = revoke

    @staticmethod
    def _prepare_eusign_signature_paragraphs(
        signature: ViewerSignatureEusign,
    ) -> list[Paragraph]:
        is_stamp = signature.type_ == SignatureType.stamp
        type_row = _('Електронна печатка') if is_stamp else _('Електронний підпис')

        date_created_row = format(signature.date_created, constants.DATETIME_FORMAT)

        # Company EDRPOU or IPN label
        company_edrpou_label = code_label(signature.is_legal)
        company_edrpou = signature.edrpou or ''
        company_edrpou_row = f'{company_edrpou_label}: {company_edrpou}'

        company_name_row = ''
        if company_name := signature.company_name:
            company_name_label = _('Юр. назва: ') if signature.is_legal else ''
            company_name_row = f'{company_name_label}{company_name}'

        # For stamp need to display company name instead of owner name
        owner_label = signature.position or _('Власник ключа')
        owner_name = signature.company_name if is_stamp else signature.name
        owner_name = owner_name or ''
        owner_row = f'{owner_label}: {owner_name}'

        # Type when signature was verified
        time_mark = format(signature.time_mark, constants.DATETIME_FORMAT)
        time_mark_row = _('Час перевірки КЕП/ЕЦП: {0}').bind(time_mark)

        serial_number_row = _('Серійний номер: {0}').bind(signature.serial_number or '')

        # Power type of signature
        signature_power_row = LazyI18nString.empty()
        if signature_power_type := signature.signature_power_type:
            signature_power_type_name = constants.SIGNATURE_POWER_MAPPING[signature_power_type]
            signature_power_row = _('Тип підпису: {0}').bind(signature_power_type_name)

        # Power type of certificate
        certificate_power_row = LazyI18nString.empty()
        if certificate_power_type := signature.certificate_power_type:
            certificate_power_type_name = constants.CERTIFICATE_POWER_MAPPING[
                certificate_power_type
            ]
            certificate_power_row = _('Тип сертифікату: {0}').bind(certificate_power_type_name)

        return [
            whitespace_paragraph(),
            bold(type_row),
            regular(date_created_row),
            regular(company_edrpou_row),
            regular(company_name_row),
            regular(owner_row),
            regular(time_mark_row),
            regular(_('Статус перевірки сертифікату: Сертифікат діє')),
            small(serial_number_row),
            regular(signature_power_row),
            regular(certificate_power_row),
        ]

    def _prepare_signature_paragraphs(self, signature: ViewerSignature) -> list[Paragraph]:
        """Return paragraphs with signature detailed info."""

        if isinstance(signature, ViewerSignatureEusign):
            return self._prepare_eusign_signature_paragraphs(signature)
        return []

    def _prepare_signatures_rows(
        self,
        signatures: list[ViewerSignature],
        *,
        is_owner: bool,
    ) -> list[list[Any]]:
        """Return rows with detailed info about given signature."""
        label = bold(_('Відправник документу') if is_owner else _('Отримувач документу'))
        rows: list[list[Any]] = [[label]]
        for signature in signatures:
            details = self._prepare_signature_paragraphs(signature)
            rows.append([details])

        return rows

    def build(self) -> io.BytesIO:
        if self.is_separate_file:
            label = _('Документ підписано у сервісі Вчасно')
        else:
            label = _('Документ підписано у сервісі Вчасно (продовження)')

        content = [
            bold(label),
            regular(self.document.file_name),
        ]
        if self.document.number:
            content.append(regular(_('Номер документу: {0}').bind(self.document.number)))

        content.append(whitespace_paragraph())

        extra_paragragh = False
        if len(self.signatures):
            extra_paragragh = True
            value = format(self.signatures[0].date_created, constants.DATETIME_FORMAT)
            date_created_row = _('Документ відправлено: {datetime}').bind(datetime=value)
            content.append(regular(date_created_row))

        if self.document.date_delivered:
            extra_paragragh = True
            value = format(self.document.date_delivered, constants.DATETIME_FORMAT)
            date_delivered_row = _('Документ отримано: {datetime}').bind(datetime=value)
            content.append(regular(date_delivered_row))

        if self.document.status.is_rejected and self.document.date_rejected:
            extra_paragragh = True
            value = format(self.document.date_rejected, constants.DATETIME_FORMAT)
            date_rejected_row = _('Документ відхилено: {datetime}').bind(datetime=value)
            content.append(regular(date_rejected_row))

        if extra_paragragh:
            content.append(whitespace_paragraph())

        is_3p = self.document.is_3p
        owner_signatures = [item for item in self.signatures if item.is_owner]
        rows = self._prepare_signatures_rows(owner_signatures, is_owner=not is_3p)

        recipient_signatures = [item for item in self.signatures if not item.is_owner]
        if recipient_signatures:
            rows.extend(self._prepare_signatures_rows(recipient_signatures, is_owner=is_3p))

        if self.document.status.is_revoked and self.revoke:
            rows.append([whitespace_paragraph()])
            rows.append([bold(_('Документ анульовано'))])
            rows.append([regular(_('Причина: {reason}').bind(reason=self.revoke.reason))])

            owner_signatures = [item for item in self.revoke_signatures if item.is_owner]
            rows.extend(self._prepare_signatures_rows(owner_signatures, is_owner=not is_3p))

            if recipient_signatures := [
                item for item in self.revoke_signatures if not item.is_owner
            ]:
                rows.extend(self._prepare_signatures_rows(recipient_signatures, is_owner=is_3p))

        table_style = [
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (0, 0), cm / 2),
            ('RIGHTPADDING', (1, 0), (-1, -1), 0),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]
        content.append(Table(rows, style=table_style))

        buffer = build_simple_doc(content)
        return normalize_pdf(buffer)


class ReviewsFileBuilder:
    def __init__(
        self,
        reviews: list[Review],
        *,
        width: float,
        height: float,
        top_margin: float,
    ) -> None:
        self.reviews = reviews
        self.width = width
        self.height = height
        self.top_margin = top_margin

    @staticmethod
    def _prepare_review(review: Review) -> tuple[Image, Paragraph]:
        icon = constants.CHECK_ICON if review.type_ == ReviewType.approve else constants.CLEAR_ICON
        return Image(icon, width=8, height=8), regular(review.email)

    def _prepare_summary(self) -> list[list[Any]]:
        result = [[None, None, None, None, None, None] for __ in range(3)]

        has_lot_reviews = len(self.reviews) > 9
        reviews_hidden = len(self.reviews) - 8

        # prepare 1st, 2nd and 3rd columns respectively
        for idx, review in enumerate(self.reviews[:3]):
            result[idx][0], result[idx][1] = self._prepare_review(review)

        for idx, review in enumerate(self.reviews[3:6]):
            result[idx][2], result[idx][3] = self._prepare_review(review)

        last_idx = 8 if has_lot_reviews else 9
        for idx, review in enumerate(self.reviews[6:last_idx]):
            result[idx][4], result[idx][5] = self._prepare_review(review)

        if has_lot_reviews:
            result[2][5] = hint(_('...і ще {count}').bind(count=reviews_hidden))

        review_header_row = [None, bold(_('Погоджено:')), None, None, None, None]
        result.insert(0, review_header_row)

        return result

    def build(self) -> io.BytesIO:
        row_height = 20
        icon_width = 16
        reviewer_width = self.width / 3.4

        left_margin = cm

        review_rows = self._prepare_summary()

        return build_simple_doc(
            [
                Table(
                    review_rows,
                    colWidths=(icon_width, reviewer_width) * 3,
                    rowHeights=row_height,
                    style=[
                        ('LEFTPADDING', (0, 0), (-1, -1), 0),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                        ('TOPPADDING', (0, 0), (-1, -1), 0),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                    ],
                )
            ],
            page_size=(self.width, self.height),
            left_margin=left_margin,
            top_margin=self.top_margin,
            bottom_margin=0,
        )


class SignedFileBuilder:
    """Put signature & stamp watermark on top of original content.

    Watermarks will be added to the page bottom to imitate regular signature
    and stamp.
    """

    def __init__(
        self,
        document: Document,
        original: io.BytesIO,
        signatures: list[ViewerSignature],
        reviews: list[Review],
        render_at_page: RenderSignatureAtPage,
        render_signature: bool,
        render_review: bool,
    ):
        self.document = document
        self.original = original
        self.signatures = signatures
        self.reviews = reviews
        self.render_at_page = render_at_page
        self.render_signature = render_signature
        self.render_review = render_review

        self._signature_matrix_cache: dict[str, SignaturesMatrix] = {}
        self._signature_images_cache: dict[str, io.BytesIO] = {}
        self._signature_revoked_cache: dict[str, io.BytesIO] = {}
        self._signature_details_cache: dict[str, io.BytesIO] = {}
        self._review_file_cache: dict[str, io.BytesIO] = {}

    def _get_revoked_watermark_file(self, width: float, height: float) -> io.BytesIO:
        """
        Create a watermark file with text: "АНУЛЬОВАНО".
        """

        # Try to get a file from cache
        cache_key = f'{width}-{height}'
        if cache_key in self._signature_revoked_cache:
            return self._signature_revoked_cache[cache_key]

        text = Paragraph(
            '✕ АНУЛЬОВАНО',
            style=ParagraphStyle(
                name='bold',
                fontName='DejaVuSans',
                fontSize=19,
                leading=35,
                textColor=constants.HexColor('#fd3515'),
                alignment=1,
            ),
        )
        table = Table([[text]], colWidths=7 * cm)
        table.setStyle(
            [
                ('BOX', (0, 0), (-1, -1), 2, constants.HexColor('#fd3515')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('TOPPADDING', (0, 0), (-1, -1), 15),
            ]
        )
        watermark = build_simple_doc(
            [
                table,
            ],
            page_size=(width, height),
            left_margin=width - 8.5 * cm,
            top_margin=1 * cm,
        )
        # Save file to cache
        self._signature_revoked_cache[cache_key] = watermark

        return watermark

    def _get_signatures_images_file(
        self,
        matrix: SignaturesMatrix,
        *,
        width: float,
        height: float,
        image_width: float,
        top_margin: float,
    ) -> io.BytesIO:
        """
        Generate PDF with empty signatures circles
        """

        # Try to get file from cache
        cache_key = f'{width}-{height}'
        if cache_key in self._signature_images_cache:
            return self._signature_images_cache[cache_key]

        margin = cm / 2
        signature_images = self._prepare_signatures_images(
            matrix=matrix,
            image_width=image_width,
            image_height=image_width,
        )
        doc = build_simple_doc(
            [
                Table(
                    signature_images,
                    colWidths=image_width,
                    rowHeights=image_width,
                    style=[
                        ('LEFTPADDING', (0, 0), (-1, -1), 0),
                        ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                        ('TOPPADDING', (0, 0), (-1, -1), 0),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                    ],
                )
            ],
            page_size=(width, height),
            left_margin=margin,
            right_margin=margin,
            top_margin=top_margin,
            bottom_margin=margin,
        )

        # Save file to cache
        self._signature_images_cache[cache_key] = doc

        return doc

    @staticmethod
    def _prepare_signatures_images(
        matrix: SignaturesMatrix, *, image_width: float, image_height: float
    ) -> list[list[Image | None]]:
        """Convert signatures matrix to images matrix."""
        result = []
        for row in matrix:
            result_row = []

            for item in row:
                image = constants.BLANK_IMAGE
                if item and item.type_ == SignatureType.signature:
                    image = constants.SIGN_TEMPLATE_IMAGE
                elif item:
                    image = constants.STAMP_TEMPLATE_IMAGE
                result_row.append(Image(image, width=image_width, height=image_height))

            result.append(result_row)
        return result

    @staticmethod
    def _split_keys_stamps(
        signatures: list[ViewerSignatureEusign],
    ) -> tuple[list[ViewerSignatureEusign], list[ViewerSignatureEusign]]:
        keys = []
        stamps = []
        for item in signatures:
            if item.type_ == SignatureType.signature:
                keys.append(item)
            else:
                stamps.append(item)
        return keys, stamps

    def _recalc_rows_max(
        self,
        type_counts: list[int],
        rows_max: int,
        cols_max: int,
        iterations: int = 5,
    ) -> int:
        if iterations == 0:
            return rows_max
        # `or 1` - save an empty placeholder for each missing type
        cols_required = sum(ceil(_count / rows_max) or 1 for _count in type_counts)
        if cols_required <= cols_max:
            return rows_max
        rows_max += 1
        iterations -= 1
        return self._recalc_rows_max(type_counts, rows_max, cols_max, iterations)

    def _prepare_signatures_matrix(
        self,
        width: float,
        height: float,
        image_width: float,
    ) -> SignaturesMatrix:
        """Prepare signatures matrix.

        Signatures matrix needed for rendering multiple signatures on the page.
        """

        # Try to get matrix from cache
        cache_key = f'{width}-{height}'
        if cache_key in self._signature_matrix_cache:
            return self._signature_matrix_cache[cache_key]

        # For signature matrix we are using only Eusign signatures, Visual signatures is
        # already visualised on document
        signatures_eusign: list[ViewerSignatureEusign] = [
            signature
            for signature in self.signatures
            if isinstance(signature, ViewerSignatureEusign)
        ]
        if not signatures_eusign:
            return [[None]]

        # split signatures by participants
        owner_signatures = [item for item in signatures_eusign if item.is_owner]
        recipient_signatures = [item for item in signatures_eusign if not item.is_owner]

        # split by type: key/stamp
        owner_keys, owner_stamps = self._split_keys_stamps(owner_signatures)
        recipient_keys, recipient_stamps = self._split_keys_stamps(recipient_signatures)

        # add some image border space
        _image_width_border = image_width + 0.5 * cm
        # calculating the maximum number of columns across the page width
        cols_max = floor(width / _image_width_border)
        # calculating the maximum number of rows
        # required to arrange all signatures circles in the available columns
        # excluding the condition that each new type must be located in a new column
        rows_max = ceil(len(signatures_eusign) / cols_max)

        matrix: SignaturesMatrix

        # insufficient document width for 4 columns
        # pack without separating types by columns
        if cols_max < 4:
            matrix = [[None for _ in range(cols_max)] for _ in range(rows_max)]

            rows_max -= 1  # -1 for indexing
            row = rows_max  # higher index => lower location in the picture
            col = 0
            for signature in signatures_eusign:
                matrix[row][col] = signature
                row -= 1
                if row < 0:
                    row = rows_max
                    col += 1
            return matrix

        _signatures_types = [owner_keys, owner_stamps, recipient_keys, recipient_stamps]
        _signatures_types_count = [len(_type) for _type in _signatures_types]

        # recursive calculation of the required number of rows
        # given the number of signatures of different types
        rows_max = self._recalc_rows_max(
            type_counts=_signatures_types_count,
            rows_max=rows_max,
            cols_max=cols_max,
        )

        if (rows_max * _image_width_border) > height:
            logger.warning('Signature images over page height')

        matrix = [[None for _ in range(cols_max)] for _ in range(rows_max)]

        rows_max -= 1  # -1 for indexing
        row = rows_max  # higher index => lower location in the picture
        col = 0
        for _signatures in _signatures_types:
            previous: ViewerSignatureEusign | None = None
            for signature in _signatures:
                if previous:
                    row -= 1
                    if row < 0:
                        row = rows_max
                        col += 1
                matrix[row][col] = signature
                previous = signature
            row = rows_max
            col += 1  # next signatures block in new column

        # save matrix to cache
        self._signature_matrix_cache[cache_key] = matrix

        return matrix

    @staticmethod
    def _prepare_signature_paragraphs(
        signature: ViewerSignatureEusign | None,
    ) -> list[Paragraph]:
        """Return paragraphs with signature info for render inside sign image."""
        if not signature:
            return [whitespace_paragraph()]

        is_stamp = signature.type_ == SignatureType.stamp
        paragraph = regular_stamp if is_stamp else regular_sign

        # For stamp need to display company name instead of owner name
        title = signature.company_name if is_stamp else signature.name

        return [
            paragraph(title or ''),
            paragraph(code_label(signature.is_legal)),
            paragraph(signature.edrpou or ''),
        ]

    def _prepare_signatures_paragraphs(
        self,
        matrix: SignaturesMatrix,
    ) -> list[list[list[Paragraph]]]:
        """Convert signatures matrix to images paragraphs matrix."""
        result = []
        for row in matrix:
            result.append([self._prepare_signature_paragraphs(item) for item in row])
        return result

    def _get_signatures_details_file(
        self,
        matrix: SignaturesMatrix,
        *,
        width: float,
        height: float,
        image_width: float,
        top_margin: float,
    ) -> io.BytesIO:
        """
        Generate PDF file with signatures paragraphs that should be places inside of
        empty signatures circles.
        """

        # Try to get file from cache
        cache_key = f'{width}-{height}'
        if cache_key in self._signature_details_cache:
            return self._signature_details_cache[cache_key]

        padding = 0.88 * cm
        margin = cm / 2
        paragraphs = self._prepare_signatures_paragraphs(matrix)
        doc = build_simple_doc(
            [
                Table(
                    paragraphs,
                    colWidths=image_width,
                    rowHeights=image_width,
                    style=[
                        ('LEFTPADDING', (0, 0), (-1, -1), padding),
                        ('RIGHTPADDING', (0, 0), (-1, -1), padding),
                        ('TOPPADDING', (0, 0), (-1, -1), padding),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), padding),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ],
                )
            ],
            page_size=(width, height),
            left_margin=margin,
            right_margin=margin,
            top_margin=top_margin,
            bottom_margin=margin,
        )

        # Save file to cache
        self._signature_details_cache[cache_key] = doc

        return doc

    def _render_signatures_on_page(
        self,
        page: pikepdf.Page,
        width: float,
        height: float,
        reviews_top_margin: float,
    ) -> None:
        """
        Render signature circles on the bottom of the page (also known as "Кружечки").
        """
        image_width = constants.SIGNATURE_CIRCLE_DIAMETER * cm

        # Prepare signatures matrix
        matrix = self._prepare_signatures_matrix(
            width=width,
            height=height,
            image_width=image_width,
        )

        signatures_top_margin = reviews_top_margin - image_width * len(matrix)
        # Add empty signature circles to the target page
        images_file = self._get_signatures_images_file(
            matrix=matrix,
            width=width,
            height=height,
            image_width=image_width,
            top_margin=signatures_top_margin,
        )
        merge_pdf_page(page, images_file)

        # Add the signer information inside the signatures circles
        details_file = self._get_signatures_details_file(
            matrix=matrix,
            width=width,
            height=height,
            image_width=image_width,
            top_margin=signatures_top_margin,
        )
        merge_pdf_page(page, details_file)

    def _render_review_on_page(
        self,
        page: pikepdf.Page,
        width: float,
        height: float,
        reviews_top_margin: float,
    ) -> None:
        """
        Render review information on the page.
        """

        # Try to get file from cache
        cache_key = f'{width}-{height}'
        if cache_key in self._review_file_cache:
            reviews_file = self._review_file_cache[cache_key]
            merge_pdf_page(page, reviews_file)
            return

        reviews_file_builder = ReviewsFileBuilder(
            reviews=self.reviews,
            width=width,
            height=height,
            top_margin=reviews_top_margin,
        )
        reviews_file = reviews_file_builder.build()

        # Save file to cache
        self._review_file_cache[cache_key] = reviews_file

        merge_pdf_page(page, reviews_file)

    def _render_review_signature_revoke_on_page(
        self,
        page: pikepdf.Page,
    ) -> None:
        """
        Render review and signature information on the page.
        """
        width, height = get_page_size(page)

        # Calculate the top margin for reviews block. Review block should be placed
        # under signature circles.
        reviews_block_height = (len(self.reviews[:3]) + 1) * 21
        reviews_top_margin = height - reviews_block_height - 0.6 * cm

        # Add signature circles to the target page, also known as "Кружечки"
        if self.render_signature and self.signatures:
            self._render_signatures_on_page(
                page=page,
                width=width,
                height=height,
                reviews_top_margin=reviews_top_margin,
            )

        # Add review info under signature circles
        if self.render_review and self.reviews:
            self._render_review_on_page(
                page=page,
                width=width,
                height=height,
                reviews_top_margin=reviews_top_margin,
            )

        # Add watermark to the document about revoked signature
        # but only if document is revoked (not in process of revocation)
        if self.document.status.is_revoked:
            watermark = self._get_revoked_watermark_file(width, height)
            merge_pdf_page(page, watermark)

    def build_base(
        self,
        content: io.BytesIO,
    ) -> io.BytesIO:
        """
        Add signature and review information to the original PDF on target page.

        NOTE: signature information is also known as "Кружечки"

        Parameters:
        - signatures: A list of the signatures to be added to the PDF file.
        - reviews: A list of the reviews to be added to the PDF file.
        - content: The original PDF file as a BytesIO object.
        - render_signature: A flag indicating whether to render the signature information.
        - render_review: A flag indicating whether to render the review information.
        - render_at_page : enum indicating where to render the signature and review information
            in the PDF file. Possible values are: first, last, all.

        Returns:
        - io.BytesIO: A BytesIO object containing the PDF file with the added signature
            and review information.
        """

        if (
            not (self.render_signature and self.signatures)
            and not (self.render_review and self.reviews)
        ) and not self.document.status.is_revoked:
            return content

        # Parse the first page of original content
        content_reader = pikepdf.Pdf.open(content)

        # Render signature and review information on target pages
        pages = []
        pages_total = len(content_reader.pages)
        for page_idx, page in enumerate(content_reader.pages):
            pages.append(page)

            if (
                self.render_at_page == RenderSignatureAtPage.all
                or (self.render_at_page == RenderSignatureAtPage.first and page_idx == 0)
                or (
                    self.render_at_page == RenderSignatureAtPage.last
                    and page_idx == pages_total - 1
                )
            ):
                self._render_review_signature_revoke_on_page(page)

        # Write resulted page to buffer
        content_writer = pikepdf.Pdf.new()
        content_writer.pages.extend(pages)
        content_reader.close()

        return save_pikepdf_document(content_writer)

    def build(self) -> io.BytesIO:
        # Non-PDF document, or no signatures, or disabled by company config -
        # nothing to add, or no need to render signatures
        content = self.original
        if not self.document.is_pdf:
            return content

        try:
            content = normalize_pdf(content)
            content = self.build_base(content=content)

        except Exception:
            if is_test_environ():
                raise  # to catch error in tests, but ignore on production

            logger.warning(
                'Error during generation file with signatures images',
                extra={'document_id': self.document.id_},
                exc_info=True,
            )

        content.seek(0)
        return content


class ReviewHistoryBuilder:
    """Prepare PDF document with review history (Also known as Протокол погодження)."""

    def __init__(self, history: ReviewHistory):
        self.history = history

    def _build_document_table(self) -> Table:
        """Build table with basic document info."""
        return build_table(
            [
                [_('ID'), self.history.document_id],
                [_('Назва'), self.history.document_title],
                [_('Номер'), self.history.document_number or ''],
                [_('Дата завантаження або отримання'), self.history.document_date_str],
            ]
        )

    def _build_review_table(self) -> Table:
        """Build table with review blocks."""
        reviews_items = (
            build_table(
                data=[
                    [review.user_name, review.date_str],
                    [review.user_email, review.action],
                ],
            )
            for review in self.history.items
        )
        return Table(
            [[item] for item in reviews_items],
            style=[
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 22),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                ('VALIGN', (0, 0), (-1, -1), 'CENTER'),
            ],
        )

    def build(self) -> io.BytesIO:
        document_table = self._build_document_table()
        reviews_table = self._build_review_table()

        content = [
            bold_header(_('Внутрішнє погодження документу')),
            whitespace_paragraph(),
            whitespace_paragraph(),
            document_table,
            whitespace_paragraph(),
            whitespace_paragraph(),
            reviews_table,
        ]

        buffer = build_simple_doc(content)
        return normalize_pdf(buffer)
