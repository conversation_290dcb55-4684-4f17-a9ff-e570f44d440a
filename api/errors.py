from enum import Enum, unique
from typing import Any

from aiohttp import web

from api.utils import api_response
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib.types import DataDict, StrList

FIELD_IS_REQUIRED = _("Поле обов'язкове")


@unique
class Code(Enum):
    error = _('Помилка')
    conflict = _('Помилка, для вирішення повторіть спробу')

    access_denied = _('Доступ заборонено')
    bad_zip_file = _('Не валідний zip-архів')
    billing_account_type_not_allowed = _('Неможливо опрацювати даний тип акаунта')
    billing_bonus_type_not_allowed = _('Неможливо опрацювати даний тип бонуса')
    billing_reject_delete_account = _('Неможливо видалити рахунок')
    billing_not_enough_resources = _('Компанія не має достатньої кількості документів')
    billing_overlimit = _(
        'Перевищено кількість підписаних та відправлених документів, '
        'дозволених Вашим поточним тарифом'
    )
    disabled_option = _('Доступ до функціоналу не активований')
    duplicated_documents_db = _('В систему вже завантажено аналогічний документ')
    duplicated_documents_request = _(
        'Один чи більше з вибраних файлів мають однаковий тип та номер. Перевірте своє завантаження'
    )
    empty_upload_csv = _('Не знайдено правильно підготовлених рядків даних')
    empty_upload_documents = _(
        'Запит не містить файлів для завантаження, які підтримуються системою. '
        'Перевірте файли, вибрані до завантаження'
    )
    error_404 = _('Сторінку не знайдено')
    error_500 = _('Помилка серверу')
    error_504 = _('Запит було завершено за таймаутом')
    invalid_action = _('Неможливо виконати цю дію')
    invalid_archive_file = _('Неможливо розархівувати файл')
    invalid_auth_method = _('Невалідний метод аутентифікації')
    invalid_credentials = _('Помилка в логіні або паролі')
    invalid_current_password = _('Помилка в паролі')
    invalid_document_link = "Неможливо створити зв'язок документів"
    invalid_document_status = _('Невалідний статус документу')
    invalid_download_host = _(
        'Вказаний хост для завантаження файлу не вказано в переліку налаштувань поточної компанії'
    )
    invalid_edrpou = _('Ваш ключ не має ЄДРПОУ/ІПН. Зверніться до свого АЦСК')
    invalid_file_extension = _(
        'Формат файлу не підтримується, поки що ми приймаємо тільки {allowed_extensions}'
    )
    invalid_ip = _('Доступ по IP заборонено')
    invalid_json_request = _('Невалідний запит до серверу')
    cloud_signer_error = _('Помилка запиту до сервісу підписання')
    eusign_client_dynamic_key_session_create = _('Помилка створення зашифрованої сесії')
    eusign_session_load = _('Помилка завантаження зашифрованої сесії')
    eusign_get_signer_info = _('Помилка отримання інформації по підписувачу')
    invalid_p7s_external = _('Неможливо веріфікувати підпис документу')
    invalid_p7s_external_value = _('Неможливо зчитати підпис документу')

    # {{ Errors for internal signatures (signatures in containers p7s/ASiC)
    invalid_container_internal = _('Неможливо веріфікувати внутрішній підпис документу')
    invalid_container_internal_mapping_key = _(
        'Неможливо веріфікувати внутрішній підпис документу через '
        'невідповідність ключів всередині контейнера'
    )
    invalid_container_internal_original = _(
        'Неможливо веріфікувати внутрішній підпис документу через помилку '
        'читання змісту документу з контейнера'
    )
    invalid_container_internal_sign_count = _(
        'Неможливо веріфікувати внутрішній підпис документу, кількість '
        'очікуваних підписів не співпадає з кількістю підписів в контейнері'
    )
    # }}
    invalid_signature_format = _('Неможливо конвертувати підпис формату {signature_format}')
    invalid_p7s_key_type = _(
        'Спроба додати печатку на місце підпису, будь-ласка завантажуйте печатку у полі "stamp"'
    )
    invalid_p7s_stamp_type = _(
        'Спроба додати підпис на місце печатки, будь-ласка завантажуйте підпис у полі "key"'
    )
    invalid_phone_number = _('Невалідний номер телефону, вкажіть інший, будь ласка')
    invalid_remote_response = _('Невалідний статус відповіді з вказаного серверу')
    invalid_referer = _("Відсутній обов'язковий параметр для запиту до серверу")
    invalid_request = _('Виникла помилка, перевірте введені дані')
    invalid_owner_edrpou = _(
        'Неможливо завантажити файли іншої компанії. Будь ласка, '
        'переконайтеся, що всі файли належать до вашої поточної компанії'
    )
    invalid_sign_info_edrpou = _('Неможливо підписати документ, використовуючи вказане ЄДРПОУ')
    invalid_sign_info_edrpou_v2 = _('Неможливо завантажити підпис іншої компанії')
    invalid_sign_session_status = _(
        'Сесія підписання має невірний статус. Будь ласка, переконайтеся, '
        'що ви робите вірний запит на оновлення сесії підписання'
    )
    invalid_token = _('Токен недійсний. Будь ласка зателефонуйте до служби підтримки')
    invalid_token_recover_password = _(
        'Ви перейшли за застарілим посиланням. Сформуйте новий запит на зміну паролю.'
    )
    invalid_token_old_format = _(
        'Використовується застарілий токен доступу в API.Сформуйте будь ласка новий токен.'
    )
    invalid_email_domain_registration = _(
        'Неможливо зареєструватися в цій компанії, використовуючи Ваш email. '
        'Спробуйте змінити email на валідний для компанії, яка надіслала Вам '
        'запрошення у сервіс або пройдіть реєстрацію самостійно.'
    )
    invalid_email_domain_inviting = _(
        'Неможливо запросити користувача з вказаним email. У компанії '
        '{edrpou} використовується email адреса, що закінчується на '
        '{domains}'
    )
    invalid_email_domain_create_role = _(
        'Неможливо створити роль для користувача з вказаним email. У компанії '
        '{edrpou} використовується email адреса, що закінчується на '
        '{domains}'
    )
    invalid_totp_code = _('Код невірний. Спробуйте ще раз')
    invalid_url2pdf_response = _(
        'Не вдалося згенерувати PDF файл через помилку стороннього сервісу'
    )
    invalid_version = _('Неможливо виконати запит до версії {version}. Доступні версії: {expected}')
    invalid_xml = _('Неможливо розібрати завантаженний XML через помилку: {err}')
    invalid_xml_request = _('Невалідний XML')
    invalid_xml_to_json_request = _('Поточний документ не є XML документом')
    xml_document_does_not_match_schema = _('XML-документ не відповідає очікуваній схемі даних')
    invalid_preview_as_pdf = _('Поточний документ не може мати PDF файлу для відображення')
    youcontrol_request_error = _('Помилка запиту до YouControl')
    login_required = _('Будь ласка, авторизуйтесь для доступу до сторінки')
    max_file_size = _(
        'Розмір файла занадто великий, оберіть файл з розміром менше ніж {max_file_size}МБ'
    )
    empty_upload_file = _('Неможливо завантажити порожній файл')
    max_total_count = _(
        'Занадто багато файлів, кількість файлів для завантаження не має бути '
        'більше ніж {max_total_count}'
    )
    max_total_size = _(
        'Розмір файлів занадто великий, оберіть файли з розміром менше ніж {max_total_size}МБ'
    )
    not_implemented = _('Неможливо виконати запит, функціонал не реалізовано')
    object_already_exists = "Об'єкт вже існує у базі даних"
    object_does_not_exist = "Об'єкт не знайдено у базі даних"
    phone_not_verified = _('Номер телефону поки що не підтверджено')
    proxy_error = _('Помилка запиту до proxy сервісу')
    reject_delete_only_role = _('Неможливо видалити єдину роль користувача з компанії')
    reject_delete_self_role = _('Ви не можете видалити себе з компанії, де ви є адміністратором')
    reject_self_recipient = _('Документ не може бути відправлений на власний Email/ЄДРПОУ')
    s3_error = _('Помилка роботи зі стороннім сервісом зберігання файлів')
    smtp_error = _('Неможливо відправити повідомлення через помилку e-mail серверу')
    token_already_used = _('Токен вже було використано для реєстрації користувача у сервісі')
    unhandled_error = _('Необроблена помилка серверу')
    unknown_encoding = _(
        'Кодування файлу не підтримується. Спробуйте зберегти файл у кодуванні UTF-8'
    )
    upload_overlimit = _('Перевищено кількість завантажених документів, дозволених Вашій компанії')
    verify_2fa_required = _('Будь ласка, підтвердіть себе через двофакторну авторизацію')
    documents_requested_found_mismatch = _(
        'Неможливо сформувати архів, один або більше файлів не знайдено'
    )
    too_many_request = _('Перевищено ліміт запитів. Спробуйте трохи згодом.')
    too_many_request_to_invite_contacts = _(
        'Ви вже надсилали запрошення вашим контактам. Спробуйте трохи згодом.'
    )
    too_many_request_download_dossier = _(
        'Ви нещодавно завантажували досьє цього контрагента. Спробуйте ще раз за {seconds} секунд.'
    )
    not_all_documents_in_finished_status = _('Не всі документи в статусі "Завершено"')
    one_or_more_documents_not_found = _(
        'Неможливо сформувати запит на видалення, один або більше документів не знайдено'
    )
    delete_requests_mismatch = _('delete_requests_mismatch')
    not_all_delete_request_allowed = _('not_all_delete_request_allowed')
    not_all_delete_requests_are_new = _('not_all_delete_requests_are_new')
    delete_request_by_doc_does_not_exist = _('delete_request_by_doc_does_not_exist')
    external_resource_error = _('Помилка запиту до стороннього сервісу')
    review_is_required = _(
        "Неможливо {action} документ, що знаходиться на обов'язковому "
        'погодженні Вашими співробітниками.'
    )

    temporary_unavailable = _('Функціонал тимчасово недоступний')

    overdraft = _('Перевищено ліміт, зазначений тарифом')
    invalid_time_range = _(
        'Не валідні часові рамки. Можна отримати інформацію не більше як за {days} днів'
    )

    without_roles = _('Користувач не має жодної ролі в компанії')
    oauth_without_email = _(
        'Ваш профіль на стороні провайдеру не містить імейл адреси. '
        'Ми не можемо продовжити реєстрацію без імейл адреси, тому перевірте '
        'налаштування на стороні провайдера або оберіть інший спосіб реєстрації'
    )
    invalid_email_provided = _('Електронні пошти не збігаються')
    session_expired = _('Сесія закінчилася, будь ласка, авторизуйтесь знову')
    report_not_found = _('Звіт недоступний для завантаження')


@unique
class Object(Enum):
    archive = (_('Архів'), _('Архів'))
    bill = (_('Рахунок'), _('Рахунок'))
    billing_account = (_('Платіжний рахунок'), _('Платіжний рахунок'))
    bonus = (_('Бонус'), _('Бонус'))
    company = (_('Компанія'), _('Компанію'))
    contact = (_('Контакт'), _('Контакт'))
    contact_person = (_('Контактна особа'), _('Контактну особу'))
    config = (_('Конфіг'), _('Конфіг'))
    document = (_('Документ'), _('Документ'))
    documents = (_('Документи'), _('Документи'))
    document_archive = (_('Архівний документ'), _('Архівний документ'))
    email = (_('Е-Mail'), _('E-Mail'))
    key = (_('Ключ КЕП/ЕЦП'), _('Ключ КЕП/ЕЦП'))
    recipient = (_('Отримувач'), _('Отримувача'))
    review = (
        _('Результат внутрішнього погодження'),
        _('Результат внутрішнього погодження'),
    )
    review_request = (
        _('Запит на внутрішнє погодження'),
        _('Запит на внутрішнє погодження'),
    )
    role = (_('Роль'), _('Роль'))
    external_role = (_('Зовнішня роль'), _('Зовнішню роль'))
    roles = (_('Ролі'), _('Ролі'))
    group = (_('Група'), _('Групи'))
    groups = (_('Групи'), _('Груп'))
    group_member = (_('Учасник групи'), _('Учасника групи'))
    group_members = (_('Учасники групи'), _('Учасників групи'))
    sign_session = (_('Сесія підписання документу'), _('Сесію підписання документу'))
    signature = (_('Підпис'), _('Підпис'))
    stamp = (_('Печатка'), _('Печатка'))
    token = (_('Токен'), _('Токен'))
    user = (_('Користувач'), _('Користувача'))
    tag = (_('Ярлик'), _('Ярлик'))
    tags = (_('Ярлики'), _('Ярлики'))
    flow = (_('Процес підписання'), _('Процес підписання'))
    banner = (_('Банер'), _('Банера'))
    documents_field = (_('Параметр документу'), _('Параметр документа'))
    ttn = (_('ТТН'), _('ТТН'))
    company_rate = (_('Тариф компанії'), _('Тарифу компанії'))
    document_version = (_('Версія документу'), _('Версію документу'))
    draft = (_('Чорновик'), _('Чорновик'))
    template = (_('Сценарій'), _('Сценарію'))
    document_category = (_('Категорія документу'), _('Категорії документу'))
    document_template = (_('Шаблон'), _('Шаблону'))
    mobile_notification = (_('Пуш повідомлення'), _('Пуш повідомлення'))
    directory = (_('Папка'), _('Папки'))
    transaction = (_('Транзакція'), _('Транзакція'))
    revoke = (_('Акт анулювання'), _('Акт анулювання'))

    # WARN: Only for internal use
    feature_flag = (_('FeatureFlag'), _('FeatureFlag'))


class Error(Exception):
    code: Code = Code.error
    status: int = 400
    reason: LazyI18nString | None = None
    details: Any
    response_kwargs: Any
    log_extra: DataDict

    def __init__(
        self,
        code: Code | None = None,
        status: int | None = None,
        reason: LazyI18nString | None = None,
        raw_reason: str | None = None,
        details: Any | None = None,
        log_extra: DataDict | None = None,
        **kwargs: Any,
    ) -> None:
        self.code: Code = code or self.code
        self.status = status or self.status
        self.reason = reason or self.reason
        # It's an error reason without translation, use only in case when error text
        # is from external system that you don't control. In other case use LazyI18nString
        # argument "reason" to have better translation coverage
        self.raw_reason = raw_reason
        self.details = details
        self.response_kwargs = kwargs
        self.log_extra = log_extra or {}
        super().__init__(self.code.name)

    def __reduce__(self) -> Any:
        """Custom reduce function for support pickling"""
        args = (self.code, self.status, self.reason, self.details)
        return type(self), args

    def to_dict(self) -> DataDict:
        code, details = self.code, self.details

        reason_template: LazyI18nString | str
        reason_template = self.reason or self.raw_reason or code.value
        reason = reason_template.format(**(details or {}))

        return {
            'code': code and code.name,
            'reason': reason,
            'details': details,
        }

    def to_http_exception(self, request: web.Request) -> web.HTTPException:
        kwargs = self.response_kwargs or {}
        kwargs['status'] = self.status
        return api_response(  # type: ignore
            request,
            self.to_dict(),
            response_class=HTTPException,
            xml_root_name='error',
            **kwargs,
        )


class AccessDenied(Error):
    def __init__(
        self,
        *,
        reason: LazyI18nString | None = None,
        details: Any | None = None,
    ) -> None:
        super().__init__(Code.access_denied, status=403, reason=reason, details=details)


class ConflictError(Error):
    def __init__(self, *, reason: LazyI18nString | None = None) -> None:
        super().__init__(Code.conflict, status=409, reason=reason)


class AlreadyExists(Error):
    def __init__(self, obj: Object, *, reason: LazyI18nString | None = None, **kwargs: Any) -> None:
        if not reason:
            reason = _('{object} вже існує у базі даних').bind(object=obj.value[0])

        super().__init__(
            Code.object_already_exists,
            reason=reason,
            details=dict(kwargs, type=obj.name, type_label=obj.value[0]),
        )


class DoesNotExist(Error):
    def __init__(self, obj: Object, **kwargs: Any) -> None:
        super().__init__(
            Code.object_does_not_exist,
            status=404,
            reason=_('{object} не знайдено у базі даних').bind(object=obj.value[1]),
            details=dict(kwargs, type=obj.name, type_label=obj.value[0]),
        )


class InvalidRequest(Error):
    def __init__(
        self,
        *,
        reason: LazyI18nString | None = None,
        details: Any | None = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(Code.invalid_request, reason=reason, details=details or kwargs)


class TooManyRequests(Error):
    def __init__(
        self,
        *,
        reason: LazyI18nString | None = None,
        details: Any | None = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(
            Code.too_many_request,
            status=429,
            reason=reason,
            details=details or kwargs,
        )


class InvalidVersion(Error):
    def __init__(
        self,
        invalid_version: str,
        expected_versions: StrList | DataDict,
        *,
        reason: LazyI18nString | None = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(
            Code.invalid_version,
            reason=reason,
            details=dict(
                kwargs,
                version=invalid_version,
                expected=', '.join(sorted(expected_versions)),
            ),
        )


class LoginRequired(Error):
    def __init__(self, *, reason: LazyI18nString | None = None, **kwargs: Any) -> None:
        super().__init__(Code.login_required, status=403, reason=reason, details=kwargs)


class ServerError(Error):
    status: int = 500


class Timeout(Error):
    def __init__(self, *, reason: LazyI18nString | None = None) -> None:
        super().__init__(Code.error_504, status=504, reason=reason)


class TemporaryUnavailableError(Error):
    status: int = 500

    def __init__(self, *, reason: LazyI18nString | None = None) -> None:
        super().__init__(Code.temporary_unavailable, reason=reason)


class HTTPException(web.HTTPException):
    def __init__(
        self,
        *,
        status: int,
        content_type: str | None = None,
        charset: str | None = None,
        **kwargs: Any,
    ) -> None:
        self.status_code = status

        headers = kwargs.pop('headers', None) or {}
        if content_type and 'Content-Type' not in headers:
            headers['Content-Type'] = (
                f'{content_type}; charset={charset}' if charset else content_type
            )

        super().__init__(headers=headers, **kwargs)


class EvoPayRequestException(Error):
    pass


class DiiaRequestException(Error):
    pass


class RateLimiterException(Error):
    pass


class GoogleAuthorisationError(Error):
    pass


class MicrosoftAuthorisationError(Error):
    pass


class AppleAuthorisationError(Error):
    pass
