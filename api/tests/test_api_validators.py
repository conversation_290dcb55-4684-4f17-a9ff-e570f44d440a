import pytest

from api.enums import Vendor
from api.errors import Error
from api.validators import validate_token_vendor


@pytest.mark.parametrize('token_vendor, expected', [('API', Vendor.api), ('1C', Vendor.onec)])
def test_validate_token_vendor(token_vendor, expected):
    validate_token_vendor(token_vendor, expected)


@pytest.mark.parametrize(
    'invalid_vendor, expected',
    [(None, Vendor.api), (None, Vendor.onec), ('api', Vendor.api), ('1c', Vendor.onec)],
)
def test_validate_token_vendor_invalid(invalid_vendor, expected):
    with pytest.raises(Error) as err:
        validate_token_vendor(invalid_vendor, expected)
    assert err.value.status == 403
